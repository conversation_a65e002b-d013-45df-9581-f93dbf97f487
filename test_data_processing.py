#!/usr/bin/env python3
"""
测试数据处理
"""

import json
import torch
from PIL import Image
from transformers import AutoProcessor

def test_data_processing():
    print("🧪 测试数据处理...")
    
    # 加载处理器
    try:
        processor = AutoProcessor.from_pretrained(
            "Qwen/Qwen2-VL-2B-Instruct",
            trust_remote_code=True
        )
        print("✅ 处理器加载成功")
    except Exception as e:
        print(f"❌ 处理器加载失败: {e}")
        return
    
    # 读取一条数据
    with open("data/metadata.jsonl", 'r', encoding='utf-8') as f:
        line = f.readline().strip()
        item = json.loads(line)
    
    print(f"📊 数据项: {item}")
    
    # 加载图片
    image_path = f"data/{item['image']}"
    try:
        image = Image.open(image_path).convert('RGB')
        print(f"✅ 图片加载成功: {image.size}")
    except Exception as e:
        print(f"⚠️ 图片加载失败: {e}")
        # 创建测试图片
        image = Image.new('RGB', (224, 224), color='red')
        print("🔄 使用测试图片")
    
    # 测试不同的处理方式
    print("\n🔧 测试方式1: 简单文本+图片")
    try:
        conversations = item['conversations']
        text = ""
        for conv in conversations:
            if conv['from'] == 'human':
                text += f"用户: {conv['value'].replace('<image>', '').strip()}\n"
            else:
                text += f"助手: {conv['value'].strip()}\n"
        
        inputs = processor(
            text=text,
            images=image,
            return_tensors="pt",
            padding=True,
            truncation=True,
            max_length=512
        )
        
        print(f"✅ 方式1成功")
        print(f"  input_ids shape: {inputs['input_ids'].shape}")
        print(f"  attention_mask shape: {inputs['attention_mask'].shape}")
        if 'pixel_values' in inputs:
            print(f"  pixel_values shape: {inputs['pixel_values'].shape}")
        
    except Exception as e:
        print(f"❌ 方式1失败: {e}")
    
    print("\n🔧 测试方式2: Chat template")
    try:
        conversations = item['conversations']
        messages = []
        
        for conv in conversations:
            if conv['from'] == 'human':
                content = conv['value'].replace('<image>\n', '').strip()
                messages.append({
                    "role": "user",
                    "content": [
                        {"type": "image", "image": image},
                        {"type": "text", "text": content}
                    ]
                })
            else:
                messages.append({
                    "role": "assistant",
                    "content": conv['value'].strip()
                })
        
        text = processor.apply_chat_template(
            messages, 
            tokenize=False, 
            add_generation_prompt=False
        )
        print(f"📝 生成的文本: {text[:200]}...")
        
        inputs = processor(
            text=[text],
            images=[image],
            return_tensors="pt",
            padding=True,
            truncation=True,
            max_length=512
        )
        
        print(f"✅ 方式2成功")
        print(f"  input_ids shape: {inputs['input_ids'].shape}")
        print(f"  attention_mask shape: {inputs['attention_mask'].shape}")
        if 'pixel_values' in inputs:
            print(f"  pixel_values shape: {inputs['pixel_values'].shape}")
        
    except Exception as e:
        print(f"❌ 方式2失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n🔧 测试方式3: 最简单方式")
    try:
        # 最简单的方式
        simple_text = "描述这个图片"
        
        inputs = processor(
            text=simple_text,
            images=image,
            return_tensors="pt"
        )
        
        print(f"✅ 方式3成功")
        print(f"  input_ids shape: {inputs['input_ids'].shape}")
        print(f"  attention_mask shape: {inputs['attention_mask'].shape}")
        if 'pixel_values' in inputs:
            print(f"  pixel_values shape: {inputs['pixel_values'].shape}")
        
        # 检查token内容
        decoded = processor.decode(inputs['input_ids'][0], skip_special_tokens=False)
        print(f"  解码文本: {decoded}")
        
    except Exception as e:
        print(f"❌ 方式3失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_data_processing()
