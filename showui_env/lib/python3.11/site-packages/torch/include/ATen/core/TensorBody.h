#pragma once

#ifdef TORCH_ASSERT_NO_OPERATORS
#error This change adds a dependency on native_functions.yaml,            \
  meaning the file will need to be re-compiled every time an operator     \
  is changed or added. Consider if your change would be better placed in  \
  another file, or if a more specific header might achieve the same goal. \
  See NOTE: [Tensor vs. TensorBase]
#endif

#include <c10/core/Device.h>
#include <c10/core/Layout.h>
#include <c10/core/MemoryFormat.h>
#include <c10/core/QScheme.h>
#include <c10/core/Stream.h>
#include <c10/core/Scalar.h>
#include <c10/core/ScalarType.h>
#include <c10/core/ScalarTypeToTypeMeta.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorImpl.h>
#include <c10/core/UndefinedTensorImpl.h>
#include <c10/core/WrapDimMinimal.h>
#include <c10/util/Exception.h>
#include <c10/util/ExclusivelyOwned.h>
#include <c10/util/Deprecated.h>
#include <c10/util/MaybeOwned.h>
#include <optional>
#include <c10/util/OptionalArrayRef.h>
#include <c10/util/intrusive_ptr.h>
#include <c10/macros/Export.h>
#include <ATen/core/CheckMemoryFormat.h>
#include <ATen/core/DeprecatedTypePropertiesRegistry.h>
#include <ATen/core/DeprecatedTypeProperties.h>
#include <ATen/core/NamedTensor.h>
#include <ATen/core/QuantizerBase.h>
#include <c10/core/SymInt.h>
#include <ATen/core/TensorAccessor.h>
#include <ATen/core/TensorBase.h>


#include <ATen/MethodOperators.h>

namespace c10{
template<class T> class List;
template<class T> class IListRef;
}
namespace at {
struct Generator;
struct Type;
class DeprecatedTypeProperties;
class Tensor;
} // namespace at
namespace at {
namespace indexing {
struct TensorIndex;
} // namespace indexing
} // namespace at

namespace torch { namespace autograd {

struct Node;

}} // namespace torch::autograd

namespace at {

class OptionalTensorRef;
class TensorRef;
class Tensor;
using TensorList = ArrayRef<Tensor>;
using ITensorList = c10::IListRef<Tensor>;

using Stream = c10::Stream;

// Tensor is a "generic" object holding a pointer to the underlying TensorImpl object, which
// has an embedded reference count. In this way, Tensor is similar to boost::intrusive_ptr.
//
// For example:
//
// void func(Tensor a) {
//   Tensor b = a;
//   ...
// }
//
// In this example, when we say Tensor b = a, we are creating a new object that points to the
// same underlying TensorImpl, and bumps its reference count. When b goes out of scope, the
// destructor decrements the reference count by calling release() on the TensorImpl it points to.
// The existing constructors, operator overloads, etc. take care to implement the correct semantics.
//
// Note that Tensor can also be NULL, i.e. it is not associated with any underlying TensorImpl, and
// special care must be taken to handle this.
class TORCH_API Tensor: public TensorBase {
 protected:
  // Create a Tensor with a +0 reference count. Special care must be
  // taken to avoid decrementing this reference count at destruction
  // time. Intended to support MaybeOwnedTraits<Tensor>.
  explicit Tensor(unsafe_borrow_t, const TensorBase& rhs): TensorBase(unsafe_borrow_t{}, rhs) {}
  friend MaybeOwnedTraits<Tensor>;
  friend OptionalTensorRef;
  friend TensorRef;

 public:
  Tensor() = default;
  // This constructor should not be used by end users and is an implementation
  // detail invoked by autogenerated code.
  explicit Tensor(
      c10::intrusive_ptr<TensorImpl, UndefinedTensorImpl> tensor_impl)
      : TensorBase(std::move(tensor_impl)) {}
  Tensor(const Tensor &tensor) = default;
  Tensor(Tensor &&tensor) = default;

  // Implicitly move-constructible from TensorBase, but must be explicit to increase refcount
  explicit Tensor(const TensorBase &base): TensorBase(base) {}
  /*implicit*/ Tensor(TensorBase &&base): TensorBase(std::move(base)) {}

  // Creates a new wrapper from TensorImpl. Intentionally a free method because
  // it should be used with care. Checks necessary invariants
  static Tensor wrap_tensor_impl(
      c10::intrusive_ptr<TensorImpl, UndefinedTensorImpl> tensor_impl) {
    return TensorBase::wrap_tensor_impl(std::move(tensor_impl));
  }

  Tensor contiguous(MemoryFormat memory_format=MemoryFormat::Contiguous) const {
    return TensorBase::contiguous(memory_format);
  }

  Tensor conj() const {
    if (!this->is_complex()) {
      return *this;
    }

    switch (this->layout()) {
      case at::kSparse:
      case at::kSparseCsr:
      case at::kSparseCsc:
      case at::kSparseBsr:
      case at::kSparseBsc:
        return this->conj_physical();
      default:
        return this->_conj();
    }
  }

  // Aliased by Dimname overloads, so need explicit using
  using TensorBase::size;
  using TensorBase::sym_size;
  using TensorBase::stride;

  /// Should be used if *this can reasonably be expected to be contiguous and
  /// performance is important.
  /// Compared to contiguous, it saves a reference count
  /// increment/decrement if *this is already contiguous, at the cost
  /// in all cases of an extra pointer of stack usage, an extra branch
  /// to access, and an extra branch at destruction time.
  c10::MaybeOwned<Tensor> expect_contiguous(MemoryFormat memory_format=MemoryFormat::Contiguous) const &;

  // Use .contiguous() instead. Trying to borrow from a prvalue Tensor
  // will only lead to trouble and dangling references.
  c10::MaybeOwned<Tensor> expect_contiguous(MemoryFormat memory_format=MemoryFormat::Contiguous) && = delete;

  // The following overloads are very intruiging.  Consider the following
  // program:
  //
  //    x[1] = 3;
  //
  // We would expect that the first entry of x is written to 3.  But how can we
  // actually achieve this?  x[1] evaluates to a tensor...
  //
  // The answer is, using a ref-qualifier.  x[1] is an rvalue, which cannot be
  // (profitably) assigned to in the traditional sense, so we overload
  // assignment to mean, "Actually, copy 3 into the tensor data."  This is done
  // with an rvalue-reference ref-qualified overload (the methods with && at the
  // end of their type.)
  //
  // There's one more fly in the ointment: We also want
  //
  //    Tensor x = y;
  //
  // to work, and we want it NOT to copy.  So we need a traditional operator=
  // overload.  But we MUST specify a mutable lvalue ref-qualifier, to
  // disambiguate the traditional overload from the rvalue-reference
  // ref-qualified overload.  Otherwise, it will be ambiguous, because
  // a non ref-qualified method is eligible for all situations.

  // Unfortunately, we have to write these constructors out manually
  // to work around an MSVC bug:
  //    error C2580: 'at::Tensor &at::Tensor::operator =(const at::Tensor &) &':
  //    multiple versions of a defaulted special member functions are not allowed
  // Tensor& operator=(const Tensor&) & = default;
  // Tensor& operator=(Tensor&&) & = default;

  // Also MSVC will wrongly issue the following warning with the aforementioned fix
  //    warning C4522: 'at::Tensor': multiple assignment operators specified
  // Let's just skip the warning.
  //
  // TODO: temporarily disabled

  Tensor& operator=(const TensorBase& x) & noexcept {
    impl_ = x.getIntrusivePtr();
    return *this;
  }
  Tensor& operator=(TensorBase&& x) & noexcept {
    impl_ = x.unsafeReleaseIntrusivePtr();
    return *this;
  }

  Tensor& operator=(const Tensor &x) & noexcept {
    return operator=(static_cast<const TensorBase&>(x));
  }
  Tensor& operator=(Tensor &&x) & noexcept {
    return operator=(static_cast<TensorBase&&>(x));
  }

  Tensor& operator=(const Scalar &v) && {
    return fill_(v);
  }
  Tensor& operator=(const Tensor &rhs) && {
    return copy_(rhs);
  }
  Tensor& operator=(Tensor&& rhs) && {
    return copy_(rhs);
  }

  C10_DEPRECATED_MESSAGE("Tensor.type() is deprecated. Instead use Tensor.options(), which in many cases (e.g. in a constructor) is a drop-in replacement. If you were using data from type(), that is now available from Tensor itself, so instead of tensor.type().scalar_type(), use tensor.scalar_type() instead and instead of tensor.type().backend() use tensor.device().")
  DeprecatedTypeProperties & type() const {
    return globalDeprecatedTypePropertiesRegistry().getDeprecatedTypeProperties(
        dispatchKeyToBackend(legacyExtractDispatchKey(key_set())),
        scalar_type());
  }

  Tensor toType(ScalarType t) const {
    return to(options().dtype(t), /*non_blocking*/ false, /*copy*/ false);
  }

  // TODO: Deprecate me
  Tensor toBackend(Backend b) const {
    return to(options().device(backendToDeviceType(b)).layout(layout_from_backend(b)), /*non_blocking*/ false, /*copy*/ false);
  }

  C10_DEPRECATED_MESSAGE("Tensor.is_variable() is deprecated; everything is a variable now. (If you want to assert that variable has been appropriately handled already, use at::impl::variable_excluded_from_dispatch())")
  bool is_variable() const noexcept {
    return !at::impl::variable_excluded_from_dispatch();
  }

  template<typename T>
  C10_DEPRECATED_MESSAGE("Tensor.data<T>() is deprecated. Please use Tensor.data_ptr<T>() instead.")
  T * data() const {
    return data_ptr<T>();
  }

  template <typename T>
  T item() const;

  template<typename T, size_t N, template <typename U> class PtrTraits = DefaultPtrTraits, typename index_t = int64_t>
  C10_DEPRECATED_MESSAGE("packed_accessor is deprecated, use packed_accessor32 or packed_accessor64 instead")
  GenericPackedTensorAccessor<T,N,PtrTraits,index_t> packed_accessor() const & {
    return generic_packed_accessor<T,N,PtrTraits,index_t>();
  }
  template<typename T, size_t N, template <typename U> class PtrTraits = DefaultPtrTraits, typename index_t = int64_t>
  C10_DEPRECATED_MESSAGE("packed_accessor is deprecated, use packed_accessor32 or packed_accessor64 instead")
  GenericPackedTensorAccessor<T,N,PtrTraits,index_t> packed_accessor() && = delete;

  Tensor operator~() const {
    return bitwise_not();
  }
  Tensor operator-() const {
    return neg();
  }
  Tensor& operator+=(const Tensor & other) {
    return add_(other);
  }
  Tensor& operator+=(const Scalar & other) {
    return add_(other);
  }
  Tensor& operator-=(const Tensor & other) {
    return sub_(other);
  }
  Tensor& operator-=(const Scalar & other) {
    return sub_(other);
  }
  Tensor& operator*=(const Tensor & other) {
    return mul_(other);
  }
  Tensor& operator*=(const Scalar & other) {
    return mul_(other);
  }
  Tensor& operator/=(const Tensor & other) {
    return div_(other);
  }
  Tensor& operator/=(const Scalar & other) {
    return div_(other);
  }
  Tensor& operator&=(const Tensor & other) {
    return bitwise_and_(other);
  }
  Tensor& operator|=(const Tensor & other) {
    return bitwise_or_(other);
  }
  Tensor& operator^=(const Tensor & other) {
    return bitwise_xor_(other);
  }
  Tensor operator[](const Scalar & index) const {
    if (!index.isIntegral(false)) {
      TORCH_CHECK_INDEX(false, "Can only index tensors with integral scalars");
    }
    return this->operator[](index.toLong());
  }
  Tensor operator[](const Tensor & index) const {
    // These properties are checked in the Scalar constructor, but we already
    // check them here to provide more useful diagnostics for the user.
    if (!index.defined()) {
      TORCH_CHECK_INDEX(false, "Can only index with tensors that are defined");
    }
    if (index.dim() != 0) {
      TORCH_CHECK_INDEX(false,
                        "Can only index with tensors that are scalars (zero-dim)");
    }
    // The Scalar(Tensor) constructor is explicit, so we need to call it.
    return this->operator[](index.item());
  }
  Tensor operator[](int64_t index) const {
    return select(0, index);
  }

  Tensor index(ArrayRef<at::indexing::TensorIndex> indices) const;
  Tensor index(std::initializer_list<at::indexing::TensorIndex> indices) const;

  Tensor & index_put_(ArrayRef<at::indexing::TensorIndex> indices, Tensor const & rhs);
  Tensor & index_put_(ArrayRef<at::indexing::TensorIndex> indices, const Scalar& v);
  Tensor & index_put_(std::initializer_list<at::indexing::TensorIndex> indices, Tensor const & rhs);
  Tensor & index_put_(std::initializer_list<at::indexing::TensorIndex> indices, const Scalar& v);

  Tensor cpu() const {
    return to(options().device(c10::DeviceType::CPU), /*non_blocking*/ false, /*copy*/ false);
  }

  // TODO: The Python version also accepts arguments
  Tensor cuda() const {
    return to(options().device(c10::DeviceType::CUDA), /*non_blocking*/ false, /*copy*/ false);
  }

  Tensor hip() const {
    return to(options().device(c10::DeviceType::HIP), /*non_blocking*/ false, /*copy*/ false);
  }

  Tensor ve() const {
    return to(options().device(c10::DeviceType::VE), /*non_blocking*/ false, /*copy*/ false);
  }

  Tensor vulkan() const {
    return to(options().device(c10::DeviceType::Vulkan), /*non_blocking*/ false, /*copy*/ false);
  }

  Tensor metal() const {
    return to(options().device(c10::DeviceType::Metal), /*non_blocking*/ false, /*copy*/ false);
  }

  Tensor meta() const {
    return to(options().device(c10::DeviceType::Meta), /*non_blocking*/ false, /*copy*/ false);
  }

  // ~~~~~ Autograd API ~~~~~

  /// \fn bool is_leaf() const;
  ///
  /// All Tensors that have `requires_grad()` which is ``false`` will be leaf Tensors by convention.
  ///
  /// For Tensors that have `requires_grad()` which is ``true``, they will be leaf Tensors if they were
  /// created by the user. This means that they are not the result of an operation and so
  /// `grad_fn()` is `nullptr`.
  ///
  /// Only leaf Tensors will have their `grad()` populated during a call to `backward()`.
  /// To get `grad()` populated for non-leaf Tensors, you can use `retain_grad()`.
  ///
  /// Example:
  /// @code
  /// auto a = torch::rand(10, torch::requires_grad());
  /// std::cout << a.is_leaf() << std::endl; // prints `true`
  ///
  /// auto b = torch::rand(10, torch::requires_grad()).to(torch::kCUDA);
  /// std::cout << b.is_leaf() << std::endl; // prints `false`
  /// // b was created by the operation that cast a cpu Tensor into a cuda Tensor
  ///
  /// auto c = torch::rand(10, torch::requires_grad()) + 2;
  /// std::cout << c.is_leaf() << std::endl; // prints `false`
  /// // c was created by the addition operation
  ///
  /// auto d = torch::rand(10).cuda();
  /// std::cout << d.is_leaf() << std::endl; // prints `true`
  /// // d does not require gradients and so has no operation creating it (that is tracked by the autograd engine)
  ///
  /// auto e = torch::rand(10).cuda().requires_grad_();
  /// std::cout << e.is_leaf() << std::endl; // prints `true`
  /// // e requires gradients and has no operations creating it
  ///
  /// auto f = torch::rand(10, torch::device(torch::kCUDA).requires_grad(true));
  /// std::cout << f.is_leaf() << std::endl; // prints `true`
  /// // f requires grad, has no operation creating it
  /// @endcode

  /// \fn void backward(const Tensor & gradient={}, std::optional<bool> retain_graph=std::nullopt, bool create_graph=false, std::optional<TensorList> inputs=std::nullopt) const;
  ///
  /// Computes the gradient of current tensor with respect to graph leaves.
  ///
  /// The graph is differentiated using the chain rule. If the tensor is
  /// non-scalar (i.e. its data has more than one element) and requires
  /// gradient, the function additionally requires specifying ``gradient``.
  /// It should be a tensor of matching type and location, that contains
  /// the gradient of the differentiated function w.r.t. this Tensor.
  ///
  /// This function accumulates gradients in the leaves - you might need to
  /// zero them before calling it.
  ///
  /// \param gradient Gradient w.r.t. the
  ///     tensor. If it is a tensor, it will be automatically converted
  ///     to a Tensor that does not require grad unless ``create_graph`` is True.
  ///     None values can be specified for scalar Tensors or ones that
  ///     don't require grad. If a None value would be acceptable then
  ///     this argument is optional.
  /// \param retain_graph If ``false``, the graph used to compute
  ///     the grads will be freed. Note that in nearly all cases setting
  ///     this option to True is not needed and often can be worked around
  ///     in a much more efficient way. Defaults to the value of
  ///     ``create_graph``.
  /// \param create_graph If ``true``, graph of the derivative will
  ///     be constructed, allowing to compute higher order derivative
  ///     products. Defaults to ``false``.
  /// \param inputs Inputs w.r.t. which the gradient will be accumulated into
  ///     ``at::Tensor::grad``. All other Tensors will be ignored. If not
  ///     provided, the gradient is accumulated into all the leaf Tensors
  ///     that were used to compute the current tensor.
  ///     When inputs are provided and a given input is not a leaf,
  ///     the current implementation will call its grad_fn (even though it is not strictly needed to get this gradients).
  ///     It is an implementation detail on which the user should not rely.
  ///     See https://github.com/pytorch/pytorch/pull/60521#issuecomment-867061780 for more details.
  void backward(const Tensor & gradient={}, std::optional<bool> retain_graph=std::nullopt, bool create_graph=false, std::optional<TensorList> inputs=std::nullopt) const {
    // NB: Adding this wrapper to _backward here because we'd like our
    // 'backwards' api to accept the 'inputs' argument optionally. Since code gen
    // currently does not support optional of TensorList our approach is to replace
    // backward in native_functions.yaml with _backward and call it here instead.
    if (inputs.has_value()) {
      TORCH_CHECK(inputs.value().size() > 0, "'inputs' argument to backward cannot be empty")
      this->_backward(inputs.value(), gradient, retain_graph, create_graph);
    } else {
      this->_backward({}, gradient, retain_graph, create_graph);
    }
  }

  /// \fn Tensor detach() const;
  ///
  /// Returns a new Tensor, detached from the current graph.
  /// The result will never require gradient.

  /// \fn Tensor & detach_() const;
  ///
  /// Detaches the Tensor from the graph that created it, making it a leaf.
  /// Views cannot be detached in-place.

  /// \fn void retain_grad() const;
  ///
  /// Enables this Tensor to have their :attr:`grad` populated during
  /// :func:`backward`. This is a no-op for leaf tensors.

  /// \fn bool retains_grad() const;
  ///
  /// Is ``true`` if this Tensor is non-leaf and its :attr:`grad` is enabled to be
  /// populated during :func:`backward`, ``false`` otherwise.

  const Tensor& set_requires_grad(bool requires_grad) const {
    TensorBase::set_requires_grad(requires_grad);
    return *this;
  }

  /// Return a mutable reference to the gradient. This is conventionally
  /// used as `t.grad() = x` to set a gradient to a completely new tensor.
  /// Note that this function work with a non-const Tensor and is not
  /// thread safe.
  Tensor& mutable_grad() const {
    return impl_->mutable_grad();
  }

  /// This function returns an undefined tensor by default and returns a defined tensor
  /// the first time a call to `backward()` computes gradients for this Tensor.
  /// The attribute will then contain the gradients computed and future calls
  /// to `backward()` will accumulate (add) gradients into it.
  const Tensor& grad() const {
    const Tensor& maybe_grad = impl_->grad();
    if (!is_leaf() && !retains_grad() && !maybe_grad.defined()) {
      TORCH_WARN(
        "The .grad attribute of a Tensor that is not a leaf Tensor is being accessed. Its .grad "
        "attribute won't be populated during autograd.backward(). If you indeed want the .grad "
        "field to be populated for a non-leaf Tensor, use .retain_grad() on the non-leaf Tensor. "
        "If you access the non-leaf Tensor by mistake, make sure you access the leaf Tensor "
        "instead. See github.com/pytorch/pytorch/pull/30531 for more informations.");
    }
    return maybe_grad;
  }

  // The Forward AD API functions below are low level and are not to be used by end
  // users who should use the API provided in torch/csrc/autograd.h

  /// This function returns the forward gradient for this Tensor at the given level.
  const Tensor& _fw_grad(uint64_t level) const {
    return impl_->_fw_grad(level, *this);
  }

  /// This function can be used to set the value of the forward grad.
  /// Note that the given new_grad might not be used directly if it has different
  /// metadata (size/stride/storage offset) compared to this Tensor. In that case,
  /// new_grad content will be copied into a new Tensor
  void _set_fw_grad(const TensorBase& new_grad, uint64_t level, bool is_inplace_op) const {
    impl_->_set_fw_grad(new_grad, *this, level, is_inplace_op);
  }


  // STOP.  Thinking of adding a method here, which only makes use
  // of other ATen methods?  Define it in native_functions.yaml.

  //example
  //Tensor * add(Tensor & b);
  void __dispatch__backward(at::TensorList inputs, const ::std::optional<at::Tensor> & gradient={}, ::std::optional<bool> retain_graph=::std::nullopt, bool create_graph=false) const;
  void __dispatch_set_data(const at::Tensor & new_data) const;
  at::Tensor __dispatch_data() const;
  bool __dispatch_is_leaf() const;
  int64_t __dispatch_output_nr() const;
  int64_t __dispatch__version() const;
  at::Tensor & __dispatch_requires_grad_(bool requires_grad=true) const;
  void __dispatch_retain_grad() const;
  bool __dispatch_retains_grad() const;
  at::Tensor _fw_primal(int64_t level) const;
  at::Tensor & rename_(::std::optional<at::DimnameList> names) const;
  at::Tensor rename(::std::optional<at::DimnameList> names) const;
  at::Tensor align_to(at::DimnameList names) const;
  at::Tensor align_to(at::DimnameList order, int64_t ellipsis_idx) const;
  at::Tensor align_as(const at::Tensor & other) const;
  at::Tensor refine_names(at::DimnameList names) const;
  at::Tensor abs() const;
  at::Tensor & abs_() const;
  at::Tensor absolute() const;
  at::Tensor & absolute_() const;
  at::Tensor angle() const;
  at::Tensor sgn() const;
  at::Tensor & sgn_() const;
  at::Tensor chalf(::std::optional<at::MemoryFormat> memory_format=::std::nullopt) const;
  at::Tensor _conj() const;
  at::Tensor __dispatch_conj() const;
  at::Tensor _conj_physical() const;
  at::Tensor conj_physical() const;
  at::Tensor & conj_physical_() const;
  at::Tensor resolve_conj() const;
  at::Tensor resolve_neg() const;
  at::Tensor _neg_view() const;
  at::Tensor acos() const;
  at::Tensor & acos_() const;
  at::Tensor arccos() const;
  at::Tensor & arccos_() const;
  at::Tensor add(const at::Tensor & other, const at::Scalar & alpha=1) const;
  at::Tensor & add_(const at::Tensor & other, const at::Scalar & alpha=1) const;
  at::Tensor add(const at::Scalar & other, const at::Scalar & alpha=1) const;
  at::Tensor & add_(const at::Scalar & other, const at::Scalar & alpha=1) const;
  at::Tensor addmv(const at::Tensor & mat, const at::Tensor & vec, const at::Scalar & beta=1, const at::Scalar & alpha=1) const;
  at::Tensor & addmv_(const at::Tensor & mat, const at::Tensor & vec, const at::Scalar & beta=1, const at::Scalar & alpha=1) const;
  at::Tensor addr(const at::Tensor & vec1, const at::Tensor & vec2, const at::Scalar & beta=1, const at::Scalar & alpha=1) const;
  at::Tensor & addr_(const at::Tensor & vec1, const at::Tensor & vec2, const at::Scalar & beta=1, const at::Scalar & alpha=1) const;
  at::Tensor _is_all_true() const;
  at::Tensor _is_any_true() const;
  at::Tensor all(int64_t dim, bool keepdim=false) const;
  at::Tensor all(at::OptionalIntArrayRef dim, bool keepdim=false) const;
  at::Tensor all(at::Dimname dim, bool keepdim=false) const;
  bool allclose(const at::Tensor & other, double rtol=1e-05, double atol=1e-08, bool equal_nan=false) const;
  at::Tensor any(int64_t dim, bool keepdim=false) const;
  at::Tensor any(at::OptionalIntArrayRef dim, bool keepdim=false) const;
  at::Tensor any(at::Dimname dim, bool keepdim=false) const;
  at::Tensor argmax(::std::optional<int64_t> dim=::std::nullopt, bool keepdim=false) const;
  at::Tensor argmin(::std::optional<int64_t> dim=::std::nullopt, bool keepdim=false) const;
  at::Tensor acosh() const;
  at::Tensor & acosh_() const;
  at::Tensor arccosh() const;
  at::Tensor & arccosh_() const;
  at::Tensor asinh() const;
  at::Tensor & asinh_() const;
  at::Tensor arcsinh() const;
  at::Tensor & arcsinh_() const;
  at::Tensor atanh() const;
  at::Tensor & atanh_() const;
  at::Tensor arctanh() const;
  at::Tensor & arctanh_() const;
  at::Tensor as_strided(at::IntArrayRef size, at::IntArrayRef stride, ::std::optional<int64_t> storage_offset=::std::nullopt) const;
  at::Tensor as_strided_symint(c10::SymIntArrayRef size, c10::SymIntArrayRef stride, ::std::optional<c10::SymInt> storage_offset=::std::nullopt) const;
  const at::Tensor & as_strided_(at::IntArrayRef size, at::IntArrayRef stride, ::std::optional<int64_t> storage_offset=::std::nullopt) const;
  const at::Tensor & as_strided__symint(c10::SymIntArrayRef size, c10::SymIntArrayRef stride, ::std::optional<c10::SymInt> storage_offset=::std::nullopt) const;
  at::Tensor asin() const;
  at::Tensor & asin_() const;
  at::Tensor arcsin() const;
  at::Tensor & arcsin_() const;
  at::Tensor atan() const;
  at::Tensor & atan_() const;
  at::Tensor arctan() const;
  at::Tensor & arctan_() const;
  at::Tensor baddbmm(const at::Tensor & batch1, const at::Tensor & batch2, const at::Scalar & beta=1, const at::Scalar & alpha=1) const;
  at::Tensor & baddbmm_(const at::Tensor & batch1, const at::Tensor & batch2, const at::Scalar & beta=1, const at::Scalar & alpha=1) const;
  at::Tensor bernoulli(::std::optional<at::Generator> generator=::std::nullopt) const;
  at::Tensor & bernoulli_(const at::Tensor & p, ::std::optional<at::Generator> generator=::std::nullopt) const;
  at::Tensor & bernoulli_(double p=0.5, ::std::optional<at::Generator> generator=::std::nullopt) const;
  at::Tensor bernoulli(double p, ::std::optional<at::Generator> generator=::std::nullopt) const;
  at::Tensor bincount(const ::std::optional<at::Tensor> & weights={}, int64_t minlength=0) const;
  at::Tensor bitwise_not() const;
  at::Tensor & bitwise_not_() const;
  at::Tensor copysign(const at::Tensor & other) const;
  at::Tensor & copysign_(const at::Tensor & other) const;
  at::Tensor copysign(const at::Scalar & other) const;
  at::Tensor & copysign_(const at::Scalar & other) const;
  at::Tensor _lazy_clone() const;
  at::Tensor logical_not() const;
  at::Tensor & logical_not_() const;
  at::Tensor logical_xor(const at::Tensor & other) const;
  at::Tensor & logical_xor_(const at::Tensor & other) const;
  at::Tensor logical_and(const at::Tensor & other) const;
  at::Tensor & logical_and_(const at::Tensor & other) const;
  at::Tensor logical_or(const at::Tensor & other) const;
  at::Tensor & logical_or_(const at::Tensor & other) const;
  at::Tensor bmm(const at::Tensor & mat2) const;
  at::Tensor broadcast_to(at::IntArrayRef size) const;
  at::Tensor broadcast_to_symint(c10::SymIntArrayRef size) const;
  at::Tensor ceil() const;
  at::Tensor & ceil_() const;
  ::std::vector<at::Tensor> unsafe_chunk(int64_t chunks, int64_t dim=0) const;
  ::std::vector<at::Tensor> chunk(int64_t chunks, int64_t dim=0) const;
  ::std::vector<at::Tensor> tensor_split(int64_t sections, int64_t dim=0) const;
  ::std::vector<at::Tensor> tensor_split_symint(c10::SymInt sections, int64_t dim=0) const;
  ::std::vector<at::Tensor> tensor_split(at::IntArrayRef indices, int64_t dim=0) const;
  ::std::vector<at::Tensor> tensor_split_symint(c10::SymIntArrayRef indices, int64_t dim=0) const;
  ::std::vector<at::Tensor> tensor_split(const at::Tensor & tensor_indices_or_sections, int64_t dim=0) const;
  at::Tensor clamp(const ::std::optional<at::Scalar> & min, const ::std::optional<at::Scalar> & max=::std::nullopt) const;
  at::Tensor clamp(const ::std::optional<at::Tensor> & min={}, const ::std::optional<at::Tensor> & max={}) const;
  at::Tensor & clamp_(const ::std::optional<at::Scalar> & min, const ::std::optional<at::Scalar> & max=::std::nullopt) const;
  at::Tensor & clamp_(const ::std::optional<at::Tensor> & min={}, const ::std::optional<at::Tensor> & max={}) const;
  at::Tensor clamp_max(const at::Scalar & max) const;
  at::Tensor clamp_max(const at::Tensor & max) const;
  at::Tensor & clamp_max_(const at::Scalar & max) const;
  at::Tensor & clamp_max_(const at::Tensor & max) const;
  at::Tensor clamp_min(const at::Scalar & min) const;
  at::Tensor clamp_min(const at::Tensor & min) const;
  at::Tensor & clamp_min_(const at::Scalar & min) const;
  at::Tensor & clamp_min_(const at::Tensor & min) const;
  at::Tensor clip(const ::std::optional<at::Scalar> & min, const ::std::optional<at::Scalar> & max=::std::nullopt) const;
  at::Tensor clip(const ::std::optional<at::Tensor> & min={}, const ::std::optional<at::Tensor> & max={}) const;
  at::Tensor & clip_(const ::std::optional<at::Scalar> & min, const ::std::optional<at::Scalar> & max=::std::nullopt) const;
  at::Tensor & clip_(const ::std::optional<at::Tensor> & min={}, const ::std::optional<at::Tensor> & max={}) const;
  at::Tensor __dispatch_contiguous(at::MemoryFormat memory_format=c10::MemoryFormat::Contiguous) const;
  at::Tensor & copy_(const at::Tensor & src, bool non_blocking=false) const;
  at::Tensor cos() const;
  at::Tensor & cos_() const;
  at::Tensor cosh() const;
  at::Tensor & cosh_() const;
  at::Tensor count_nonzero(at::IntArrayRef dim) const;
  at::Tensor count_nonzero(::std::optional<int64_t> dim=::std::nullopt) const;
  at::Tensor cov(int64_t correction=1, const ::std::optional<at::Tensor> & fweights={}, const ::std::optional<at::Tensor> & aweights={}) const;
  at::Tensor corrcoef() const;
  ::std::tuple<at::Tensor,at::Tensor> cummax(int64_t dim) const;
  ::std::tuple<at::Tensor,at::Tensor> cummax(at::Dimname dim) const;
  ::std::tuple<at::Tensor,at::Tensor> cummin(int64_t dim) const;
  ::std::tuple<at::Tensor,at::Tensor> cummin(at::Dimname dim) const;
  at::Tensor cumprod(int64_t dim, ::std::optional<at::ScalarType> dtype=::std::nullopt) const;
  at::Tensor & cumprod_(int64_t dim, ::std::optional<at::ScalarType> dtype=::std::nullopt) const;
  at::Tensor cumprod(at::Dimname dim, ::std::optional<at::ScalarType> dtype=::std::nullopt) const;
  at::Tensor & cumprod_(at::Dimname dim, ::std::optional<at::ScalarType> dtype=::std::nullopt) const;
  at::Tensor cumsum(int64_t dim, ::std::optional<at::ScalarType> dtype=::std::nullopt) const;
  at::Tensor & cumsum_(int64_t dim, ::std::optional<at::ScalarType> dtype=::std::nullopt) const;
  at::Tensor cumsum(at::Dimname dim, ::std::optional<at::ScalarType> dtype=::std::nullopt) const;
  at::Tensor & cumsum_(at::Dimname dim, ::std::optional<at::ScalarType> dtype=::std::nullopt) const;
  at::Tensor diag_embed(int64_t offset=0, int64_t dim1=-2, int64_t dim2=-1) const;
  at::Tensor diagflat(int64_t offset=0) const;
  at::Tensor diagonal(int64_t offset=0, int64_t dim1=0, int64_t dim2=1) const;
  at::Tensor diagonal(at::Dimname outdim, at::Dimname dim1, at::Dimname dim2, int64_t offset=0) const;
  at::Tensor & fill_diagonal_(const at::Scalar & fill_value, bool wrap=false) const;
  at::Tensor diff(int64_t n=1, int64_t dim=-1, const ::std::optional<at::Tensor> & prepend={}, const ::std::optional<at::Tensor> & append={}) const;
  at::Tensor div(const at::Tensor & other) const;
  at::Tensor & div_(const at::Tensor & other) const;
  at::Tensor div(const at::Tensor & other, ::std::optional<c10::string_view> rounding_mode) const;
  at::Tensor & div_(const at::Tensor & other, ::std::optional<c10::string_view> rounding_mode) const;
  at::Tensor div(const at::Scalar & other) const;
  at::Tensor & div_(const at::Scalar & other) const;
  at::Tensor div(const at::Scalar & other, ::std::optional<c10::string_view> rounding_mode) const;
  at::Tensor & div_(const at::Scalar & other, ::std::optional<c10::string_view> rounding_mode) const;
  at::Tensor divide(const at::Tensor & other) const;
  at::Tensor & divide_(const at::Tensor & other) const;
  at::Tensor divide(const at::Scalar & other) const;
  at::Tensor & divide_(const at::Scalar & other) const;
  at::Tensor divide(const at::Tensor & other, ::std::optional<c10::string_view> rounding_mode) const;
  at::Tensor & divide_(const at::Tensor & other, ::std::optional<c10::string_view> rounding_mode) const;
  at::Tensor divide(const at::Scalar & other, ::std::optional<c10::string_view> rounding_mode) const;
  at::Tensor & divide_(const at::Scalar & other, ::std::optional<c10::string_view> rounding_mode) const;
  at::Tensor true_divide(const at::Tensor & other) const;
  at::Tensor & true_divide_(const at::Tensor & other) const;
  at::Tensor true_divide(const at::Scalar & other) const;
  at::Tensor & true_divide_(const at::Scalar & other) const;
  at::Tensor dot(const at::Tensor & tensor) const;
  at::Tensor vdot(const at::Tensor & other) const;
  at::Tensor new_empty(at::IntArrayRef size, at::TensorOptions options={}) const;
  at::Tensor new_empty(at::IntArrayRef size, ::std::optional<at::ScalarType> dtype, ::std::optional<at::Layout> layout, ::std::optional<at::Device> device, ::std::optional<bool> pin_memory) const;
  at::Tensor new_empty_symint(c10::SymIntArrayRef size, at::TensorOptions options={}) const;
  at::Tensor new_empty_symint(c10::SymIntArrayRef size, ::std::optional<at::ScalarType> dtype, ::std::optional<at::Layout> layout, ::std::optional<at::Device> device, ::std::optional<bool> pin_memory) const;
  at::Tensor new_empty_strided(at::IntArrayRef size, at::IntArrayRef stride, at::TensorOptions options={}) const;
  at::Tensor new_empty_strided(at::IntArrayRef size, at::IntArrayRef stride, ::std::optional<at::ScalarType> dtype, ::std::optional<at::Layout> layout, ::std::optional<at::Device> device, ::std::optional<bool> pin_memory) const;
  at::Tensor new_empty_strided_symint(c10::SymIntArrayRef size, c10::SymIntArrayRef stride, at::TensorOptions options={}) const;
  at::Tensor new_empty_strided_symint(c10::SymIntArrayRef size, c10::SymIntArrayRef stride, ::std::optional<at::ScalarType> dtype, ::std::optional<at::Layout> layout, ::std::optional<at::Device> device, ::std::optional<bool> pin_memory) const;
  at::Tensor new_full(at::IntArrayRef size, const at::Scalar & fill_value, at::TensorOptions options={}) const;
  at::Tensor new_full(at::IntArrayRef size, const at::Scalar & fill_value, ::std::optional<at::ScalarType> dtype, ::std::optional<at::Layout> layout, ::std::optional<at::Device> device, ::std::optional<bool> pin_memory) const;
  at::Tensor new_full_symint(c10::SymIntArrayRef size, const at::Scalar & fill_value, at::TensorOptions options={}) const;
  at::Tensor new_full_symint(c10::SymIntArrayRef size, const at::Scalar & fill_value, ::std::optional<at::ScalarType> dtype, ::std::optional<at::Layout> layout, ::std::optional<at::Device> device, ::std::optional<bool> pin_memory) const;
  at::Tensor new_zeros(at::IntArrayRef size, at::TensorOptions options={}) const;
  at::Tensor new_zeros(at::IntArrayRef size, ::std::optional<at::ScalarType> dtype, ::std::optional<at::Layout> layout, ::std::optional<at::Device> device, ::std::optional<bool> pin_memory) const;
  at::Tensor new_zeros_symint(c10::SymIntArrayRef size, at::TensorOptions options={}) const;
  at::Tensor new_zeros_symint(c10::SymIntArrayRef size, ::std::optional<at::ScalarType> dtype, ::std::optional<at::Layout> layout, ::std::optional<at::Device> device, ::std::optional<bool> pin_memory) const;
  at::Tensor new_ones(at::IntArrayRef size, at::TensorOptions options={}) const;
  at::Tensor new_ones(at::IntArrayRef size, ::std::optional<at::ScalarType> dtype, ::std::optional<at::Layout> layout, ::std::optional<at::Device> device, ::std::optional<bool> pin_memory) const;
  at::Tensor new_ones_symint(c10::SymIntArrayRef size, at::TensorOptions options={}) const;
  at::Tensor new_ones_symint(c10::SymIntArrayRef size, ::std::optional<at::ScalarType> dtype, ::std::optional<at::Layout> layout, ::std::optional<at::Device> device, ::std::optional<bool> pin_memory) const;
  const at::Tensor & resize_(at::IntArrayRef size, ::std::optional<at::MemoryFormat> memory_format=::std::nullopt) const;
  const at::Tensor & resize__symint(c10::SymIntArrayRef size, ::std::optional<at::MemoryFormat> memory_format=::std::nullopt) const;
  at::Tensor erf() const;
  at::Tensor & erf_() const;
  at::Tensor erfc() const;
  at::Tensor & erfc_() const;
  at::Tensor exp() const;
  at::Tensor & exp_() const;
  at::Tensor exp2() const;
  at::Tensor & exp2_() const;
  at::Tensor expm1() const;
  at::Tensor & expm1_() const;
  at::Tensor expand(at::IntArrayRef size, bool implicit=false) const;
  at::Tensor expand_symint(c10::SymIntArrayRef size, bool implicit=false) const;
  at::Tensor expand_as(const at::Tensor & other) const;
  at::Tensor flatten(int64_t start_dim=0, int64_t end_dim=-1) const;
  at::Tensor flatten(int64_t start_dim, int64_t end_dim, at::Dimname out_dim) const;
  at::Tensor flatten(at::Dimname start_dim, at::Dimname end_dim, at::Dimname out_dim) const;
  at::Tensor flatten(at::DimnameList dims, at::Dimname out_dim) const;
  at::Tensor unflatten(int64_t dim, at::IntArrayRef sizes) const;
  at::Tensor unflatten_symint(int64_t dim, c10::SymIntArrayRef sizes) const;
  at::Tensor unflatten(at::Dimname dim, at::IntArrayRef sizes, at::DimnameList names) const;
  at::Tensor unflatten_symint(at::Dimname dim, c10::SymIntArrayRef sizes, at::DimnameList names) const;
  at::Tensor & fill_(const at::Scalar & value) const;
  at::Tensor & fill_(const at::Tensor & value) const;
  at::Tensor floor() const;
  at::Tensor & floor_() const;
  at::Tensor floor_divide(const at::Tensor & other) const;
  at::Tensor & floor_divide_(const at::Tensor & other) const;
  at::Tensor floor_divide(const at::Scalar & other) const;
  at::Tensor & floor_divide_(const at::Scalar & other) const;
  at::Tensor frac() const;
  at::Tensor & frac_() const;
  at::Tensor gcd(const at::Tensor & other) const;
  at::Tensor & gcd_(const at::Tensor & other) const;
  at::Tensor lcm(const at::Tensor & other) const;
  at::Tensor & lcm_(const at::Tensor & other) const;
  at::Tensor index(const c10::List<::std::optional<at::Tensor>> & indices) const;
  at::Tensor & index_copy_(int64_t dim, const at::Tensor & index, const at::Tensor & source) const;
  at::Tensor index_copy(int64_t dim, const at::Tensor & index, const at::Tensor & source) const;
  at::Tensor & index_copy_(at::Dimname dim, const at::Tensor & index, const at::Tensor & source) const;
  at::Tensor index_copy(at::Dimname dim, const at::Tensor & index, const at::Tensor & source) const;
  at::Tensor & index_put_(const c10::List<::std::optional<at::Tensor>> & indices, const at::Tensor & values, bool accumulate=false) const;
  at::Tensor index_put(const c10::List<::std::optional<at::Tensor>> & indices, const at::Tensor & values, bool accumulate=false) const;
  at::Tensor isclose(const at::Tensor & other, double rtol=1e-05, double atol=1e-08, bool equal_nan=false) const;
  at::Tensor isnan() const;
  bool is_distributed() const;
  bool __dispatch_is_floating_point() const;
  bool __dispatch_is_complex() const;
  bool __dispatch_is_conj() const;
  bool __dispatch__is_zerotensor() const;
  bool __dispatch_is_neg() const;
  at::Tensor isreal() const;
  bool is_nonzero() const;
  bool is_same_size(const at::Tensor & other) const;
  bool __dispatch_is_signed() const;
  bool __dispatch_is_inference() const;
  at::Tensor kron(const at::Tensor & other) const;
  ::std::tuple<at::Tensor,at::Tensor> kthvalue(int64_t k, int64_t dim=-1, bool keepdim=false) const;
  ::std::tuple<at::Tensor,at::Tensor> kthvalue(int64_t k, at::Dimname dim, bool keepdim=false) const;
  at::Tensor nan_to_num(::std::optional<double> nan=::std::nullopt, ::std::optional<double> posinf=::std::nullopt, ::std::optional<double> neginf=::std::nullopt) const;
  at::Tensor & nan_to_num_(::std::optional<double> nan=::std::nullopt, ::std::optional<double> posinf=::std::nullopt, ::std::optional<double> neginf=::std::nullopt) const;
  at::Tensor ldexp(const at::Tensor & other) const;
  at::Tensor & ldexp_(const at::Tensor & other) const;
  at::Tensor log() const;
  at::Tensor & log_() const;
  at::Tensor log10() const;
  at::Tensor & log10_() const;
  at::Tensor log1p() const;
  at::Tensor & log1p_() const;
  at::Tensor log2() const;
  at::Tensor & log2_() const;
  at::Tensor logaddexp(const at::Tensor & other) const;
  at::Tensor logaddexp2(const at::Tensor & other) const;
  at::Tensor xlogy(const at::Tensor & other) const;
  at::Tensor xlogy(const at::Scalar & other) const;
  at::Tensor & xlogy_(const at::Tensor & other) const;
  at::Tensor & xlogy_(const at::Scalar & other) const;
  at::Tensor log_softmax(int64_t dim, ::std::optional<at::ScalarType> dtype=::std::nullopt) const;
  at::Tensor log_softmax(at::Dimname dim, ::std::optional<at::ScalarType> dtype=::std::nullopt) const;
  at::Tensor logcumsumexp(int64_t dim) const;
  at::Tensor logcumsumexp(at::Dimname dim) const;
  at::Tensor logsumexp(at::IntArrayRef dim, bool keepdim=false) const;
  at::Tensor logsumexp(at::DimnameList dim, bool keepdim=false) const;
  at::Tensor matmul(const at::Tensor & other) const;
  at::Tensor matrix_power(int64_t n) const;
  at::Tensor matrix_exp() const;
  ::std::tuple<at::Tensor,at::Tensor> aminmax(::std::optional<int64_t> dim=::std::nullopt, bool keepdim=false) const;
  ::std::tuple<at::Tensor,at::Tensor> max(int64_t dim, bool keepdim=false) const;
  ::std::tuple<at::Tensor,at::Tensor> max(at::Dimname dim, bool keepdim=false) const;
  at::Tensor amax(at::IntArrayRef dim={}, bool keepdim=false) const;
  at::Tensor mean(::std::optional<at::ScalarType> dtype=::std::nullopt) const;
  at::Tensor mean(at::OptionalIntArrayRef dim, bool keepdim=false, ::std::optional<at::ScalarType> dtype=::std::nullopt) const;
  at::Tensor mean(at::DimnameList dim, bool keepdim=false, ::std::optional<at::ScalarType> dtype=::std::nullopt) const;
  at::Tensor nanmean(at::OptionalIntArrayRef dim=::std::nullopt, bool keepdim=false, ::std::optional<at::ScalarType> dtype=::std::nullopt) const;
  at::Tensor median() const;
  ::std::tuple<at::Tensor,at::Tensor> median(int64_t dim, bool keepdim=false) const;
  ::std::tuple<at::Tensor,at::Tensor> median(at::Dimname dim, bool keepdim=false) const;
  at::Tensor nanmedian() const;
  ::std::tuple<at::Tensor,at::Tensor> nanmedian(int64_t dim, bool keepdim=false) const;
  ::std::tuple<at::Tensor,at::Tensor> nanmedian(at::Dimname dim, bool keepdim=false) const;
  ::std::tuple<at::Tensor,at::Tensor> min(int64_t dim, bool keepdim=false) const;
  ::std::tuple<at::Tensor,at::Tensor> min(at::Dimname dim, bool keepdim=false) const;
  at::Tensor amin(at::IntArrayRef dim={}, bool keepdim=false) const;
  at::Tensor mm(const at::Tensor & mat2) const;
  ::std::tuple<at::Tensor,at::Tensor> mode(int64_t dim=-1, bool keepdim=false) const;
  ::std::tuple<at::Tensor,at::Tensor> mode(at::Dimname dim, bool keepdim=false) const;
  at::Tensor mul(const at::Tensor & other) const;
  at::Tensor & mul_(const at::Tensor & other) const;
  at::Tensor mul(const at::Scalar & other) const;
  at::Tensor & mul_(const at::Scalar & other) const;
  at::Tensor multiply(const at::Tensor & other) const;
  at::Tensor & multiply_(const at::Tensor & other) const;
  at::Tensor multiply(const at::Scalar & other) const;
  at::Tensor & multiply_(const at::Scalar & other) const;
  at::Tensor mv(const at::Tensor & vec) const;
  at::Tensor mvlgamma(int64_t p) const;
  at::Tensor & mvlgamma_(int64_t p) const;
  at::Tensor narrow_copy(int64_t dim, int64_t start, int64_t length) const;
  at::Tensor narrow_copy_symint(int64_t dim, c10::SymInt start, c10::SymInt length) const;
  at::Tensor narrow(int64_t dim, int64_t start, int64_t length) const;
  at::Tensor narrow_symint(int64_t dim, c10::SymInt start, c10::SymInt length) const;
  at::Tensor narrow(int64_t dim, const at::Tensor & start, int64_t length) const;
  at::Tensor narrow_symint(int64_t dim, const at::Tensor & start, c10::SymInt length) const;
  at::Tensor permute(at::IntArrayRef dims) const;
  at::Tensor movedim(at::IntArrayRef source, at::IntArrayRef destination) const;
  at::Tensor movedim(int64_t source, int64_t destination) const;
  at::Tensor moveaxis(at::IntArrayRef source, at::IntArrayRef destination) const;
  at::Tensor moveaxis(int64_t source, int64_t destination) const;
  at::Tensor numpy_T() const;
  at::Tensor matrix_H() const;
  at::Tensor mT() const;
  at::Tensor mH() const;
  at::Tensor adjoint() const;
  bool is_pinned(::std::optional<at::Device> device=::std::nullopt) const;
  at::Tensor pin_memory(::std::optional<at::Device> device=::std::nullopt) const;
  at::Tensor pinverse(double rcond=1e-15) const;
  at::Tensor rad2deg() const;
  at::Tensor & rad2deg_() const;
  at::Tensor deg2rad() const;
  at::Tensor & deg2rad_() const;
  at::Tensor ravel() const;
  at::Tensor reciprocal() const;
  at::Tensor & reciprocal_() const;
  at::Tensor neg() const;
  at::Tensor & neg_() const;
  at::Tensor negative() const;
  at::Tensor & negative_() const;
  at::Tensor repeat(at::IntArrayRef repeats) const;
  at::Tensor repeat_symint(c10::SymIntArrayRef repeats) const;
  at::Tensor repeat_interleave(const at::Tensor & repeats, ::std::optional<int64_t> dim=::std::nullopt, ::std::optional<int64_t> output_size=::std::nullopt) const;
  at::Tensor repeat_interleave_symint(const at::Tensor & repeats, ::std::optional<int64_t> dim=::std::nullopt, ::std::optional<c10::SymInt> output_size=::std::nullopt) const;
  at::Tensor repeat_interleave(int64_t repeats, ::std::optional<int64_t> dim=::std::nullopt, ::std::optional<int64_t> output_size=::std::nullopt) const;
  at::Tensor repeat_interleave_symint(c10::SymInt repeats, ::std::optional<int64_t> dim=::std::nullopt, ::std::optional<c10::SymInt> output_size=::std::nullopt) const;
  at::Tensor reshape(at::IntArrayRef shape) const;
  at::Tensor reshape_symint(c10::SymIntArrayRef shape) const;
  at::Tensor _reshape_alias(at::IntArrayRef size, at::IntArrayRef stride) const;
  at::Tensor _reshape_alias_symint(c10::SymIntArrayRef size, c10::SymIntArrayRef stride) const;
  at::Tensor reshape_as(const at::Tensor & other) const;
  at::Tensor round() const;
  at::Tensor & round_() const;
  at::Tensor round(int64_t decimals) const;
  at::Tensor & round_(int64_t decimals) const;
  at::Tensor relu() const;
  at::Tensor & relu_() const;
  at::Tensor prelu(const at::Tensor & weight) const;
  at::Tensor hardshrink(const at::Scalar & lambd=0.5) const;
  at::Tensor hardshrink_backward(const at::Tensor & grad_out, const at::Scalar & lambd) const;
  at::Tensor rsqrt() const;
  at::Tensor & rsqrt_() const;
  at::Tensor select(at::Dimname dim, int64_t index) const;
  at::Tensor select(int64_t dim, int64_t index) const;
  at::Tensor select_symint(int64_t dim, c10::SymInt index) const;
  at::Tensor sigmoid() const;
  at::Tensor & sigmoid_() const;
  at::Tensor logit(::std::optional<double> eps=::std::nullopt) const;
  at::Tensor & logit_(::std::optional<double> eps=::std::nullopt) const;
  at::Tensor sin() const;
  at::Tensor & sin_() const;
  at::Tensor sinc() const;
  at::Tensor & sinc_() const;
  at::Tensor sinh() const;
  at::Tensor & sinh_() const;
  at::Tensor detach() const;
  at::Tensor & detach_() const;
  int64_t size(at::Dimname dim) const;
  at::Tensor slice(int64_t dim=0, ::std::optional<int64_t> start=::std::nullopt, ::std::optional<int64_t> end=::std::nullopt, int64_t step=1) const;
  at::Tensor slice_symint(int64_t dim=0, ::std::optional<c10::SymInt> start=::std::nullopt, ::std::optional<c10::SymInt> end=::std::nullopt, c10::SymInt step=1) const;
  at::Tensor slice_inverse(const at::Tensor & src, int64_t dim=0, ::std::optional<int64_t> start=::std::nullopt, ::std::optional<int64_t> end=::std::nullopt, int64_t step=1) const;
  at::Tensor slice_inverse_symint(const at::Tensor & src, int64_t dim=0, ::std::optional<c10::SymInt> start=::std::nullopt, ::std::optional<c10::SymInt> end=::std::nullopt, c10::SymInt step=1) const;
  at::Tensor slice_scatter(const at::Tensor & src, int64_t dim=0, ::std::optional<int64_t> start=::std::nullopt, ::std::optional<int64_t> end=::std::nullopt, int64_t step=1) const;
  at::Tensor slice_scatter_symint(const at::Tensor & src, int64_t dim=0, ::std::optional<c10::SymInt> start=::std::nullopt, ::std::optional<c10::SymInt> end=::std::nullopt, c10::SymInt step=1) const;
  at::Tensor select_scatter(const at::Tensor & src, int64_t dim, int64_t index) const;
  at::Tensor select_scatter_symint(const at::Tensor & src, int64_t dim, c10::SymInt index) const;
  at::Tensor diagonal_scatter(const at::Tensor & src, int64_t offset=0, int64_t dim1=0, int64_t dim2=1) const;
  at::Tensor as_strided_scatter(const at::Tensor & src, at::IntArrayRef size, at::IntArrayRef stride, ::std::optional<int64_t> storage_offset=::std::nullopt) const;
  at::Tensor as_strided_scatter_symint(const at::Tensor & src, c10::SymIntArrayRef size, c10::SymIntArrayRef stride, ::std::optional<c10::SymInt> storage_offset=::std::nullopt) const;
  at::Tensor smm(const at::Tensor & mat2) const;
  at::Tensor softmax(int64_t dim, ::std::optional<at::ScalarType> dtype=::std::nullopt) const;
  at::Tensor softmax(at::Dimname dim, ::std::optional<at::ScalarType> dtype=::std::nullopt) const;
  ::std::vector<at::Tensor> unsafe_split(int64_t split_size, int64_t dim=0) const;
  ::std::vector<at::Tensor> unsafe_split_symint(c10::SymInt split_size, int64_t dim=0) const;
  ::std::vector<at::Tensor> split(int64_t split_size, int64_t dim=0) const;
  ::std::vector<at::Tensor> split_symint(c10::SymInt split_size, int64_t dim=0) const;
  ::std::vector<at::Tensor> split(at::IntArrayRef split_size, int64_t dim=0) const;
  ::std::vector<at::Tensor> split_symint(c10::SymIntArrayRef split_size, int64_t dim=0) const;
  ::std::vector<at::Tensor> unsafe_split_with_sizes(at::IntArrayRef split_sizes, int64_t dim=0) const;
  ::std::vector<at::Tensor> unsafe_split_with_sizes_symint(c10::SymIntArrayRef split_sizes, int64_t dim=0) const;
  ::std::vector<at::Tensor> split_with_sizes(at::IntArrayRef split_sizes, int64_t dim=0) const;
  ::std::vector<at::Tensor> split_with_sizes_symint(c10::SymIntArrayRef split_sizes, int64_t dim=0) const;
  ::std::vector<at::Tensor> hsplit(int64_t sections) const;
  ::std::vector<at::Tensor> hsplit(at::IntArrayRef indices) const;
  ::std::vector<at::Tensor> vsplit(int64_t sections) const;
  ::std::vector<at::Tensor> vsplit(at::IntArrayRef indices) const;
  ::std::vector<at::Tensor> dsplit(int64_t sections) const;
  ::std::vector<at::Tensor> dsplit(at::IntArrayRef indices) const;
  at::Tensor squeeze() const;
  at::Tensor squeeze(int64_t dim) const;
  at::Tensor squeeze(at::Dimname dim) const;
  at::Tensor squeeze(at::IntArrayRef dim) const;
  at::Tensor & squeeze_() const;
  at::Tensor & squeeze_(int64_t dim) const;
  at::Tensor & squeeze_(at::IntArrayRef dim) const;
  at::Tensor & squeeze_(at::Dimname dim) const;
  at::Tensor sspaddmm(const at::Tensor & mat1, const at::Tensor & mat2, const at::Scalar & beta=1, const at::Scalar & alpha=1) const;
  at::Tensor stft(int64_t n_fft, ::std::optional<int64_t> hop_length, ::std::optional<int64_t> win_length, const ::std::optional<at::Tensor> & window, bool normalized, ::std::optional<bool> onesided=::std::nullopt, ::std::optional<bool> return_complex=::std::nullopt, ::std::optional<bool> align_to_window=::std::nullopt) const;
  at::Tensor stft(int64_t n_fft, ::std::optional<int64_t> hop_length=::std::nullopt, ::std::optional<int64_t> win_length=::std::nullopt, const ::std::optional<at::Tensor> & window={}, bool center=true, c10::string_view pad_mode="reflect", bool normalized=false, ::std::optional<bool> onesided=::std::nullopt, ::std::optional<bool> return_complex=::std::nullopt, ::std::optional<bool> align_to_window=::std::nullopt) const;
  at::Tensor istft(int64_t n_fft, ::std::optional<int64_t> hop_length=::std::nullopt, ::std::optional<int64_t> win_length=::std::nullopt, const ::std::optional<at::Tensor> & window={}, bool center=true, bool normalized=false, ::std::optional<bool> onesided=::std::nullopt, ::std::optional<int64_t> length=::std::nullopt, bool return_complex=false) const;
  int64_t stride(at::Dimname dim) const;
  at::Tensor sum(::std::optional<at::ScalarType> dtype=::std::nullopt) const;
  at::Tensor sum(at::OptionalIntArrayRef dim, bool keepdim=false, ::std::optional<at::ScalarType> dtype=::std::nullopt) const;
  at::Tensor sum(at::DimnameList dim, bool keepdim=false, ::std::optional<at::ScalarType> dtype=::std::nullopt) const;
  at::Tensor nansum(at::OptionalIntArrayRef dim=::std::nullopt, bool keepdim=false, ::std::optional<at::ScalarType> dtype=::std::nullopt) const;
  at::Tensor sum_to_size(at::IntArrayRef size) const;
  at::Tensor sum_to_size_symint(c10::SymIntArrayRef size) const;
  at::Tensor sqrt() const;
  at::Tensor & sqrt_() const;
  at::Tensor square() const;
  at::Tensor & square_() const;
  at::Tensor std(bool unbiased) const;
  at::Tensor std(at::OptionalIntArrayRef dim, bool unbiased, bool keepdim=false) const;
  at::Tensor std(at::OptionalIntArrayRef dim=::std::nullopt, const ::std::optional<at::Scalar> & correction=::std::nullopt, bool keepdim=false) const;
  at::Tensor std(at::DimnameList dim, bool unbiased, bool keepdim=false) const;
  at::Tensor std(at::DimnameList dim, const ::std::optional<at::Scalar> & correction=::std::nullopt, bool keepdim=false) const;
  at::Tensor prod(::std::optional<at::ScalarType> dtype=::std::nullopt) const;
  at::Tensor prod(int64_t dim, bool keepdim=false, ::std::optional<at::ScalarType> dtype=::std::nullopt) const;
  at::Tensor prod(at::Dimname dim, bool keepdim=false, ::std::optional<at::ScalarType> dtype=::std::nullopt) const;
  at::Tensor t() const;
  at::Tensor & t_() const;
  at::Tensor tan() const;
  at::Tensor & tan_() const;
  at::Tensor tanh() const;
  at::Tensor & tanh_() const;
  at::Tensor tile(at::IntArrayRef dims) const;
  at::Tensor tile_symint(c10::SymIntArrayRef dims) const;
  at::Tensor transpose(int64_t dim0, int64_t dim1) const;
  at::Tensor transpose(at::Dimname dim0, at::Dimname dim1) const;
  at::Tensor & transpose_(int64_t dim0, int64_t dim1) const;
  at::Tensor flip(at::IntArrayRef dims) const;
  at::Tensor fliplr() const;
  at::Tensor flipud() const;
  at::Tensor roll(at::IntArrayRef shifts, at::IntArrayRef dims={}) const;
  at::Tensor roll_symint(c10::SymIntArrayRef shifts, at::IntArrayRef dims={}) const;
  at::Tensor rot90(int64_t k=1, at::IntArrayRef dims={0,1}) const;
  at::Tensor _nested_tensor_size() const;
  at::Tensor _nested_tensor_strides() const;
  at::Tensor _nested_tensor_storage_offsets() const;
  at::Tensor trunc() const;
  at::Tensor & trunc_() const;
  at::Tensor fix() const;
  at::Tensor & fix_() const;
  at::Tensor type_as(const at::Tensor & other) const;
  at::Tensor unsqueeze(int64_t dim) const;
  at::Tensor & unsqueeze_(int64_t dim) const;
  at::Tensor var(bool unbiased) const;
  at::Tensor var(at::OptionalIntArrayRef dim, bool unbiased, bool keepdim=false) const;
  at::Tensor var(at::OptionalIntArrayRef dim=::std::nullopt, const ::std::optional<at::Scalar> & correction=::std::nullopt, bool keepdim=false) const;
  at::Tensor var(at::DimnameList dim, bool unbiased, bool keepdim=false) const;
  at::Tensor var(at::DimnameList dim, const ::std::optional<at::Scalar> & correction=::std::nullopt, bool keepdim=false) const;
  at::Tensor view_as(const at::Tensor & other) const;
  at::Tensor where(const at::Tensor & condition, const at::Tensor & other) const;
  at::Tensor where(const at::Tensor & condition, const at::Scalar & other) const;
  at::Tensor norm(const ::std::optional<at::Scalar> & p, at::ScalarType dtype) const;
  at::Tensor norm(const at::Scalar & p=2) const;
  at::Tensor norm(const ::std::optional<at::Scalar> & p, at::IntArrayRef dim, bool keepdim, at::ScalarType dtype) const;
  at::Tensor norm(const ::std::optional<at::Scalar> & p, at::IntArrayRef dim, bool keepdim=false) const;
  at::Tensor norm(const ::std::optional<at::Scalar> & p, at::DimnameList dim, bool keepdim, at::ScalarType dtype) const;
  at::Tensor norm(const ::std::optional<at::Scalar> & p, at::DimnameList dim, bool keepdim=false) const;
  ::std::tuple<at::Tensor,at::Tensor> frexp() const;
  at::Tensor clone(::std::optional<at::MemoryFormat> memory_format=::std::nullopt) const;
  at::Tensor positive() const;
  const at::Tensor & resize_as_(const at::Tensor & the_template, ::std::optional<at::MemoryFormat> memory_format=::std::nullopt) const;
  const at::Tensor & resize_as_sparse_(const at::Tensor & the_template) const;
  at::Tensor & zero_() const;
  at::Tensor sub(const at::Tensor & other, const at::Scalar & alpha=1) const;
  at::Tensor & sub_(const at::Tensor & other, const at::Scalar & alpha=1) const;
  at::Tensor sub(const at::Scalar & other, const at::Scalar & alpha=1) const;
  at::Tensor & sub_(const at::Scalar & other, const at::Scalar & alpha=1) const;
  at::Tensor subtract(const at::Tensor & other, const at::Scalar & alpha=1) const;
  at::Tensor & subtract_(const at::Tensor & other, const at::Scalar & alpha=1) const;
  at::Tensor subtract(const at::Scalar & other, const at::Scalar & alpha=1) const;
  at::Tensor & subtract_(const at::Scalar & other, const at::Scalar & alpha=1) const;
  at::Tensor heaviside(const at::Tensor & values) const;
  at::Tensor & heaviside_(const at::Tensor & values) const;
  at::Tensor addmm(const at::Tensor & mat1, const at::Tensor & mat2, const at::Scalar & beta=1, const at::Scalar & alpha=1) const;
  at::Tensor & addmm_(const at::Tensor & mat1, const at::Tensor & mat2, const at::Scalar & beta=1, const at::Scalar & alpha=1) const;
  at::Tensor _addmm_activation(const at::Tensor & mat1, const at::Tensor & mat2, const at::Scalar & beta=1, const at::Scalar & alpha=1, bool use_gelu=false) const;
  const at::Tensor & sparse_resize_(at::IntArrayRef size, int64_t sparse_dim, int64_t dense_dim) const;
  const at::Tensor & sparse_resize_and_clear_(at::IntArrayRef size, int64_t sparse_dim, int64_t dense_dim) const;
  at::Tensor sparse_mask(const at::Tensor & mask) const;
  at::Tensor _sparse_mask_projection(const at::Tensor & mask, bool accumulate_matches=false) const;
  at::Tensor to_dense(::std::optional<at::ScalarType> dtype=::std::nullopt, ::std::optional<bool> masked_grad=::std::nullopt) const;
  at::Tensor _to_dense(::std::optional<at::ScalarType> dtype=::std::nullopt, ::std::optional<bool> masked_grad=::std::nullopt) const;
  int64_t sparse_dim() const;
  int64_t _dimI() const;
  int64_t dense_dim() const;
  int64_t _dimV() const;
  int64_t _nnz() const;
  at::Tensor coalesce() const;
  bool is_coalesced() const;
  at::Tensor _indices() const;
  at::Tensor _values() const;
  at::Tensor & _coalesced_(bool coalesced) const;
  at::Tensor indices() const;
  at::Tensor values() const;
  at::Tensor crow_indices() const;
  at::Tensor col_indices() const;
  at::Tensor ccol_indices() const;
  at::Tensor row_indices() const;
  ::std::vector<at::Tensor> unbind(int64_t dim=0) const;
  ::std::vector<at::Tensor> unbind(at::Dimname dim) const;
  at::Tensor to_sparse(int64_t sparse_dim) const;
  at::Tensor _to_sparse(int64_t sparse_dim) const;
  at::Tensor to_sparse(::std::optional<at::Layout> layout=::std::nullopt, at::OptionalIntArrayRef blocksize=::std::nullopt, ::std::optional<int64_t> dense_dim=::std::nullopt) const;
  at::Tensor _to_sparse(::std::optional<at::Layout> layout=::std::nullopt, at::OptionalIntArrayRef blocksize=::std::nullopt, ::std::optional<int64_t> dense_dim=::std::nullopt) const;
  at::Tensor to_sparse_csr(::std::optional<int64_t> dense_dim=::std::nullopt) const;
  at::Tensor _to_sparse_csr(::std::optional<int64_t> dense_dim=::std::nullopt) const;
  at::Tensor to_sparse_csc(::std::optional<int64_t> dense_dim=::std::nullopt) const;
  at::Tensor _to_sparse_csc(::std::optional<int64_t> dense_dim=::std::nullopt) const;
  at::Tensor to_sparse_bsr(at::IntArrayRef blocksize, ::std::optional<int64_t> dense_dim=::std::nullopt) const;
  at::Tensor _to_sparse_bsr(at::IntArrayRef blocksize, ::std::optional<int64_t> dense_dim=::std::nullopt) const;
  at::Tensor to_sparse_bsc(at::IntArrayRef blocksize, ::std::optional<int64_t> dense_dim=::std::nullopt) const;
  at::Tensor _to_sparse_bsc(at::IntArrayRef blocksize, ::std::optional<int64_t> dense_dim=::std::nullopt) const;
  at::Tensor to_mkldnn(::std::optional<at::ScalarType> dtype=::std::nullopt) const;
  at::Tensor dequantize() const;
  double q_scale() const;
  int64_t q_zero_point() const;
  at::Tensor q_per_channel_scales() const;
  at::Tensor q_per_channel_zero_points() const;
  int64_t q_per_channel_axis() const;
  at::Tensor int_repr() const;
  at::QScheme qscheme() const;
  at::Tensor _autocast_to_reduced_precision(bool cuda_enabled, bool cpu_enabled, at::ScalarType cuda_dtype, at::ScalarType cpu_dtype) const;
  at::Tensor _autocast_to_full_precision(bool cuda_enabled, bool cpu_enabled) const;
  at::Tensor to(at::TensorOptions options={}, bool non_blocking=false, bool copy=false, ::std::optional<at::MemoryFormat> memory_format=::std::nullopt) const;
  at::Tensor to(::std::optional<at::ScalarType> dtype, ::std::optional<at::Layout> layout, ::std::optional<at::Device> device, ::std::optional<bool> pin_memory, bool non_blocking, bool copy, ::std::optional<at::MemoryFormat> memory_format) const;
  at::Tensor to(at::Device device, at::ScalarType dtype, bool non_blocking=false, bool copy=false, ::std::optional<at::MemoryFormat> memory_format=::std::nullopt) const;
  at::Tensor to(at::ScalarType dtype, bool non_blocking=false, bool copy=false, ::std::optional<at::MemoryFormat> memory_format=::std::nullopt) const;
  at::Tensor to(const at::Tensor & other, bool non_blocking=false, bool copy=false, ::std::optional<at::MemoryFormat> memory_format=::std::nullopt) const;
  at::Scalar item() const;
  at::Tensor & set_(at::Storage source) const;
  at::Tensor & set_(at::Storage source, int64_t storage_offset, at::IntArrayRef size, at::IntArrayRef stride={}) const;
  at::Tensor & set__symint(at::Storage source, c10::SymInt storage_offset, c10::SymIntArrayRef size, c10::SymIntArrayRef stride={}) const;
  at::Tensor & set_(const at::Tensor & source, int64_t storage_offset, at::IntArrayRef size, at::IntArrayRef stride={}) const;
  at::Tensor & set__symint(const at::Tensor & source, c10::SymInt storage_offset, c10::SymIntArrayRef size, c10::SymIntArrayRef stride={}) const;
  at::Tensor & set_(const at::Tensor & source) const;
  at::Tensor & set_() const;
  bool is_set_to(const at::Tensor & tensor) const;
  at::Tensor & masked_fill_(const at::Tensor & mask, const at::Scalar & value) const;
  at::Tensor masked_fill(const at::Tensor & mask, const at::Scalar & value) const;
  at::Tensor & masked_fill_(const at::Tensor & mask, const at::Tensor & value) const;
  at::Tensor masked_fill(const at::Tensor & mask, const at::Tensor & value) const;
  at::Tensor & masked_scatter_(const at::Tensor & mask, const at::Tensor & source) const;
  at::Tensor masked_scatter(const at::Tensor & mask, const at::Tensor & source) const;
  at::Tensor view(at::IntArrayRef size) const;
  at::Tensor view_symint(c10::SymIntArrayRef size) const;
  at::Tensor view(at::ScalarType dtype) const;
  at::Tensor & put_(const at::Tensor & index, const at::Tensor & source, bool accumulate=false) const;
  at::Tensor put(const at::Tensor & index, const at::Tensor & source, bool accumulate=false) const;
  at::Tensor & index_add_(int64_t dim, const at::Tensor & index, const at::Tensor & source, const at::Scalar & alpha=1) const;
  at::Tensor index_add(int64_t dim, const at::Tensor & index, const at::Tensor & source, const at::Scalar & alpha=1) const;
  at::Tensor index_add(at::Dimname dim, const at::Tensor & index, const at::Tensor & source, const at::Scalar & alpha=1) const;
  at::Tensor & index_reduce_(int64_t dim, const at::Tensor & index, const at::Tensor & source, c10::string_view reduce, bool include_self=true) const;
  at::Tensor index_reduce(int64_t dim, const at::Tensor & index, const at::Tensor & source, c10::string_view reduce, bool include_self=true) const;
  at::Tensor & index_fill_(int64_t dim, const at::Tensor & index, const at::Scalar & value) const;
  at::Tensor index_fill(int64_t dim, const at::Tensor & index, const at::Scalar & value) const;
  at::Tensor & index_fill_(int64_t dim, const at::Tensor & index, const at::Tensor & value) const;
  at::Tensor index_fill(int64_t dim, const at::Tensor & index, const at::Tensor & value) const;
  at::Tensor & index_fill_(at::Dimname dim, const at::Tensor & index, const at::Scalar & value) const;
  at::Tensor & index_fill_(at::Dimname dim, const at::Tensor & index, const at::Tensor & value) const;
  at::Tensor index_fill(at::Dimname dim, const at::Tensor & index, const at::Scalar & value) const;
  at::Tensor index_fill(at::Dimname dim, const at::Tensor & index, const at::Tensor & value) const;
  at::Tensor scatter(int64_t dim, const at::Tensor & index, const at::Tensor & src) const;
  at::Tensor & scatter_(int64_t dim, const at::Tensor & index, const at::Tensor & src) const;
  at::Tensor scatter(int64_t dim, const at::Tensor & index, const at::Scalar & value) const;
  at::Tensor & scatter_(int64_t dim, const at::Tensor & index, const at::Scalar & value) const;
  at::Tensor scatter(int64_t dim, const at::Tensor & index, const at::Tensor & src, c10::string_view reduce) const;
  at::Tensor & scatter_(int64_t dim, const at::Tensor & index, const at::Tensor & src, c10::string_view reduce) const;
  at::Tensor scatter(int64_t dim, const at::Tensor & index, const at::Scalar & value, c10::string_view reduce) const;
  at::Tensor & scatter_(int64_t dim, const at::Tensor & index, const at::Scalar & value, c10::string_view reduce) const;
  at::Tensor scatter(at::Dimname dim, const at::Tensor & index, const at::Tensor & src) const;
  at::Tensor scatter(at::Dimname dim, const at::Tensor & index, const at::Scalar & value) const;
  at::Tensor scatter_add(int64_t dim, const at::Tensor & index, const at::Tensor & src) const;
  at::Tensor & scatter_add_(int64_t dim, const at::Tensor & index, const at::Tensor & src) const;
  at::Tensor scatter_add(at::Dimname dim, const at::Tensor & index, const at::Tensor & src) const;
  at::Tensor scatter_reduce(int64_t dim, const at::Tensor & index, const at::Tensor & src, c10::string_view reduce, bool include_self=true) const;
  at::Tensor & scatter_reduce_(int64_t dim, const at::Tensor & index, const at::Tensor & src, c10::string_view reduce, bool include_self=true) const;
  at::Tensor & eq_(const at::Scalar & other) const;
  at::Tensor & eq_(const at::Tensor & other) const;
  at::Tensor bitwise_and(const at::Scalar & other) const;
  at::Tensor bitwise_and(const at::Tensor & other) const;
  at::Tensor & bitwise_and_(const at::Scalar & other) const;
  at::Tensor & bitwise_and_(const at::Tensor & other) const;
  at::Tensor __and__(const at::Scalar & other) const;
  at::Tensor __and__(const at::Tensor & other) const;
  at::Tensor & __iand__(const at::Scalar & other) const;
  at::Tensor & __iand__(const at::Tensor & other) const;
  at::Tensor bitwise_or(const at::Scalar & other) const;
  at::Tensor bitwise_or(const at::Tensor & other) const;
  at::Tensor & bitwise_or_(const at::Scalar & other) const;
  at::Tensor & bitwise_or_(const at::Tensor & other) const;
  at::Tensor __or__(const at::Scalar & other) const;
  at::Tensor __or__(const at::Tensor & other) const;
  at::Tensor & __ior__(const at::Scalar & other) const;
  at::Tensor & __ior__(const at::Tensor & other) const;
  at::Tensor bitwise_xor(const at::Scalar & other) const;
  at::Tensor bitwise_xor(const at::Tensor & other) const;
  at::Tensor & bitwise_xor_(const at::Scalar & other) const;
  at::Tensor & bitwise_xor_(const at::Tensor & other) const;
  at::Tensor __xor__(const at::Scalar & other) const;
  at::Tensor __xor__(const at::Tensor & other) const;
  at::Tensor & __ixor__(const at::Scalar & other) const;
  at::Tensor & __ixor__(const at::Tensor & other) const;
  at::Tensor __lshift__(const at::Scalar & other) const;
  at::Tensor __lshift__(const at::Tensor & other) const;
  at::Tensor & __ilshift__(const at::Scalar & other) const;
  at::Tensor & __ilshift__(const at::Tensor & other) const;
  at::Tensor bitwise_left_shift(const at::Tensor & other) const;
  at::Tensor & bitwise_left_shift_(const at::Tensor & other) const;
  at::Tensor bitwise_left_shift(const at::Scalar & other) const;
  at::Tensor & bitwise_left_shift_(const at::Scalar & other) const;
  at::Tensor __rshift__(const at::Scalar & other) const;
  at::Tensor __rshift__(const at::Tensor & other) const;
  at::Tensor & __irshift__(const at::Scalar & other) const;
  at::Tensor & __irshift__(const at::Tensor & other) const;
  at::Tensor bitwise_right_shift(const at::Tensor & other) const;
  at::Tensor & bitwise_right_shift_(const at::Tensor & other) const;
  at::Tensor bitwise_right_shift(const at::Scalar & other) const;
  at::Tensor & bitwise_right_shift_(const at::Scalar & other) const;
  at::Tensor & tril_(int64_t diagonal=0) const;
  at::Tensor & triu_(int64_t diagonal=0) const;
  at::Tensor & digamma_() const;
  at::Tensor & lerp_(const at::Tensor & end, const at::Scalar & weight) const;
  at::Tensor & lerp_(const at::Tensor & end, const at::Tensor & weight) const;
  at::Tensor & addbmm_(const at::Tensor & batch1, const at::Tensor & batch2, const at::Scalar & beta=1, const at::Scalar & alpha=1) const;
  at::Tensor addbmm(const at::Tensor & batch1, const at::Tensor & batch2, const at::Scalar & beta=1, const at::Scalar & alpha=1) const;
  at::Tensor & random_(int64_t from, ::std::optional<int64_t> to, ::std::optional<at::Generator> generator=::std::nullopt) const;
  at::Tensor & random_(int64_t to, ::std::optional<at::Generator> generator=::std::nullopt) const;
  at::Tensor & random_(::std::optional<at::Generator> generator=::std::nullopt) const;
  at::Tensor & uniform_(double from=0, double to=1, ::std::optional<at::Generator> generator=::std::nullopt) const;
  at::Tensor & cauchy_(double median=0, double sigma=1, ::std::optional<at::Generator> generator=::std::nullopt) const;
  at::Tensor & log_normal_(double mean=1, double std=2, ::std::optional<at::Generator> generator=::std::nullopt) const;
  at::Tensor & exponential_(double lambd=1, ::std::optional<at::Generator> generator=::std::nullopt) const;
  at::Tensor & geometric_(double p, ::std::optional<at::Generator> generator=::std::nullopt) const;
  at::Tensor diag(int64_t diagonal=0) const;
  at::Tensor cross(const at::Tensor & other, ::std::optional<int64_t> dim=::std::nullopt) const;
  at::Tensor triu(int64_t diagonal=0) const;
  at::Tensor tril(int64_t diagonal=0) const;
  at::Tensor trace() const;
  at::Tensor ne(const at::Scalar & other) const;
  at::Tensor ne(const at::Tensor & other) const;
  at::Tensor & ne_(const at::Scalar & other) const;
  at::Tensor & ne_(const at::Tensor & other) const;
  at::Tensor not_equal(const at::Scalar & other) const;
  at::Tensor not_equal(const at::Tensor & other) const;
  at::Tensor & not_equal_(const at::Scalar & other) const;
  at::Tensor & not_equal_(const at::Tensor & other) const;
  at::Tensor eq(const at::Scalar & other) const;
  at::Tensor eq(const at::Tensor & other) const;
  at::Tensor ge(const at::Scalar & other) const;
  at::Tensor ge(const at::Tensor & other) const;
  at::Tensor & ge_(const at::Scalar & other) const;
  at::Tensor & ge_(const at::Tensor & other) const;
  at::Tensor greater_equal(const at::Scalar & other) const;
  at::Tensor greater_equal(const at::Tensor & other) const;
  at::Tensor & greater_equal_(const at::Scalar & other) const;
  at::Tensor & greater_equal_(const at::Tensor & other) const;
  at::Tensor le(const at::Scalar & other) const;
  at::Tensor le(const at::Tensor & other) const;
  at::Tensor & le_(const at::Scalar & other) const;
  at::Tensor & le_(const at::Tensor & other) const;
  at::Tensor less_equal(const at::Scalar & other) const;
  at::Tensor less_equal(const at::Tensor & other) const;
  at::Tensor & less_equal_(const at::Scalar & other) const;
  at::Tensor & less_equal_(const at::Tensor & other) const;
  at::Tensor gt(const at::Scalar & other) const;
  at::Tensor gt(const at::Tensor & other) const;
  at::Tensor & gt_(const at::Scalar & other) const;
  at::Tensor & gt_(const at::Tensor & other) const;
  at::Tensor greater(const at::Scalar & other) const;
  at::Tensor greater(const at::Tensor & other) const;
  at::Tensor & greater_(const at::Scalar & other) const;
  at::Tensor & greater_(const at::Tensor & other) const;
  at::Tensor lt(const at::Scalar & other) const;
  at::Tensor lt(const at::Tensor & other) const;
  at::Tensor & lt_(const at::Scalar & other) const;
  at::Tensor & lt_(const at::Tensor & other) const;
  at::Tensor less(const at::Scalar & other) const;
  at::Tensor less(const at::Tensor & other) const;
  at::Tensor & less_(const at::Scalar & other) const;
  at::Tensor & less_(const at::Tensor & other) const;
  at::Tensor take(const at::Tensor & index) const;
  at::Tensor take_along_dim(const at::Tensor & indices, ::std::optional<int64_t> dim=::std::nullopt) const;
  at::Tensor index_select(int64_t dim, const at::Tensor & index) const;
  at::Tensor index_select(at::Dimname dim, const at::Tensor & index) const;
  at::Tensor masked_select(const at::Tensor & mask) const;
  at::Tensor nonzero() const;
  at::Tensor nonzero_static(int64_t size, int64_t fill_value=-1) const;
  at::Tensor nonzero_static_symint(c10::SymInt size, int64_t fill_value=-1) const;
  ::std::vector<at::Tensor> nonzero_numpy() const;
  at::Tensor argwhere() const;
  at::Tensor gather(int64_t dim, const at::Tensor & index, bool sparse_grad=false) const;
  at::Tensor gather(at::Dimname dim, const at::Tensor & index, bool sparse_grad=false) const;
  at::Tensor addcmul(const at::Tensor & tensor1, const at::Tensor & tensor2, const at::Scalar & value=1) const;
  at::Tensor & addcmul_(const at::Tensor & tensor1, const at::Tensor & tensor2, const at::Scalar & value=1) const;
  at::Tensor addcdiv(const at::Tensor & tensor1, const at::Tensor & tensor2, const at::Scalar & value=1) const;
  at::Tensor & addcdiv_(const at::Tensor & tensor1, const at::Tensor & tensor2, const at::Scalar & value=1) const;
  ::std::tuple<at::Tensor,at::Tensor> triangular_solve(const at::Tensor & A, bool upper=true, bool transpose=false, bool unitriangular=false) const;
  ::std::tuple<at::Tensor,at::Tensor,at::Tensor> svd(bool some=true, bool compute_uv=true) const;
  at::Tensor swapaxes(int64_t axis0, int64_t axis1) const;
  at::Tensor & swapaxes_(int64_t axis0, int64_t axis1) const;
  at::Tensor swapdims(int64_t dim0, int64_t dim1) const;
  at::Tensor & swapdims_(int64_t dim0, int64_t dim1) const;
  at::Tensor cholesky(bool upper=false) const;
  at::Tensor cholesky_solve(const at::Tensor & input2, bool upper=false) const;
  at::Tensor cholesky_inverse(bool upper=false) const;
  ::std::tuple<at::Tensor,at::Tensor> qr(bool some=true) const;
  ::std::tuple<at::Tensor,at::Tensor> geqrf() const;
  at::Tensor orgqr(const at::Tensor & input2) const;
  at::Tensor ormqr(const at::Tensor & input2, const at::Tensor & input3, bool left=true, bool transpose=false) const;
  at::Tensor lu_solve(const at::Tensor & LU_data, const at::Tensor & LU_pivots) const;
  at::Tensor multinomial(int64_t num_samples, bool replacement=false, ::std::optional<at::Generator> generator=::std::nullopt) const;
  at::Tensor & lgamma_() const;
  at::Tensor lgamma() const;
  at::Tensor digamma() const;
  at::Tensor polygamma(int64_t n) const;
  at::Tensor & polygamma_(int64_t n) const;
  at::Tensor erfinv() const;
  at::Tensor & erfinv_() const;
  at::Tensor i0() const;
  at::Tensor & i0_() const;
  at::Tensor sign() const;
  at::Tensor & sign_() const;
  at::Tensor signbit() const;
  at::Tensor dist(const at::Tensor & other, const at::Scalar & p=2) const;
  at::Tensor & atan2_(const at::Tensor & other) const;
  at::Tensor atan2(const at::Tensor & other) const;
  at::Tensor arctan2(const at::Tensor & other) const;
  at::Tensor & arctan2_(const at::Tensor & other) const;
  at::Tensor lerp(const at::Tensor & end, const at::Scalar & weight) const;
  at::Tensor lerp(const at::Tensor & end, const at::Tensor & weight) const;
  at::Tensor histc(int64_t bins=100, const at::Scalar & min=0, const at::Scalar & max=0) const;
  ::std::tuple<at::Tensor,at::Tensor> histogram(const at::Tensor & bins, const ::std::optional<at::Tensor> & weight={}, bool density=false) const;
  ::std::tuple<at::Tensor,at::Tensor> histogram(int64_t bins=100, ::std::optional<at::ArrayRef<double>> range=::std::nullopt, const ::std::optional<at::Tensor> & weight={}, bool density=false) const;
  at::Tensor fmod(const at::Scalar & other) const;
  at::Tensor & fmod_(const at::Scalar & other) const;
  at::Tensor fmod(const at::Tensor & other) const;
  at::Tensor & fmod_(const at::Tensor & other) const;
  at::Tensor hypot(const at::Tensor & other) const;
  at::Tensor & hypot_(const at::Tensor & other) const;
  at::Tensor igamma(const at::Tensor & other) const;
  at::Tensor & igamma_(const at::Tensor & other) const;
  at::Tensor igammac(const at::Tensor & other) const;
  at::Tensor & igammac_(const at::Tensor & other) const;
  at::Tensor nextafter(const at::Tensor & other) const;
  at::Tensor & nextafter_(const at::Tensor & other) const;
  at::Tensor remainder(const at::Scalar & other) const;
  at::Tensor & remainder_(const at::Scalar & other) const;
  at::Tensor remainder(const at::Tensor & other) const;
  at::Tensor & remainder_(const at::Tensor & other) const;
  at::Tensor min() const;
  at::Tensor fmin(const at::Tensor & other) const;
  at::Tensor max() const;
  at::Tensor fmax(const at::Tensor & other) const;
  at::Tensor maximum(const at::Tensor & other) const;
  at::Tensor max(const at::Tensor & other) const;
  at::Tensor minimum(const at::Tensor & other) const;
  at::Tensor min(const at::Tensor & other) const;
  at::Tensor quantile(const at::Tensor & q, ::std::optional<int64_t> dim=::std::nullopt, bool keepdim=false, c10::string_view interpolation="linear") const;
  at::Tensor quantile(double q, ::std::optional<int64_t> dim=::std::nullopt, bool keepdim=false, c10::string_view interpolation="linear") const;
  at::Tensor nanquantile(const at::Tensor & q, ::std::optional<int64_t> dim=::std::nullopt, bool keepdim=false, c10::string_view interpolation="linear") const;
  at::Tensor nanquantile(double q, ::std::optional<int64_t> dim=::std::nullopt, bool keepdim=false, c10::string_view interpolation="linear") const;
  ::std::tuple<at::Tensor,at::Tensor> sort(int64_t dim=-1, bool descending=false) const;
  ::std::tuple<at::Tensor,at::Tensor> sort(::std::optional<bool> stable, int64_t dim=-1, bool descending=false) const;
  ::std::tuple<at::Tensor,at::Tensor> sort(at::Dimname dim, bool descending=false) const;
  ::std::tuple<at::Tensor,at::Tensor> sort(::std::optional<bool> stable, at::Dimname dim, bool descending=false) const;
  at::Tensor msort() const;
  at::Tensor argsort(int64_t dim=-1, bool descending=false) const;
  at::Tensor argsort(bool stable, int64_t dim=-1, bool descending=false) const;
  at::Tensor argsort(at::Dimname dim, bool descending=false) const;
  ::std::tuple<at::Tensor,at::Tensor> topk(int64_t k, int64_t dim=-1, bool largest=true, bool sorted=true) const;
  ::std::tuple<at::Tensor,at::Tensor> topk_symint(c10::SymInt k, int64_t dim=-1, bool largest=true, bool sorted=true) const;
  at::Tensor all() const;
  at::Tensor any() const;
  at::Tensor renorm(const at::Scalar & p, int64_t dim, const at::Scalar & maxnorm) const;
  at::Tensor & renorm_(const at::Scalar & p, int64_t dim, const at::Scalar & maxnorm) const;
  at::Tensor unfold(int64_t dimension, int64_t size, int64_t step) const;
  bool equal(const at::Tensor & other) const;
  at::Tensor pow(const at::Tensor & exponent) const;
  at::Tensor pow(const at::Scalar & exponent) const;
  at::Tensor & pow_(const at::Scalar & exponent) const;
  at::Tensor & pow_(const at::Tensor & exponent) const;
  at::Tensor float_power(const at::Tensor & exponent) const;
  at::Tensor float_power(const at::Scalar & exponent) const;
  at::Tensor & float_power_(const at::Scalar & exponent) const;
  at::Tensor & float_power_(const at::Tensor & exponent) const;
  at::Tensor & normal_(double mean=0, double std=1, ::std::optional<at::Generator> generator=::std::nullopt) const;
  at::Tensor alias() const;
  at::Tensor isfinite() const;
  at::Tensor isinf() const;
  void record_stream(at::Stream s) const;
  at::Tensor isposinf() const;
  at::Tensor isneginf() const;
  at::Tensor det() const;
  ::std::tuple<at::Tensor,at::Tensor> slogdet() const;
  at::Tensor logdet() const;
  at::Tensor inverse() const;
  at::Tensor inner(const at::Tensor & other) const;
  at::Tensor outer(const at::Tensor & vec2) const;
  at::Tensor ger(const at::Tensor & vec2) const;
  at::Tensor to_padded_tensor(double padding, at::OptionalIntArrayRef output_size=::std::nullopt) const;
  at::Tensor to_padded_tensor_symint(double padding, at::OptionalSymIntArrayRef output_size=::std::nullopt) const;

  // Special C++ only overloads for std()-like functions (See gh-40287)
  // These are needed because int -> bool conversion takes precedence over int -> IntArrayRef
  // So, for example std(0) would select the std(unbiased=False) overload

  Tensor var(int dim) const {
    return var(IntArrayRef{dim});
  }

  Tensor std(int dim) const {
    return std(IntArrayRef{dim});
  }

  // We changed .dtype() to return a TypeMeta in #12766. Ideally, we want the
  // at::kDouble and its friends to be TypeMeta's, but that hasn't happened yet.
  // Before that change, we make this method to maintain BC for C++ usage like
  // `x.to(y.dtype)`.
  // TODO: remove following two after at::kDouble and its friends are TypeMeta's.
  inline Tensor to(caffe2::TypeMeta type_meta, bool non_blocking=false, bool copy=false) const {
    return this->to(/*scalar_type=*/typeMetaToScalarType(type_meta), non_blocking, copy);
  }
  inline Tensor to(Device device, caffe2::TypeMeta type_meta, bool non_blocking=false, bool copy=false) const {
    return this->to(device, /*scalar_type=*/typeMetaToScalarType(type_meta), non_blocking, copy);
  }

  template <typename F, typename... Args>
  decltype(auto) m(F func, Args&&... params) const {
    return func(*this, std::forward<Args>(params)...);
  }

  /// NOTE: This is similar to the legacy `.data()` function on `Variable`, and is intended
  /// to be used from functions that need to access the `Variable`'s equivalent `Tensor`
  /// (i.e. `Tensor` that shares the same storage and tensor metadata with the `Variable`).
  ///
  /// One notable difference with the legacy `.data()` function is that changes to the
  /// returned `Tensor`'s tensor metadata (e.g. sizes / strides / storage / storage_offset)
  /// will not update the original `Variable`, due to the fact that this function
  /// shallow-copies the `Variable`'s underlying TensorImpl.
  at::Tensor tensor_data() const {
    return TensorBase::tensor_data();
  }

  /// NOTE: `var.variable_data()` in C++ has the same semantics as `tensor.data`
  /// in Python, which create a new `Variable` that shares the same storage and
  /// tensor metadata with the original `Variable`, but with a completely new
  /// autograd history.
  ///
  /// NOTE: If we change the tensor metadata (e.g. sizes / strides /
  /// storage / storage_offset) of a variable created from `var.variable_data()`, those
  /// changes will not update the original variable `var`. In `.variable_data()`, we set
  /// `allow_tensor_metadata_change_` to false to make such changes explicitly illegal,
  /// in order to prevent users from changing metadata of `var.variable_data()`
  /// and expecting the original variable `var` to also be updated.
  at::Tensor variable_data() const {
    return TensorBase::variable_data();
  }

  // Hooks
  //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

  template <typename T>
  using hook_return_void_t = std::enable_if_t<std::is_void<typename std::invoke_result_t<T&, Tensor>>::value, unsigned>;
  template <typename T>
  using hook_return_var_t = std::enable_if_t<std::is_same_v<typename std::invoke_result_t<T&, Tensor>, Tensor>, unsigned>;

  /// Registers a backward hook.
  ///
  /// The hook will be called every time a gradient with respect to the Tensor is computed.
  /// The hook should have one of the following signature:
  /// ```
  /// hook(Tensor grad) -> Tensor
  /// ```
  /// ```
  /// hook(Tensor grad) -> void
  /// ```
  /// The hook should not modify its argument, but it can optionally return a new gradient
  /// which will be used in place of `grad`.
  ///
  /// This function returns the index of the hook in the list which can be used to remove hook.
  ///
  /// Example:
  /// @code
  /// auto v = torch::tensor({0., 0., 0.}, torch::requires_grad());
  /// auto h = v.register_hook([](torch::Tensor grad){ return grad * 2; }); // double the gradient
  /// v.backward(torch::tensor({1., 2., 3.}));
  /// // This prints:
  /// // ```
  /// //  2
  /// //  4
  /// //  6
  /// // [ CPUFloatType{3} ]
  /// // ```
  /// std::cout << v.grad() << std::endl;
  /// v.remove_hook(h);  // removes the hook
  /// @endcode
  template <typename T>
  hook_return_void_t<T> register_hook(T&& hook) const;
  template <typename T>
  hook_return_var_t<T> register_hook(T&& hook) const;

  // Variable methods
  //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

  Tensor data() const {
    return TensorBase::data();
  }

  void _backward(TensorList inputs, const std::optional<Tensor>& gradient, std::optional<bool> keep_graph, bool create_graph) const;

  const Tensor& requires_grad_(bool _requires_grad=true) const {
    TensorBase::requires_grad_(_requires_grad);
    return *this;
  }
};

namespace detail {
// Helper creator for Tensor class which doesn't requires the users to pass
// in an intrusive_ptr instead it just converts the argument passed to
// requested intrusive_ptr type.
template <typename T, typename... Args>
Tensor make_tensor(Args&&... args) {
  return Tensor(c10::make_intrusive<T>(std::forward<Args>(args)...));
}

} // namespace detail

} // namespace at


namespace at {

// aten::_backward(Tensor self, Tensor[] inputs, Tensor? gradient=None, bool? retain_graph=None, bool create_graph=False) -> ()
inline void Tensor::__dispatch__backward(at::TensorList inputs, const ::std::optional<at::Tensor> & gradient, ::std::optional<bool> retain_graph, bool create_graph) const {
    return at::_ops::_backward::call(const_cast<Tensor&>(*this), inputs, gradient, retain_graph, create_graph);
}

// aten::set_data(Tensor(a!) self, Tensor new_data) -> ()
inline void Tensor::__dispatch_set_data(const at::Tensor & new_data) const {
    return at::_ops::set_data::call(const_cast<Tensor&>(*this), new_data);
}

// aten::data(Tensor self) -> Tensor
inline at::Tensor Tensor::__dispatch_data() const {
    return at::_ops::data::call(const_cast<Tensor&>(*this));
}

// aten::is_leaf(Tensor self) -> bool
inline bool Tensor::__dispatch_is_leaf() const {
    return at::_ops::is_leaf::call(const_cast<Tensor&>(*this));
}

// aten::output_nr(Tensor self) -> int
inline int64_t Tensor::__dispatch_output_nr() const {
    return at::_ops::output_nr::call(const_cast<Tensor&>(*this));
}

// aten::_version(Tensor self) -> int
inline int64_t Tensor::__dispatch__version() const {
    return at::_ops::_version::call(const_cast<Tensor&>(*this));
}

// aten::requires_grad_(Tensor(a!) self, bool requires_grad=True) -> Tensor(a!)
inline at::Tensor & Tensor::__dispatch_requires_grad_(bool requires_grad) const {
    return at::_ops::requires_grad_::call(const_cast<Tensor&>(*this), requires_grad);
}

// aten::retain_grad(Tensor(a!) self) -> ()
inline void Tensor::__dispatch_retain_grad() const {
    return at::_ops::retain_grad::call(const_cast<Tensor&>(*this));
}

// aten::retains_grad(Tensor self) -> bool
inline bool Tensor::__dispatch_retains_grad() const {
    return at::_ops::retains_grad::call(const_cast<Tensor&>(*this));
}

// aten::_fw_primal(Tensor(a) self, int level) -> Tensor(a)
inline at::Tensor Tensor::_fw_primal(int64_t level) const {
    return at::_ops::_fw_primal::call(const_cast<Tensor&>(*this), level);
}

// aten::rename_(Tensor(a!) self, Dimname[]? names) -> Tensor(a!)
inline at::Tensor & Tensor::rename_(::std::optional<at::DimnameList> names) const {
    return at::_ops::rename_::call(const_cast<Tensor&>(*this), names);
}

// aten::rename(Tensor(a) self, Dimname[]? names) -> Tensor(a)
inline at::Tensor Tensor::rename(::std::optional<at::DimnameList> names) const {
    return at::_ops::rename::call(const_cast<Tensor&>(*this), names);
}

// aten::align_to(Tensor(a) self, Dimname[] names) -> Tensor(a)
inline at::Tensor Tensor::align_to(at::DimnameList names) const {
    return at::_ops::align_to::call(const_cast<Tensor&>(*this), names);
}

// aten::align_to.ellipsis_idx(Tensor(a) self, Dimname[] order, int ellipsis_idx) -> Tensor(a)
inline at::Tensor Tensor::align_to(at::DimnameList order, int64_t ellipsis_idx) const {
    return at::_ops::align_to_ellipsis_idx::call(const_cast<Tensor&>(*this), order, ellipsis_idx);
}

// aten::align_as(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::align_as(const at::Tensor & other) const {
    return at::_ops::align_as::call(const_cast<Tensor&>(*this), other);
}

// aten::refine_names(Tensor(a) self, Dimname[] names) -> Tensor(a)
inline at::Tensor Tensor::refine_names(at::DimnameList names) const {
    return at::_ops::refine_names::call(const_cast<Tensor&>(*this), names);
}

// aten::abs(Tensor self) -> Tensor
inline at::Tensor Tensor::abs() const {
    return at::_ops::abs::call(const_cast<Tensor&>(*this));
}

// aten::abs_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::abs_() const {
    return at::_ops::abs_::call(const_cast<Tensor&>(*this));
}

// aten::absolute(Tensor self) -> Tensor
inline at::Tensor Tensor::absolute() const {
    return at::_ops::absolute::call(const_cast<Tensor&>(*this));
}

// aten::absolute_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::absolute_() const {
    return at::_ops::absolute_::call(const_cast<Tensor&>(*this));
}

// aten::angle(Tensor self) -> Tensor
inline at::Tensor Tensor::angle() const {
    return at::_ops::angle::call(const_cast<Tensor&>(*this));
}

// aten::sgn(Tensor self) -> Tensor
inline at::Tensor Tensor::sgn() const {
    return at::_ops::sgn::call(const_cast<Tensor&>(*this));
}

// aten::sgn_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::sgn_() const {
    return at::_ops::sgn_::call(const_cast<Tensor&>(*this));
}

// aten::chalf(Tensor self, *, MemoryFormat? memory_format=None) -> Tensor
inline at::Tensor Tensor::chalf(::std::optional<at::MemoryFormat> memory_format) const {
    return at::_ops::chalf::call(const_cast<Tensor&>(*this), memory_format);
}

// aten::_conj(Tensor(a) self) -> Tensor(a)
inline at::Tensor Tensor::_conj() const {
    return at::_ops::_conj::call(const_cast<Tensor&>(*this));
}

// aten::conj(Tensor(a) self) -> Tensor(a)
inline at::Tensor Tensor::__dispatch_conj() const {
    return at::_ops::conj::call(const_cast<Tensor&>(*this));
}

// aten::_conj_physical(Tensor self) -> Tensor
inline at::Tensor Tensor::_conj_physical() const {
    return at::_ops::_conj_physical::call(const_cast<Tensor&>(*this));
}

// aten::conj_physical(Tensor self) -> Tensor
inline at::Tensor Tensor::conj_physical() const {
    return at::_ops::conj_physical::call(const_cast<Tensor&>(*this));
}

// aten::conj_physical_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::conj_physical_() const {
    return at::_ops::conj_physical_::call(const_cast<Tensor&>(*this));
}

// aten::resolve_conj(Tensor(a) self) -> Tensor(a)
inline at::Tensor Tensor::resolve_conj() const {
    return at::_ops::resolve_conj::call(const_cast<Tensor&>(*this));
}

// aten::resolve_neg(Tensor(a) self) -> Tensor(a)
inline at::Tensor Tensor::resolve_neg() const {
    return at::_ops::resolve_neg::call(const_cast<Tensor&>(*this));
}

// aten::_neg_view(Tensor(a) self) -> Tensor(a)
inline at::Tensor Tensor::_neg_view() const {
    return at::_ops::_neg_view::call(const_cast<Tensor&>(*this));
}

// aten::acos(Tensor self) -> Tensor
inline at::Tensor Tensor::acos() const {
    return at::_ops::acos::call(const_cast<Tensor&>(*this));
}

// aten::acos_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::acos_() const {
    return at::_ops::acos_::call(const_cast<Tensor&>(*this));
}

// aten::arccos(Tensor self) -> Tensor
inline at::Tensor Tensor::arccos() const {
    return at::_ops::arccos::call(const_cast<Tensor&>(*this));
}

// aten::arccos_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::arccos_() const {
    return at::_ops::arccos_::call(const_cast<Tensor&>(*this));
}

// aten::add.Tensor(Tensor self, Tensor other, *, Scalar alpha=1) -> Tensor
inline at::Tensor Tensor::add(const at::Tensor & other, const at::Scalar & alpha) const {
    return at::_ops::add_Tensor::call(const_cast<Tensor&>(*this), other, alpha);
}

// aten::add_.Tensor(Tensor(a!) self, Tensor other, *, Scalar alpha=1) -> Tensor(a!)
inline at::Tensor & Tensor::add_(const at::Tensor & other, const at::Scalar & alpha) const {
    return at::_ops::add__Tensor::call(const_cast<Tensor&>(*this), other, alpha);
}

// aten::add.Scalar(Tensor self, Scalar other, Scalar alpha=1) -> Tensor
inline at::Tensor Tensor::add(const at::Scalar & other, const at::Scalar & alpha) const {
    return at::_ops::add_Scalar::call(const_cast<Tensor&>(*this), other, alpha);
}

// aten::add_.Scalar(Tensor(a!) self, Scalar other, Scalar alpha=1) -> Tensor(a!)
inline at::Tensor & Tensor::add_(const at::Scalar & other, const at::Scalar & alpha) const {
    return at::_ops::add__Scalar::call(const_cast<Tensor&>(*this), other, alpha);
}

// aten::addmv(Tensor self, Tensor mat, Tensor vec, *, Scalar beta=1, Scalar alpha=1) -> Tensor
inline at::Tensor Tensor::addmv(const at::Tensor & mat, const at::Tensor & vec, const at::Scalar & beta, const at::Scalar & alpha) const {
    return at::_ops::addmv::call(const_cast<Tensor&>(*this), mat, vec, beta, alpha);
}

// aten::addmv_(Tensor(a!) self, Tensor mat, Tensor vec, *, Scalar beta=1, Scalar alpha=1) -> Tensor(a!)
inline at::Tensor & Tensor::addmv_(const at::Tensor & mat, const at::Tensor & vec, const at::Scalar & beta, const at::Scalar & alpha) const {
    return at::_ops::addmv_::call(const_cast<Tensor&>(*this), mat, vec, beta, alpha);
}

// aten::addr(Tensor self, Tensor vec1, Tensor vec2, *, Scalar beta=1, Scalar alpha=1) -> Tensor
inline at::Tensor Tensor::addr(const at::Tensor & vec1, const at::Tensor & vec2, const at::Scalar & beta, const at::Scalar & alpha) const {
    return at::_ops::addr::call(const_cast<Tensor&>(*this), vec1, vec2, beta, alpha);
}

// aten::addr_(Tensor(a!) self, Tensor vec1, Tensor vec2, *, Scalar beta=1, Scalar alpha=1) -> Tensor(a!)
inline at::Tensor & Tensor::addr_(const at::Tensor & vec1, const at::Tensor & vec2, const at::Scalar & beta, const at::Scalar & alpha) const {
    return at::_ops::addr_::call(const_cast<Tensor&>(*this), vec1, vec2, beta, alpha);
}

// aten::_is_all_true(Tensor self) -> Tensor
inline at::Tensor Tensor::_is_all_true() const {
    return at::_ops::_is_all_true::call(const_cast<Tensor&>(*this));
}

// aten::_is_any_true(Tensor self) -> Tensor
inline at::Tensor Tensor::_is_any_true() const {
    return at::_ops::_is_any_true::call(const_cast<Tensor&>(*this));
}

// aten::all.dim(Tensor self, int dim, bool keepdim=False) -> Tensor
inline at::Tensor Tensor::all(int64_t dim, bool keepdim) const {
    return at::_ops::all_dim::call(const_cast<Tensor&>(*this), dim, keepdim);
}

// aten::all.dims(Tensor self, int[]? dim=None, bool keepdim=False) -> Tensor
inline at::Tensor Tensor::all(at::OptionalIntArrayRef dim, bool keepdim) const {
    return at::_ops::all_dims::call(const_cast<Tensor&>(*this), dim, keepdim);
}

// aten::all.dimname(Tensor self, Dimname dim, bool keepdim=False) -> Tensor
inline at::Tensor Tensor::all(at::Dimname dim, bool keepdim) const {
    return at::_ops::all_dimname::call(const_cast<Tensor&>(*this), dim, keepdim);
}

// aten::allclose(Tensor self, Tensor other, float rtol=1e-05, float atol=1e-08, bool equal_nan=False) -> bool
inline bool Tensor::allclose(const at::Tensor & other, double rtol, double atol, bool equal_nan) const {
    return at::_ops::allclose::call(const_cast<Tensor&>(*this), other, rtol, atol, equal_nan);
}

// aten::any.dim(Tensor self, int dim, bool keepdim=False) -> Tensor
inline at::Tensor Tensor::any(int64_t dim, bool keepdim) const {
    return at::_ops::any_dim::call(const_cast<Tensor&>(*this), dim, keepdim);
}

// aten::any.dims(Tensor self, int[]? dim=None, bool keepdim=False) -> Tensor
inline at::Tensor Tensor::any(at::OptionalIntArrayRef dim, bool keepdim) const {
    return at::_ops::any_dims::call(const_cast<Tensor&>(*this), dim, keepdim);
}

// aten::any.dimname(Tensor self, Dimname dim, bool keepdim=False) -> Tensor
inline at::Tensor Tensor::any(at::Dimname dim, bool keepdim) const {
    return at::_ops::any_dimname::call(const_cast<Tensor&>(*this), dim, keepdim);
}

// aten::argmax(Tensor self, int? dim=None, bool keepdim=False) -> Tensor
inline at::Tensor Tensor::argmax(::std::optional<int64_t> dim, bool keepdim) const {
    return at::_ops::argmax::call(const_cast<Tensor&>(*this), dim, keepdim);
}

// aten::argmin(Tensor self, int? dim=None, bool keepdim=False) -> Tensor
inline at::Tensor Tensor::argmin(::std::optional<int64_t> dim, bool keepdim) const {
    return at::_ops::argmin::call(const_cast<Tensor&>(*this), dim, keepdim);
}

// aten::acosh(Tensor self) -> Tensor
inline at::Tensor Tensor::acosh() const {
    return at::_ops::acosh::call(const_cast<Tensor&>(*this));
}

// aten::acosh_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::acosh_() const {
    return at::_ops::acosh_::call(const_cast<Tensor&>(*this));
}

// aten::arccosh(Tensor self) -> Tensor
inline at::Tensor Tensor::arccosh() const {
    return at::_ops::arccosh::call(const_cast<Tensor&>(*this));
}

// aten::arccosh_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::arccosh_() const {
    return at::_ops::arccosh_::call(const_cast<Tensor&>(*this));
}

// aten::asinh(Tensor self) -> Tensor
inline at::Tensor Tensor::asinh() const {
    return at::_ops::asinh::call(const_cast<Tensor&>(*this));
}

// aten::asinh_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::asinh_() const {
    return at::_ops::asinh_::call(const_cast<Tensor&>(*this));
}

// aten::arcsinh(Tensor self) -> Tensor
inline at::Tensor Tensor::arcsinh() const {
    return at::_ops::arcsinh::call(const_cast<Tensor&>(*this));
}

// aten::arcsinh_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::arcsinh_() const {
    return at::_ops::arcsinh_::call(const_cast<Tensor&>(*this));
}

// aten::atanh(Tensor self) -> Tensor
inline at::Tensor Tensor::atanh() const {
    return at::_ops::atanh::call(const_cast<Tensor&>(*this));
}

// aten::atanh_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::atanh_() const {
    return at::_ops::atanh_::call(const_cast<Tensor&>(*this));
}

// aten::arctanh(Tensor self) -> Tensor
inline at::Tensor Tensor::arctanh() const {
    return at::_ops::arctanh::call(const_cast<Tensor&>(*this));
}

// aten::arctanh_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::arctanh_() const {
    return at::_ops::arctanh_::call(const_cast<Tensor&>(*this));
}

// aten::as_strided(Tensor(a) self, SymInt[] size, SymInt[] stride, SymInt? storage_offset=None) -> Tensor(a)
inline at::Tensor Tensor::as_strided(at::IntArrayRef size, at::IntArrayRef stride, ::std::optional<int64_t> storage_offset) const {
    return at::_ops::as_strided::call(const_cast<Tensor&>(*this), c10::fromIntArrayRefSlow(size), c10::fromIntArrayRefSlow(stride), storage_offset.has_value() ? ::std::make_optional(c10::SymInt(*storage_offset)) : ::std::nullopt);
}

// aten::as_strided(Tensor(a) self, SymInt[] size, SymInt[] stride, SymInt? storage_offset=None) -> Tensor(a)
inline at::Tensor Tensor::as_strided_symint(c10::SymIntArrayRef size, c10::SymIntArrayRef stride, ::std::optional<c10::SymInt> storage_offset) const {
    return at::_ops::as_strided::call(const_cast<Tensor&>(*this), size, stride, storage_offset);
}

// aten::as_strided_(Tensor(a!) self, SymInt[] size, SymInt[] stride, SymInt? storage_offset=None) -> Tensor(a!)
inline const at::Tensor & Tensor::as_strided_(at::IntArrayRef size, at::IntArrayRef stride, ::std::optional<int64_t> storage_offset) const {
    return at::_ops::as_strided_::call(const_cast<Tensor&>(*this), c10::fromIntArrayRefSlow(size), c10::fromIntArrayRefSlow(stride), storage_offset.has_value() ? ::std::make_optional(c10::SymInt(*storage_offset)) : ::std::nullopt);
}

// aten::as_strided_(Tensor(a!) self, SymInt[] size, SymInt[] stride, SymInt? storage_offset=None) -> Tensor(a!)
inline const at::Tensor & Tensor::as_strided__symint(c10::SymIntArrayRef size, c10::SymIntArrayRef stride, ::std::optional<c10::SymInt> storage_offset) const {
    return at::_ops::as_strided_::call(const_cast<Tensor&>(*this), size, stride, storage_offset);
}

// aten::asin(Tensor self) -> Tensor
inline at::Tensor Tensor::asin() const {
    return at::_ops::asin::call(const_cast<Tensor&>(*this));
}

// aten::asin_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::asin_() const {
    return at::_ops::asin_::call(const_cast<Tensor&>(*this));
}

// aten::arcsin(Tensor self) -> Tensor
inline at::Tensor Tensor::arcsin() const {
    return at::_ops::arcsin::call(const_cast<Tensor&>(*this));
}

// aten::arcsin_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::arcsin_() const {
    return at::_ops::arcsin_::call(const_cast<Tensor&>(*this));
}

// aten::atan(Tensor self) -> Tensor
inline at::Tensor Tensor::atan() const {
    return at::_ops::atan::call(const_cast<Tensor&>(*this));
}

// aten::atan_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::atan_() const {
    return at::_ops::atan_::call(const_cast<Tensor&>(*this));
}

// aten::arctan(Tensor self) -> Tensor
inline at::Tensor Tensor::arctan() const {
    return at::_ops::arctan::call(const_cast<Tensor&>(*this));
}

// aten::arctan_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::arctan_() const {
    return at::_ops::arctan_::call(const_cast<Tensor&>(*this));
}

// aten::baddbmm(Tensor self, Tensor batch1, Tensor batch2, *, Scalar beta=1, Scalar alpha=1) -> Tensor
inline at::Tensor Tensor::baddbmm(const at::Tensor & batch1, const at::Tensor & batch2, const at::Scalar & beta, const at::Scalar & alpha) const {
    return at::_ops::baddbmm::call(const_cast<Tensor&>(*this), batch1, batch2, beta, alpha);
}

// aten::baddbmm_(Tensor(a!) self, Tensor batch1, Tensor batch2, *, Scalar beta=1, Scalar alpha=1) -> Tensor(a!)
inline at::Tensor & Tensor::baddbmm_(const at::Tensor & batch1, const at::Tensor & batch2, const at::Scalar & beta, const at::Scalar & alpha) const {
    return at::_ops::baddbmm_::call(const_cast<Tensor&>(*this), batch1, batch2, beta, alpha);
}

// aten::bernoulli(Tensor self, *, Generator? generator=None) -> Tensor
inline at::Tensor Tensor::bernoulli(::std::optional<at::Generator> generator) const {
    return at::_ops::bernoulli::call(const_cast<Tensor&>(*this), generator);
}

// aten::bernoulli_.Tensor(Tensor(a!) self, Tensor p, *, Generator? generator=None) -> Tensor(a!)
inline at::Tensor & Tensor::bernoulli_(const at::Tensor & p, ::std::optional<at::Generator> generator) const {
    return at::_ops::bernoulli__Tensor::call(const_cast<Tensor&>(*this), p, generator);
}

// aten::bernoulli_.float(Tensor(a!) self, float p=0.5, *, Generator? generator=None) -> Tensor(a!)
inline at::Tensor & Tensor::bernoulli_(double p, ::std::optional<at::Generator> generator) const {
    return at::_ops::bernoulli__float::call(const_cast<Tensor&>(*this), p, generator);
}

// aten::bernoulli.p(Tensor self, float p, *, Generator? generator=None) -> Tensor
inline at::Tensor Tensor::bernoulli(double p, ::std::optional<at::Generator> generator) const {
    return at::_ops::bernoulli_p::call(const_cast<Tensor&>(*this), p, generator);
}

// aten::bincount(Tensor self, Tensor? weights=None, int minlength=0) -> Tensor
inline at::Tensor Tensor::bincount(const ::std::optional<at::Tensor> & weights, int64_t minlength) const {
    return at::_ops::bincount::call(const_cast<Tensor&>(*this), weights, minlength);
}

// aten::bitwise_not(Tensor self) -> Tensor
inline at::Tensor Tensor::bitwise_not() const {
    return at::_ops::bitwise_not::call(const_cast<Tensor&>(*this));
}

// aten::bitwise_not_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::bitwise_not_() const {
    return at::_ops::bitwise_not_::call(const_cast<Tensor&>(*this));
}

// aten::copysign.Tensor(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::copysign(const at::Tensor & other) const {
    return at::_ops::copysign_Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::copysign_.Tensor(Tensor(a!) self, Tensor other) -> Tensor(a!)
inline at::Tensor & Tensor::copysign_(const at::Tensor & other) const {
    return at::_ops::copysign__Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::copysign.Scalar(Tensor self, Scalar other) -> Tensor
inline at::Tensor Tensor::copysign(const at::Scalar & other) const {
    return at::_ops::copysign_Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::copysign_.Scalar(Tensor(a!) self, Scalar other) -> Tensor(a!)
inline at::Tensor & Tensor::copysign_(const at::Scalar & other) const {
    return at::_ops::copysign__Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::_lazy_clone(Tensor self) -> Tensor
inline at::Tensor Tensor::_lazy_clone() const {
    return at::_ops::_lazy_clone::call(const_cast<Tensor&>(*this));
}

// aten::logical_not(Tensor self) -> Tensor
inline at::Tensor Tensor::logical_not() const {
    return at::_ops::logical_not::call(const_cast<Tensor&>(*this));
}

// aten::logical_not_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::logical_not_() const {
    return at::_ops::logical_not_::call(const_cast<Tensor&>(*this));
}

// aten::logical_xor(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::logical_xor(const at::Tensor & other) const {
    return at::_ops::logical_xor::call(const_cast<Tensor&>(*this), other);
}

// aten::logical_xor_(Tensor(a!) self, Tensor other) -> Tensor(a!)
inline at::Tensor & Tensor::logical_xor_(const at::Tensor & other) const {
    return at::_ops::logical_xor_::call(const_cast<Tensor&>(*this), other);
}

// aten::logical_and(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::logical_and(const at::Tensor & other) const {
    return at::_ops::logical_and::call(const_cast<Tensor&>(*this), other);
}

// aten::logical_and_(Tensor(a!) self, Tensor other) -> Tensor(a!)
inline at::Tensor & Tensor::logical_and_(const at::Tensor & other) const {
    return at::_ops::logical_and_::call(const_cast<Tensor&>(*this), other);
}

// aten::logical_or(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::logical_or(const at::Tensor & other) const {
    return at::_ops::logical_or::call(const_cast<Tensor&>(*this), other);
}

// aten::logical_or_(Tensor(a!) self, Tensor other) -> Tensor(a!)
inline at::Tensor & Tensor::logical_or_(const at::Tensor & other) const {
    return at::_ops::logical_or_::call(const_cast<Tensor&>(*this), other);
}

// aten::bmm(Tensor self, Tensor mat2) -> Tensor
inline at::Tensor Tensor::bmm(const at::Tensor & mat2) const {
    return at::_ops::bmm::call(const_cast<Tensor&>(*this), mat2);
}

// aten::broadcast_to(Tensor(a) self, SymInt[] size) -> Tensor(a)
inline at::Tensor Tensor::broadcast_to(at::IntArrayRef size) const {
    return at::_ops::broadcast_to::call(const_cast<Tensor&>(*this), c10::fromIntArrayRefSlow(size));
}

// aten::broadcast_to(Tensor(a) self, SymInt[] size) -> Tensor(a)
inline at::Tensor Tensor::broadcast_to_symint(c10::SymIntArrayRef size) const {
    return at::_ops::broadcast_to::call(const_cast<Tensor&>(*this), size);
}

// aten::ceil(Tensor self) -> Tensor
inline at::Tensor Tensor::ceil() const {
    return at::_ops::ceil::call(const_cast<Tensor&>(*this));
}

// aten::ceil_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::ceil_() const {
    return at::_ops::ceil_::call(const_cast<Tensor&>(*this));
}

// aten::unsafe_chunk(Tensor self, int chunks, int dim=0) -> Tensor[]
inline ::std::vector<at::Tensor> Tensor::unsafe_chunk(int64_t chunks, int64_t dim) const {
    return at::_ops::unsafe_chunk::call(const_cast<Tensor&>(*this), chunks, dim);
}

// aten::chunk(Tensor(a -> *) self, int chunks, int dim=0) -> Tensor(a)[]
inline ::std::vector<at::Tensor> Tensor::chunk(int64_t chunks, int64_t dim) const {
    return at::_ops::chunk::call(const_cast<Tensor&>(*this), chunks, dim);
}

// aten::tensor_split.sections(Tensor(a -> *) self, SymInt sections, int dim=0) -> Tensor(a)[]
inline ::std::vector<at::Tensor> Tensor::tensor_split(int64_t sections, int64_t dim) const {
    return at::_ops::tensor_split_sections::call(const_cast<Tensor&>(*this), sections, dim);
}

// aten::tensor_split.sections(Tensor(a -> *) self, SymInt sections, int dim=0) -> Tensor(a)[]
inline ::std::vector<at::Tensor> Tensor::tensor_split_symint(c10::SymInt sections, int64_t dim) const {
    return at::_ops::tensor_split_sections::call(const_cast<Tensor&>(*this), sections, dim);
}

// aten::tensor_split.indices(Tensor(a -> *) self, SymInt[] indices, int dim=0) -> Tensor(a)[]
inline ::std::vector<at::Tensor> Tensor::tensor_split(at::IntArrayRef indices, int64_t dim) const {
    return at::_ops::tensor_split_indices::call(const_cast<Tensor&>(*this), c10::fromIntArrayRefSlow(indices), dim);
}

// aten::tensor_split.indices(Tensor(a -> *) self, SymInt[] indices, int dim=0) -> Tensor(a)[]
inline ::std::vector<at::Tensor> Tensor::tensor_split_symint(c10::SymIntArrayRef indices, int64_t dim) const {
    return at::_ops::tensor_split_indices::call(const_cast<Tensor&>(*this), indices, dim);
}

// aten::tensor_split.tensor_indices_or_sections(Tensor(a -> *) self, Tensor tensor_indices_or_sections, int dim=0) -> Tensor(a)[]
inline ::std::vector<at::Tensor> Tensor::tensor_split(const at::Tensor & tensor_indices_or_sections, int64_t dim) const {
    return at::_ops::tensor_split_tensor_indices_or_sections::call(const_cast<Tensor&>(*this), tensor_indices_or_sections, dim);
}

// aten::clamp(Tensor self, Scalar? min=None, Scalar? max=None) -> Tensor
inline at::Tensor Tensor::clamp(const ::std::optional<at::Scalar> & min, const ::std::optional<at::Scalar> & max) const {
    return at::_ops::clamp::call(const_cast<Tensor&>(*this), min, max);
}

// aten::clamp.Tensor(Tensor self, Tensor? min=None, Tensor? max=None) -> Tensor
inline at::Tensor Tensor::clamp(const ::std::optional<at::Tensor> & min, const ::std::optional<at::Tensor> & max) const {
    return at::_ops::clamp_Tensor::call(const_cast<Tensor&>(*this), min, max);
}

// aten::clamp_(Tensor(a!) self, Scalar? min=None, Scalar? max=None) -> Tensor(a!)
inline at::Tensor & Tensor::clamp_(const ::std::optional<at::Scalar> & min, const ::std::optional<at::Scalar> & max) const {
    return at::_ops::clamp_::call(const_cast<Tensor&>(*this), min, max);
}

// aten::clamp_.Tensor(Tensor(a!) self, Tensor? min=None, Tensor? max=None) -> Tensor(a!)
inline at::Tensor & Tensor::clamp_(const ::std::optional<at::Tensor> & min, const ::std::optional<at::Tensor> & max) const {
    return at::_ops::clamp__Tensor::call(const_cast<Tensor&>(*this), min, max);
}

// aten::clamp_max(Tensor self, Scalar max) -> Tensor
inline at::Tensor Tensor::clamp_max(const at::Scalar & max) const {
    return at::_ops::clamp_max::call(const_cast<Tensor&>(*this), max);
}

// aten::clamp_max.Tensor(Tensor self, Tensor max) -> Tensor
inline at::Tensor Tensor::clamp_max(const at::Tensor & max) const {
    return at::_ops::clamp_max_Tensor::call(const_cast<Tensor&>(*this), max);
}

// aten::clamp_max_(Tensor(a!) self, Scalar max) -> Tensor(a!)
inline at::Tensor & Tensor::clamp_max_(const at::Scalar & max) const {
    return at::_ops::clamp_max_::call(const_cast<Tensor&>(*this), max);
}

// aten::clamp_max_.Tensor(Tensor(a!) self, Tensor max) -> Tensor(a!)
inline at::Tensor & Tensor::clamp_max_(const at::Tensor & max) const {
    return at::_ops::clamp_max__Tensor::call(const_cast<Tensor&>(*this), max);
}

// aten::clamp_min(Tensor self, Scalar min) -> Tensor
inline at::Tensor Tensor::clamp_min(const at::Scalar & min) const {
    return at::_ops::clamp_min::call(const_cast<Tensor&>(*this), min);
}

// aten::clamp_min.Tensor(Tensor self, Tensor min) -> Tensor
inline at::Tensor Tensor::clamp_min(const at::Tensor & min) const {
    return at::_ops::clamp_min_Tensor::call(const_cast<Tensor&>(*this), min);
}

// aten::clamp_min_(Tensor(a!) self, Scalar min) -> Tensor(a!)
inline at::Tensor & Tensor::clamp_min_(const at::Scalar & min) const {
    return at::_ops::clamp_min_::call(const_cast<Tensor&>(*this), min);
}

// aten::clamp_min_.Tensor(Tensor(a!) self, Tensor min) -> Tensor(a!)
inline at::Tensor & Tensor::clamp_min_(const at::Tensor & min) const {
    return at::_ops::clamp_min__Tensor::call(const_cast<Tensor&>(*this), min);
}

// aten::clip(Tensor self, Scalar? min=None, Scalar? max=None) -> Tensor
inline at::Tensor Tensor::clip(const ::std::optional<at::Scalar> & min, const ::std::optional<at::Scalar> & max) const {
    return at::_ops::clip::call(const_cast<Tensor&>(*this), min, max);
}

// aten::clip.Tensor(Tensor self, Tensor? min=None, Tensor? max=None) -> Tensor
inline at::Tensor Tensor::clip(const ::std::optional<at::Tensor> & min, const ::std::optional<at::Tensor> & max) const {
    return at::_ops::clip_Tensor::call(const_cast<Tensor&>(*this), min, max);
}

// aten::clip_(Tensor(a!) self, Scalar? min=None, Scalar? max=None) -> Tensor(a!)
inline at::Tensor & Tensor::clip_(const ::std::optional<at::Scalar> & min, const ::std::optional<at::Scalar> & max) const {
    return at::_ops::clip_::call(const_cast<Tensor&>(*this), min, max);
}

// aten::clip_.Tensor(Tensor(a!) self, Tensor? min=None, Tensor? max=None) -> Tensor(a!)
inline at::Tensor & Tensor::clip_(const ::std::optional<at::Tensor> & min, const ::std::optional<at::Tensor> & max) const {
    return at::_ops::clip__Tensor::call(const_cast<Tensor&>(*this), min, max);
}

// aten::contiguous(Tensor(a) self, *, MemoryFormat memory_format=contiguous_format) -> Tensor(a)
inline at::Tensor Tensor::__dispatch_contiguous(at::MemoryFormat memory_format) const {
    return at::_ops::contiguous::call(const_cast<Tensor&>(*this), memory_format);
}

// aten::copy_(Tensor(a!) self, Tensor src, bool non_blocking=False) -> Tensor(a!)
inline at::Tensor & Tensor::copy_(const at::Tensor & src, bool non_blocking) const {
    return at::_ops::copy_::call(const_cast<Tensor&>(*this), src, non_blocking);
}

// aten::cos(Tensor self) -> Tensor
inline at::Tensor Tensor::cos() const {
    return at::_ops::cos::call(const_cast<Tensor&>(*this));
}

// aten::cos_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::cos_() const {
    return at::_ops::cos_::call(const_cast<Tensor&>(*this));
}

// aten::cosh(Tensor self) -> Tensor
inline at::Tensor Tensor::cosh() const {
    return at::_ops::cosh::call(const_cast<Tensor&>(*this));
}

// aten::cosh_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::cosh_() const {
    return at::_ops::cosh_::call(const_cast<Tensor&>(*this));
}

// aten::count_nonzero.dim_IntList(Tensor self, int[] dim) -> Tensor
inline at::Tensor Tensor::count_nonzero(at::IntArrayRef dim) const {
    return at::_ops::count_nonzero_dim_IntList::call(const_cast<Tensor&>(*this), dim);
}

// aten::count_nonzero(Tensor self, int? dim=None) -> Tensor
inline at::Tensor Tensor::count_nonzero(::std::optional<int64_t> dim) const {
    return at::_ops::count_nonzero::call(const_cast<Tensor&>(*this), dim);
}

// aten::cov(Tensor self, *, int correction=1, Tensor? fweights=None, Tensor? aweights=None) -> Tensor
inline at::Tensor Tensor::cov(int64_t correction, const ::std::optional<at::Tensor> & fweights, const ::std::optional<at::Tensor> & aweights) const {
    return at::_ops::cov::call(const_cast<Tensor&>(*this), correction, fweights, aweights);
}

// aten::corrcoef(Tensor self) -> Tensor
inline at::Tensor Tensor::corrcoef() const {
    return at::_ops::corrcoef::call(const_cast<Tensor&>(*this));
}

// aten::cummax(Tensor self, int dim) -> (Tensor values, Tensor indices)
inline ::std::tuple<at::Tensor,at::Tensor> Tensor::cummax(int64_t dim) const {
    return at::_ops::cummax::call(const_cast<Tensor&>(*this), dim);
}

// aten::cummax.dimname(Tensor self, Dimname dim) -> (Tensor values, Tensor indices)
inline ::std::tuple<at::Tensor,at::Tensor> Tensor::cummax(at::Dimname dim) const {
    return at::_ops::cummax_dimname::call(const_cast<Tensor&>(*this), dim);
}

// aten::cummin(Tensor self, int dim) -> (Tensor values, Tensor indices)
inline ::std::tuple<at::Tensor,at::Tensor> Tensor::cummin(int64_t dim) const {
    return at::_ops::cummin::call(const_cast<Tensor&>(*this), dim);
}

// aten::cummin.dimname(Tensor self, Dimname dim) -> (Tensor values, Tensor indices)
inline ::std::tuple<at::Tensor,at::Tensor> Tensor::cummin(at::Dimname dim) const {
    return at::_ops::cummin_dimname::call(const_cast<Tensor&>(*this), dim);
}

// aten::cumprod(Tensor self, int dim, *, ScalarType? dtype=None) -> Tensor
inline at::Tensor Tensor::cumprod(int64_t dim, ::std::optional<at::ScalarType> dtype) const {
    return at::_ops::cumprod::call(const_cast<Tensor&>(*this), dim, dtype);
}

// aten::cumprod_(Tensor(a!) self, int dim, *, ScalarType? dtype=None) -> Tensor(a!)
inline at::Tensor & Tensor::cumprod_(int64_t dim, ::std::optional<at::ScalarType> dtype) const {
    return at::_ops::cumprod_::call(const_cast<Tensor&>(*this), dim, dtype);
}

// aten::cumprod.dimname(Tensor self, Dimname dim, *, ScalarType? dtype=None) -> Tensor
inline at::Tensor Tensor::cumprod(at::Dimname dim, ::std::optional<at::ScalarType> dtype) const {
    return at::_ops::cumprod_dimname::call(const_cast<Tensor&>(*this), dim, dtype);
}

// aten::cumprod_.dimname(Tensor(a!) self, Dimname dim, *, ScalarType? dtype=None) -> Tensor(a!)
inline at::Tensor & Tensor::cumprod_(at::Dimname dim, ::std::optional<at::ScalarType> dtype) const {
    return at::_ops::cumprod__dimname::call(const_cast<Tensor&>(*this), dim, dtype);
}

// aten::cumsum(Tensor self, int dim, *, ScalarType? dtype=None) -> Tensor
inline at::Tensor Tensor::cumsum(int64_t dim, ::std::optional<at::ScalarType> dtype) const {
    return at::_ops::cumsum::call(const_cast<Tensor&>(*this), dim, dtype);
}

// aten::cumsum_(Tensor(a!) self, int dim, *, ScalarType? dtype=None) -> Tensor(a!)
inline at::Tensor & Tensor::cumsum_(int64_t dim, ::std::optional<at::ScalarType> dtype) const {
    return at::_ops::cumsum_::call(const_cast<Tensor&>(*this), dim, dtype);
}

// aten::cumsum.dimname(Tensor self, Dimname dim, *, ScalarType? dtype=None) -> Tensor
inline at::Tensor Tensor::cumsum(at::Dimname dim, ::std::optional<at::ScalarType> dtype) const {
    return at::_ops::cumsum_dimname::call(const_cast<Tensor&>(*this), dim, dtype);
}

// aten::cumsum_.dimname(Tensor(a!) self, Dimname dim, *, ScalarType? dtype=None) -> Tensor(a!)
inline at::Tensor & Tensor::cumsum_(at::Dimname dim, ::std::optional<at::ScalarType> dtype) const {
    return at::_ops::cumsum__dimname::call(const_cast<Tensor&>(*this), dim, dtype);
}

// aten::diag_embed(Tensor self, int offset=0, int dim1=-2, int dim2=-1) -> Tensor
inline at::Tensor Tensor::diag_embed(int64_t offset, int64_t dim1, int64_t dim2) const {
    return at::_ops::diag_embed::call(const_cast<Tensor&>(*this), offset, dim1, dim2);
}

// aten::diagflat(Tensor self, int offset=0) -> Tensor
inline at::Tensor Tensor::diagflat(int64_t offset) const {
    return at::_ops::diagflat::call(const_cast<Tensor&>(*this), offset);
}

// aten::diagonal(Tensor(a) self, int offset=0, int dim1=0, int dim2=1) -> Tensor(a)
inline at::Tensor Tensor::diagonal(int64_t offset, int64_t dim1, int64_t dim2) const {
    return at::_ops::diagonal::call(const_cast<Tensor&>(*this), offset, dim1, dim2);
}

// aten::diagonal.Dimname(Tensor(a) self, *, Dimname outdim, Dimname dim1, Dimname dim2, int offset=0) -> Tensor(a)
inline at::Tensor Tensor::diagonal(at::Dimname outdim, at::Dimname dim1, at::Dimname dim2, int64_t offset) const {
    return at::_ops::diagonal_Dimname::call(const_cast<Tensor&>(*this), outdim, dim1, dim2, offset);
}

// aten::fill_diagonal_(Tensor(a!) self, Scalar fill_value, bool wrap=False) -> Tensor(a!)
inline at::Tensor & Tensor::fill_diagonal_(const at::Scalar & fill_value, bool wrap) const {
    return at::_ops::fill_diagonal_::call(const_cast<Tensor&>(*this), fill_value, wrap);
}

// aten::diff(Tensor self, int n=1, int dim=-1, Tensor? prepend=None, Tensor? append=None) -> Tensor
inline at::Tensor Tensor::diff(int64_t n, int64_t dim, const ::std::optional<at::Tensor> & prepend, const ::std::optional<at::Tensor> & append) const {
    return at::_ops::diff::call(const_cast<Tensor&>(*this), n, dim, prepend, append);
}

// aten::div.Tensor(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::div(const at::Tensor & other) const {
    return at::_ops::div_Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::div_.Tensor(Tensor(a!) self, Tensor other) -> Tensor(a!)
inline at::Tensor & Tensor::div_(const at::Tensor & other) const {
    return at::_ops::div__Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::div.Tensor_mode(Tensor self, Tensor other, *, str? rounding_mode) -> Tensor
inline at::Tensor Tensor::div(const at::Tensor & other, ::std::optional<c10::string_view> rounding_mode) const {
    return at::_ops::div_Tensor_mode::call(const_cast<Tensor&>(*this), other, rounding_mode);
}

// aten::div_.Tensor_mode(Tensor(a!) self, Tensor other, *, str? rounding_mode) -> Tensor(a!)
inline at::Tensor & Tensor::div_(const at::Tensor & other, ::std::optional<c10::string_view> rounding_mode) const {
    return at::_ops::div__Tensor_mode::call(const_cast<Tensor&>(*this), other, rounding_mode);
}

// aten::div.Scalar(Tensor self, Scalar other) -> Tensor
inline at::Tensor Tensor::div(const at::Scalar & other) const {
    return at::_ops::div_Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::div_.Scalar(Tensor(a!) self, Scalar other) -> Tensor(a!)
inline at::Tensor & Tensor::div_(const at::Scalar & other) const {
    return at::_ops::div__Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::div.Scalar_mode(Tensor self, Scalar other, *, str? rounding_mode) -> Tensor
inline at::Tensor Tensor::div(const at::Scalar & other, ::std::optional<c10::string_view> rounding_mode) const {
    return at::_ops::div_Scalar_mode::call(const_cast<Tensor&>(*this), other, rounding_mode);
}

// aten::div_.Scalar_mode(Tensor(a!) self, Scalar other, *, str? rounding_mode) -> Tensor(a!)
inline at::Tensor & Tensor::div_(const at::Scalar & other, ::std::optional<c10::string_view> rounding_mode) const {
    return at::_ops::div__Scalar_mode::call(const_cast<Tensor&>(*this), other, rounding_mode);
}

// aten::divide.Tensor(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::divide(const at::Tensor & other) const {
    return at::_ops::divide_Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::divide_.Tensor(Tensor(a!) self, Tensor other) -> Tensor(a!)
inline at::Tensor & Tensor::divide_(const at::Tensor & other) const {
    return at::_ops::divide__Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::divide.Scalar(Tensor self, Scalar other) -> Tensor
inline at::Tensor Tensor::divide(const at::Scalar & other) const {
    return at::_ops::divide_Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::divide_.Scalar(Tensor(a!) self, Scalar other) -> Tensor(a!)
inline at::Tensor & Tensor::divide_(const at::Scalar & other) const {
    return at::_ops::divide__Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::divide.Tensor_mode(Tensor self, Tensor other, *, str? rounding_mode) -> Tensor
inline at::Tensor Tensor::divide(const at::Tensor & other, ::std::optional<c10::string_view> rounding_mode) const {
    return at::_ops::divide_Tensor_mode::call(const_cast<Tensor&>(*this), other, rounding_mode);
}

// aten::divide_.Tensor_mode(Tensor(a!) self, Tensor other, *, str? rounding_mode) -> Tensor(a!)
inline at::Tensor & Tensor::divide_(const at::Tensor & other, ::std::optional<c10::string_view> rounding_mode) const {
    return at::_ops::divide__Tensor_mode::call(const_cast<Tensor&>(*this), other, rounding_mode);
}

// aten::divide.Scalar_mode(Tensor self, Scalar other, *, str? rounding_mode) -> Tensor
inline at::Tensor Tensor::divide(const at::Scalar & other, ::std::optional<c10::string_view> rounding_mode) const {
    return at::_ops::divide_Scalar_mode::call(const_cast<Tensor&>(*this), other, rounding_mode);
}

// aten::divide_.Scalar_mode(Tensor(a!) self, Scalar other, *, str? rounding_mode) -> Tensor(a!)
inline at::Tensor & Tensor::divide_(const at::Scalar & other, ::std::optional<c10::string_view> rounding_mode) const {
    return at::_ops::divide__Scalar_mode::call(const_cast<Tensor&>(*this), other, rounding_mode);
}

// aten::true_divide.Tensor(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::true_divide(const at::Tensor & other) const {
    return at::_ops::true_divide_Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::true_divide_.Tensor(Tensor(a!) self, Tensor other) -> Tensor(a!)
inline at::Tensor & Tensor::true_divide_(const at::Tensor & other) const {
    return at::_ops::true_divide__Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::true_divide.Scalar(Tensor self, Scalar other) -> Tensor
inline at::Tensor Tensor::true_divide(const at::Scalar & other) const {
    return at::_ops::true_divide_Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::true_divide_.Scalar(Tensor(a!) self, Scalar other) -> Tensor(a!)
inline at::Tensor & Tensor::true_divide_(const at::Scalar & other) const {
    return at::_ops::true_divide__Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::dot(Tensor self, Tensor tensor) -> Tensor
inline at::Tensor Tensor::dot(const at::Tensor & tensor) const {
    return at::_ops::dot::call(const_cast<Tensor&>(*this), tensor);
}

// aten::vdot(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::vdot(const at::Tensor & other) const {
    return at::_ops::vdot::call(const_cast<Tensor&>(*this), other);
}

// aten::new_empty(Tensor self, SymInt[] size, *, ScalarType? dtype=None, Layout? layout=None, Device? device=None, bool? pin_memory=None) -> Tensor
inline at::Tensor Tensor::new_empty(at::IntArrayRef size, at::TensorOptions options) const {
    return at::_ops::new_empty::call(const_cast<Tensor&>(*this), c10::fromIntArrayRefSlow(size), c10::optTypeMetaToScalarType(options.dtype_opt()), options.layout_opt(), options.device_opt(), options.pinned_memory_opt());
}

// aten::new_empty(Tensor self, SymInt[] size, *, ScalarType? dtype=None, Layout? layout=None, Device? device=None, bool? pin_memory=None) -> Tensor
inline at::Tensor Tensor::new_empty(at::IntArrayRef size, ::std::optional<at::ScalarType> dtype, ::std::optional<at::Layout> layout, ::std::optional<at::Device> device, ::std::optional<bool> pin_memory) const {
    return at::_ops::new_empty::call(const_cast<Tensor&>(*this), c10::fromIntArrayRefSlow(size), dtype, layout, device, pin_memory);
}

// aten::new_empty(Tensor self, SymInt[] size, *, ScalarType? dtype=None, Layout? layout=None, Device? device=None, bool? pin_memory=None) -> Tensor
inline at::Tensor Tensor::new_empty_symint(c10::SymIntArrayRef size, at::TensorOptions options) const {
    return at::_ops::new_empty::call(const_cast<Tensor&>(*this), size, c10::optTypeMetaToScalarType(options.dtype_opt()), options.layout_opt(), options.device_opt(), options.pinned_memory_opt());
}

// aten::new_empty(Tensor self, SymInt[] size, *, ScalarType? dtype=None, Layout? layout=None, Device? device=None, bool? pin_memory=None) -> Tensor
inline at::Tensor Tensor::new_empty_symint(c10::SymIntArrayRef size, ::std::optional<at::ScalarType> dtype, ::std::optional<at::Layout> layout, ::std::optional<at::Device> device, ::std::optional<bool> pin_memory) const {
    return at::_ops::new_empty::call(const_cast<Tensor&>(*this), size, dtype, layout, device, pin_memory);
}

// aten::new_empty_strided(Tensor self, SymInt[] size, SymInt[] stride, *, ScalarType? dtype=None, Layout? layout=None, Device? device=None, bool? pin_memory=None) -> Tensor
inline at::Tensor Tensor::new_empty_strided(at::IntArrayRef size, at::IntArrayRef stride, at::TensorOptions options) const {
    return at::_ops::new_empty_strided::call(const_cast<Tensor&>(*this), c10::fromIntArrayRefSlow(size), c10::fromIntArrayRefSlow(stride), c10::optTypeMetaToScalarType(options.dtype_opt()), options.layout_opt(), options.device_opt(), options.pinned_memory_opt());
}

// aten::new_empty_strided(Tensor self, SymInt[] size, SymInt[] stride, *, ScalarType? dtype=None, Layout? layout=None, Device? device=None, bool? pin_memory=None) -> Tensor
inline at::Tensor Tensor::new_empty_strided(at::IntArrayRef size, at::IntArrayRef stride, ::std::optional<at::ScalarType> dtype, ::std::optional<at::Layout> layout, ::std::optional<at::Device> device, ::std::optional<bool> pin_memory) const {
    return at::_ops::new_empty_strided::call(const_cast<Tensor&>(*this), c10::fromIntArrayRefSlow(size), c10::fromIntArrayRefSlow(stride), dtype, layout, device, pin_memory);
}

// aten::new_empty_strided(Tensor self, SymInt[] size, SymInt[] stride, *, ScalarType? dtype=None, Layout? layout=None, Device? device=None, bool? pin_memory=None) -> Tensor
inline at::Tensor Tensor::new_empty_strided_symint(c10::SymIntArrayRef size, c10::SymIntArrayRef stride, at::TensorOptions options) const {
    return at::_ops::new_empty_strided::call(const_cast<Tensor&>(*this), size, stride, c10::optTypeMetaToScalarType(options.dtype_opt()), options.layout_opt(), options.device_opt(), options.pinned_memory_opt());
}

// aten::new_empty_strided(Tensor self, SymInt[] size, SymInt[] stride, *, ScalarType? dtype=None, Layout? layout=None, Device? device=None, bool? pin_memory=None) -> Tensor
inline at::Tensor Tensor::new_empty_strided_symint(c10::SymIntArrayRef size, c10::SymIntArrayRef stride, ::std::optional<at::ScalarType> dtype, ::std::optional<at::Layout> layout, ::std::optional<at::Device> device, ::std::optional<bool> pin_memory) const {
    return at::_ops::new_empty_strided::call(const_cast<Tensor&>(*this), size, stride, dtype, layout, device, pin_memory);
}

// aten::new_full(Tensor self, SymInt[] size, Scalar fill_value, *, ScalarType? dtype=None, Layout? layout=None, Device? device=None, bool? pin_memory=None) -> Tensor
inline at::Tensor Tensor::new_full(at::IntArrayRef size, const at::Scalar & fill_value, at::TensorOptions options) const {
    return at::_ops::new_full::call(const_cast<Tensor&>(*this), c10::fromIntArrayRefSlow(size), fill_value, c10::optTypeMetaToScalarType(options.dtype_opt()), options.layout_opt(), options.device_opt(), options.pinned_memory_opt());
}

// aten::new_full(Tensor self, SymInt[] size, Scalar fill_value, *, ScalarType? dtype=None, Layout? layout=None, Device? device=None, bool? pin_memory=None) -> Tensor
inline at::Tensor Tensor::new_full(at::IntArrayRef size, const at::Scalar & fill_value, ::std::optional<at::ScalarType> dtype, ::std::optional<at::Layout> layout, ::std::optional<at::Device> device, ::std::optional<bool> pin_memory) const {
    return at::_ops::new_full::call(const_cast<Tensor&>(*this), c10::fromIntArrayRefSlow(size), fill_value, dtype, layout, device, pin_memory);
}

// aten::new_full(Tensor self, SymInt[] size, Scalar fill_value, *, ScalarType? dtype=None, Layout? layout=None, Device? device=None, bool? pin_memory=None) -> Tensor
inline at::Tensor Tensor::new_full_symint(c10::SymIntArrayRef size, const at::Scalar & fill_value, at::TensorOptions options) const {
    return at::_ops::new_full::call(const_cast<Tensor&>(*this), size, fill_value, c10::optTypeMetaToScalarType(options.dtype_opt()), options.layout_opt(), options.device_opt(), options.pinned_memory_opt());
}

// aten::new_full(Tensor self, SymInt[] size, Scalar fill_value, *, ScalarType? dtype=None, Layout? layout=None, Device? device=None, bool? pin_memory=None) -> Tensor
inline at::Tensor Tensor::new_full_symint(c10::SymIntArrayRef size, const at::Scalar & fill_value, ::std::optional<at::ScalarType> dtype, ::std::optional<at::Layout> layout, ::std::optional<at::Device> device, ::std::optional<bool> pin_memory) const {
    return at::_ops::new_full::call(const_cast<Tensor&>(*this), size, fill_value, dtype, layout, device, pin_memory);
}

// aten::new_zeros(Tensor self, SymInt[] size, *, ScalarType? dtype=None, Layout? layout=None, Device? device=None, bool? pin_memory=None) -> Tensor
inline at::Tensor Tensor::new_zeros(at::IntArrayRef size, at::TensorOptions options) const {
    return at::_ops::new_zeros::call(const_cast<Tensor&>(*this), c10::fromIntArrayRefSlow(size), c10::optTypeMetaToScalarType(options.dtype_opt()), options.layout_opt(), options.device_opt(), options.pinned_memory_opt());
}

// aten::new_zeros(Tensor self, SymInt[] size, *, ScalarType? dtype=None, Layout? layout=None, Device? device=None, bool? pin_memory=None) -> Tensor
inline at::Tensor Tensor::new_zeros(at::IntArrayRef size, ::std::optional<at::ScalarType> dtype, ::std::optional<at::Layout> layout, ::std::optional<at::Device> device, ::std::optional<bool> pin_memory) const {
    return at::_ops::new_zeros::call(const_cast<Tensor&>(*this), c10::fromIntArrayRefSlow(size), dtype, layout, device, pin_memory);
}

// aten::new_zeros(Tensor self, SymInt[] size, *, ScalarType? dtype=None, Layout? layout=None, Device? device=None, bool? pin_memory=None) -> Tensor
inline at::Tensor Tensor::new_zeros_symint(c10::SymIntArrayRef size, at::TensorOptions options) const {
    return at::_ops::new_zeros::call(const_cast<Tensor&>(*this), size, c10::optTypeMetaToScalarType(options.dtype_opt()), options.layout_opt(), options.device_opt(), options.pinned_memory_opt());
}

// aten::new_zeros(Tensor self, SymInt[] size, *, ScalarType? dtype=None, Layout? layout=None, Device? device=None, bool? pin_memory=None) -> Tensor
inline at::Tensor Tensor::new_zeros_symint(c10::SymIntArrayRef size, ::std::optional<at::ScalarType> dtype, ::std::optional<at::Layout> layout, ::std::optional<at::Device> device, ::std::optional<bool> pin_memory) const {
    return at::_ops::new_zeros::call(const_cast<Tensor&>(*this), size, dtype, layout, device, pin_memory);
}

// aten::new_ones(Tensor self, SymInt[] size, *, ScalarType? dtype=None, Layout? layout=None, Device? device=None, bool? pin_memory=None) -> Tensor
inline at::Tensor Tensor::new_ones(at::IntArrayRef size, at::TensorOptions options) const {
    return at::_ops::new_ones::call(const_cast<Tensor&>(*this), c10::fromIntArrayRefSlow(size), c10::optTypeMetaToScalarType(options.dtype_opt()), options.layout_opt(), options.device_opt(), options.pinned_memory_opt());
}

// aten::new_ones(Tensor self, SymInt[] size, *, ScalarType? dtype=None, Layout? layout=None, Device? device=None, bool? pin_memory=None) -> Tensor
inline at::Tensor Tensor::new_ones(at::IntArrayRef size, ::std::optional<at::ScalarType> dtype, ::std::optional<at::Layout> layout, ::std::optional<at::Device> device, ::std::optional<bool> pin_memory) const {
    return at::_ops::new_ones::call(const_cast<Tensor&>(*this), c10::fromIntArrayRefSlow(size), dtype, layout, device, pin_memory);
}

// aten::new_ones(Tensor self, SymInt[] size, *, ScalarType? dtype=None, Layout? layout=None, Device? device=None, bool? pin_memory=None) -> Tensor
inline at::Tensor Tensor::new_ones_symint(c10::SymIntArrayRef size, at::TensorOptions options) const {
    return at::_ops::new_ones::call(const_cast<Tensor&>(*this), size, c10::optTypeMetaToScalarType(options.dtype_opt()), options.layout_opt(), options.device_opt(), options.pinned_memory_opt());
}

// aten::new_ones(Tensor self, SymInt[] size, *, ScalarType? dtype=None, Layout? layout=None, Device? device=None, bool? pin_memory=None) -> Tensor
inline at::Tensor Tensor::new_ones_symint(c10::SymIntArrayRef size, ::std::optional<at::ScalarType> dtype, ::std::optional<at::Layout> layout, ::std::optional<at::Device> device, ::std::optional<bool> pin_memory) const {
    return at::_ops::new_ones::call(const_cast<Tensor&>(*this), size, dtype, layout, device, pin_memory);
}

// aten::resize_(Tensor(a!) self, SymInt[] size, *, MemoryFormat? memory_format=None) -> Tensor(a!)
inline const at::Tensor & Tensor::resize_(at::IntArrayRef size, ::std::optional<at::MemoryFormat> memory_format) const {
    return at::_ops::resize_::call(const_cast<Tensor&>(*this), c10::fromIntArrayRefSlow(size), memory_format);
}

// aten::resize_(Tensor(a!) self, SymInt[] size, *, MemoryFormat? memory_format=None) -> Tensor(a!)
inline const at::Tensor & Tensor::resize__symint(c10::SymIntArrayRef size, ::std::optional<at::MemoryFormat> memory_format) const {
    return at::_ops::resize_::call(const_cast<Tensor&>(*this), size, memory_format);
}

// aten::erf(Tensor self) -> Tensor
inline at::Tensor Tensor::erf() const {
    return at::_ops::erf::call(const_cast<Tensor&>(*this));
}

// aten::erf_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::erf_() const {
    return at::_ops::erf_::call(const_cast<Tensor&>(*this));
}

// aten::erfc(Tensor self) -> Tensor
inline at::Tensor Tensor::erfc() const {
    return at::_ops::erfc::call(const_cast<Tensor&>(*this));
}

// aten::erfc_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::erfc_() const {
    return at::_ops::erfc_::call(const_cast<Tensor&>(*this));
}

// aten::exp(Tensor self) -> Tensor
inline at::Tensor Tensor::exp() const {
    return at::_ops::exp::call(const_cast<Tensor&>(*this));
}

// aten::exp_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::exp_() const {
    return at::_ops::exp_::call(const_cast<Tensor&>(*this));
}

// aten::exp2(Tensor self) -> Tensor
inline at::Tensor Tensor::exp2() const {
    return at::_ops::exp2::call(const_cast<Tensor&>(*this));
}

// aten::exp2_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::exp2_() const {
    return at::_ops::exp2_::call(const_cast<Tensor&>(*this));
}

// aten::expm1(Tensor self) -> Tensor
inline at::Tensor Tensor::expm1() const {
    return at::_ops::expm1::call(const_cast<Tensor&>(*this));
}

// aten::expm1_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::expm1_() const {
    return at::_ops::expm1_::call(const_cast<Tensor&>(*this));
}

// aten::expand(Tensor(a) self, SymInt[] size, *, bool implicit=False) -> Tensor(a)
inline at::Tensor Tensor::expand(at::IntArrayRef size, bool implicit) const {
    return at::_ops::expand::call(const_cast<Tensor&>(*this), c10::fromIntArrayRefSlow(size), implicit);
}

// aten::expand(Tensor(a) self, SymInt[] size, *, bool implicit=False) -> Tensor(a)
inline at::Tensor Tensor::expand_symint(c10::SymIntArrayRef size, bool implicit) const {
    return at::_ops::expand::call(const_cast<Tensor&>(*this), size, implicit);
}

// aten::expand_as(Tensor(a) self, Tensor other) -> Tensor(a)
inline at::Tensor Tensor::expand_as(const at::Tensor & other) const {
    return at::_ops::expand_as::call(const_cast<Tensor&>(*this), other);
}

// aten::flatten.using_ints(Tensor(a) self, int start_dim=0, int end_dim=-1) -> Tensor(a)
inline at::Tensor Tensor::flatten(int64_t start_dim, int64_t end_dim) const {
    return at::_ops::flatten_using_ints::call(const_cast<Tensor&>(*this), start_dim, end_dim);
}

// aten::flatten.named_out_dim(Tensor(a) self, int start_dim, int end_dim, Dimname out_dim) -> Tensor(a)
inline at::Tensor Tensor::flatten(int64_t start_dim, int64_t end_dim, at::Dimname out_dim) const {
    return at::_ops::flatten_named_out_dim::call(const_cast<Tensor&>(*this), start_dim, end_dim, out_dim);
}

// aten::flatten.using_names(Tensor(a) self, Dimname start_dim, Dimname end_dim, Dimname out_dim) -> Tensor(a)
inline at::Tensor Tensor::flatten(at::Dimname start_dim, at::Dimname end_dim, at::Dimname out_dim) const {
    return at::_ops::flatten_using_names::call(const_cast<Tensor&>(*this), start_dim, end_dim, out_dim);
}

// aten::flatten.DimnameList(Tensor(a) self, Dimname[] dims, Dimname out_dim) -> Tensor(a)
inline at::Tensor Tensor::flatten(at::DimnameList dims, at::Dimname out_dim) const {
    return at::_ops::flatten_DimnameList::call(const_cast<Tensor&>(*this), dims, out_dim);
}

// aten::unflatten.int(Tensor(a) self, int dim, SymInt[] sizes) -> Tensor(a)
inline at::Tensor Tensor::unflatten(int64_t dim, at::IntArrayRef sizes) const {
    return at::_ops::unflatten_int::call(const_cast<Tensor&>(*this), dim, c10::fromIntArrayRefSlow(sizes));
}

// aten::unflatten.int(Tensor(a) self, int dim, SymInt[] sizes) -> Tensor(a)
inline at::Tensor Tensor::unflatten_symint(int64_t dim, c10::SymIntArrayRef sizes) const {
    return at::_ops::unflatten_int::call(const_cast<Tensor&>(*this), dim, sizes);
}

// aten::unflatten.Dimname(Tensor(a) self, Dimname dim, SymInt[] sizes, Dimname[] names) -> Tensor(a)
inline at::Tensor Tensor::unflatten(at::Dimname dim, at::IntArrayRef sizes, at::DimnameList names) const {
    return at::_ops::unflatten_Dimname::call(const_cast<Tensor&>(*this), dim, c10::fromIntArrayRefSlow(sizes), names);
}

// aten::unflatten.Dimname(Tensor(a) self, Dimname dim, SymInt[] sizes, Dimname[] names) -> Tensor(a)
inline at::Tensor Tensor::unflatten_symint(at::Dimname dim, c10::SymIntArrayRef sizes, at::DimnameList names) const {
    return at::_ops::unflatten_Dimname::call(const_cast<Tensor&>(*this), dim, sizes, names);
}

// aten::fill_.Scalar(Tensor(a!) self, Scalar value) -> Tensor(a!)
inline at::Tensor & Tensor::fill_(const at::Scalar & value) const {
    return at::_ops::fill__Scalar::call(const_cast<Tensor&>(*this), value);
}

// aten::fill_.Tensor(Tensor(a!) self, Tensor value) -> Tensor(a!)
inline at::Tensor & Tensor::fill_(const at::Tensor & value) const {
    return at::_ops::fill__Tensor::call(const_cast<Tensor&>(*this), value);
}

// aten::floor(Tensor self) -> Tensor
inline at::Tensor Tensor::floor() const {
    return at::_ops::floor::call(const_cast<Tensor&>(*this));
}

// aten::floor_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::floor_() const {
    return at::_ops::floor_::call(const_cast<Tensor&>(*this));
}

// aten::floor_divide(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::floor_divide(const at::Tensor & other) const {
    return at::_ops::floor_divide::call(const_cast<Tensor&>(*this), other);
}

// aten::floor_divide_.Tensor(Tensor(a!) self, Tensor other) -> Tensor(a!)
inline at::Tensor & Tensor::floor_divide_(const at::Tensor & other) const {
    return at::_ops::floor_divide__Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::floor_divide.Scalar(Tensor self, Scalar other) -> Tensor
inline at::Tensor Tensor::floor_divide(const at::Scalar & other) const {
    return at::_ops::floor_divide_Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::floor_divide_.Scalar(Tensor(a!) self, Scalar other) -> Tensor(a!)
inline at::Tensor & Tensor::floor_divide_(const at::Scalar & other) const {
    return at::_ops::floor_divide__Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::frac(Tensor self) -> Tensor
inline at::Tensor Tensor::frac() const {
    return at::_ops::frac::call(const_cast<Tensor&>(*this));
}

// aten::frac_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::frac_() const {
    return at::_ops::frac_::call(const_cast<Tensor&>(*this));
}

// aten::gcd(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::gcd(const at::Tensor & other) const {
    return at::_ops::gcd::call(const_cast<Tensor&>(*this), other);
}

// aten::gcd_(Tensor(a!) self, Tensor other) -> Tensor(a!)
inline at::Tensor & Tensor::gcd_(const at::Tensor & other) const {
    return at::_ops::gcd_::call(const_cast<Tensor&>(*this), other);
}

// aten::lcm(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::lcm(const at::Tensor & other) const {
    return at::_ops::lcm::call(const_cast<Tensor&>(*this), other);
}

// aten::lcm_(Tensor(a!) self, Tensor other) -> Tensor(a!)
inline at::Tensor & Tensor::lcm_(const at::Tensor & other) const {
    return at::_ops::lcm_::call(const_cast<Tensor&>(*this), other);
}

// aten::index.Tensor(Tensor self, Tensor?[] indices) -> Tensor
inline at::Tensor Tensor::index(const c10::List<::std::optional<at::Tensor>> & indices) const {
    return at::_ops::index_Tensor::call(const_cast<Tensor&>(*this), indices);
}

// aten::index_copy_(Tensor(a!) self, int dim, Tensor index, Tensor source) -> Tensor(a!)
inline at::Tensor & Tensor::index_copy_(int64_t dim, const at::Tensor & index, const at::Tensor & source) const {
    return at::_ops::index_copy_::call(const_cast<Tensor&>(*this), dim, index, source);
}

// aten::index_copy(Tensor self, int dim, Tensor index, Tensor source) -> Tensor
inline at::Tensor Tensor::index_copy(int64_t dim, const at::Tensor & index, const at::Tensor & source) const {
    return at::_ops::index_copy::call(const_cast<Tensor&>(*this), dim, index, source);
}

// aten::index_copy_.dimname(Tensor(a!) self, Dimname dim, Tensor index, Tensor source) -> Tensor(a!)
inline at::Tensor & Tensor::index_copy_(at::Dimname dim, const at::Tensor & index, const at::Tensor & source) const {
    return at::_ops::index_copy__dimname::call(const_cast<Tensor&>(*this), dim, index, source);
}

// aten::index_copy.dimname(Tensor self, Dimname dim, Tensor index, Tensor source) -> Tensor
inline at::Tensor Tensor::index_copy(at::Dimname dim, const at::Tensor & index, const at::Tensor & source) const {
    return at::_ops::index_copy_dimname::call(const_cast<Tensor&>(*this), dim, index, source);
}

// aten::index_put_(Tensor(a!) self, Tensor?[] indices, Tensor values, bool accumulate=False) -> Tensor(a!)
inline at::Tensor & Tensor::index_put_(const c10::List<::std::optional<at::Tensor>> & indices, const at::Tensor & values, bool accumulate) const {
    return at::_ops::index_put_::call(const_cast<Tensor&>(*this), indices, values, accumulate);
}

// aten::index_put(Tensor self, Tensor?[] indices, Tensor values, bool accumulate=False) -> Tensor
inline at::Tensor Tensor::index_put(const c10::List<::std::optional<at::Tensor>> & indices, const at::Tensor & values, bool accumulate) const {
    return at::_ops::index_put::call(const_cast<Tensor&>(*this), indices, values, accumulate);
}

// aten::isclose(Tensor self, Tensor other, float rtol=1e-05, float atol=1e-08, bool equal_nan=False) -> Tensor
inline at::Tensor Tensor::isclose(const at::Tensor & other, double rtol, double atol, bool equal_nan) const {
    return at::_ops::isclose::call(const_cast<Tensor&>(*this), other, rtol, atol, equal_nan);
}

// aten::isnan(Tensor self) -> Tensor
inline at::Tensor Tensor::isnan() const {
    return at::_ops::isnan::call(const_cast<Tensor&>(*this));
}

// aten::is_distributed(Tensor self) -> bool
inline bool Tensor::is_distributed() const {
    return at::_ops::is_distributed::call(const_cast<Tensor&>(*this));
}

// aten::is_floating_point(Tensor self) -> bool
inline bool Tensor::__dispatch_is_floating_point() const {
    return at::_ops::is_floating_point::call(const_cast<Tensor&>(*this));
}

// aten::is_complex(Tensor self) -> bool
inline bool Tensor::__dispatch_is_complex() const {
    return at::_ops::is_complex::call(const_cast<Tensor&>(*this));
}

// aten::is_conj(Tensor self) -> bool
inline bool Tensor::__dispatch_is_conj() const {
    return at::_ops::is_conj::call(const_cast<Tensor&>(*this));
}

// aten::_is_zerotensor(Tensor self) -> bool
inline bool Tensor::__dispatch__is_zerotensor() const {
    return at::_ops::_is_zerotensor::call(const_cast<Tensor&>(*this));
}

// aten::is_neg(Tensor self) -> bool
inline bool Tensor::__dispatch_is_neg() const {
    return at::_ops::is_neg::call(const_cast<Tensor&>(*this));
}

// aten::isreal(Tensor self) -> Tensor
inline at::Tensor Tensor::isreal() const {
    return at::_ops::isreal::call(const_cast<Tensor&>(*this));
}

// aten::is_nonzero(Tensor self) -> bool
inline bool Tensor::is_nonzero() const {
    return at::_ops::is_nonzero::call(const_cast<Tensor&>(*this));
}

// aten::is_same_size(Tensor self, Tensor other) -> bool
inline bool Tensor::is_same_size(const at::Tensor & other) const {
    return at::_ops::is_same_size::call(const_cast<Tensor&>(*this), other);
}

// aten::is_signed(Tensor self) -> bool
inline bool Tensor::__dispatch_is_signed() const {
    return at::_ops::is_signed::call(const_cast<Tensor&>(*this));
}

// aten::is_inference(Tensor self) -> bool
inline bool Tensor::__dispatch_is_inference() const {
    return at::_ops::is_inference::call(const_cast<Tensor&>(*this));
}

// aten::kron(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::kron(const at::Tensor & other) const {
    return at::_ops::kron::call(const_cast<Tensor&>(*this), other);
}

// aten::kthvalue(Tensor self, int k, int dim=-1, bool keepdim=False) -> (Tensor values, Tensor indices)
inline ::std::tuple<at::Tensor,at::Tensor> Tensor::kthvalue(int64_t k, int64_t dim, bool keepdim) const {
    return at::_ops::kthvalue::call(const_cast<Tensor&>(*this), k, dim, keepdim);
}

// aten::kthvalue.dimname(Tensor self, int k, Dimname dim, bool keepdim=False) -> (Tensor values, Tensor indices)
inline ::std::tuple<at::Tensor,at::Tensor> Tensor::kthvalue(int64_t k, at::Dimname dim, bool keepdim) const {
    return at::_ops::kthvalue_dimname::call(const_cast<Tensor&>(*this), k, dim, keepdim);
}

// aten::nan_to_num(Tensor self, float? nan=None, float? posinf=None, float? neginf=None) -> Tensor
inline at::Tensor Tensor::nan_to_num(::std::optional<double> nan, ::std::optional<double> posinf, ::std::optional<double> neginf) const {
    return at::_ops::nan_to_num::call(const_cast<Tensor&>(*this), nan, posinf, neginf);
}

// aten::nan_to_num_(Tensor(a!) self, float? nan=None, float? posinf=None, float? neginf=None) -> Tensor(a!)
inline at::Tensor & Tensor::nan_to_num_(::std::optional<double> nan, ::std::optional<double> posinf, ::std::optional<double> neginf) const {
    return at::_ops::nan_to_num_::call(const_cast<Tensor&>(*this), nan, posinf, neginf);
}

// aten::ldexp.Tensor(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::ldexp(const at::Tensor & other) const {
    return at::_ops::ldexp_Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::ldexp_(Tensor(a!) self, Tensor other) -> Tensor(a!)
inline at::Tensor & Tensor::ldexp_(const at::Tensor & other) const {
    return at::_ops::ldexp_::call(const_cast<Tensor&>(*this), other);
}

// aten::log(Tensor self) -> Tensor
inline at::Tensor Tensor::log() const {
    return at::_ops::log::call(const_cast<Tensor&>(*this));
}

// aten::log_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::log_() const {
    return at::_ops::log_::call(const_cast<Tensor&>(*this));
}

// aten::log10(Tensor self) -> Tensor
inline at::Tensor Tensor::log10() const {
    return at::_ops::log10::call(const_cast<Tensor&>(*this));
}

// aten::log10_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::log10_() const {
    return at::_ops::log10_::call(const_cast<Tensor&>(*this));
}

// aten::log1p(Tensor self) -> Tensor
inline at::Tensor Tensor::log1p() const {
    return at::_ops::log1p::call(const_cast<Tensor&>(*this));
}

// aten::log1p_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::log1p_() const {
    return at::_ops::log1p_::call(const_cast<Tensor&>(*this));
}

// aten::log2(Tensor self) -> Tensor
inline at::Tensor Tensor::log2() const {
    return at::_ops::log2::call(const_cast<Tensor&>(*this));
}

// aten::log2_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::log2_() const {
    return at::_ops::log2_::call(const_cast<Tensor&>(*this));
}

// aten::logaddexp(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::logaddexp(const at::Tensor & other) const {
    return at::_ops::logaddexp::call(const_cast<Tensor&>(*this), other);
}

// aten::logaddexp2(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::logaddexp2(const at::Tensor & other) const {
    return at::_ops::logaddexp2::call(const_cast<Tensor&>(*this), other);
}

// aten::xlogy.Tensor(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::xlogy(const at::Tensor & other) const {
    return at::_ops::xlogy_Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::xlogy.Scalar_Other(Tensor self, Scalar other) -> Tensor
inline at::Tensor Tensor::xlogy(const at::Scalar & other) const {
    return at::_ops::xlogy_Scalar_Other::call(const_cast<Tensor&>(*this), other);
}

// aten::xlogy_.Tensor(Tensor(a!) self, Tensor other) -> Tensor(a!)
inline at::Tensor & Tensor::xlogy_(const at::Tensor & other) const {
    return at::_ops::xlogy__Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::xlogy_.Scalar_Other(Tensor(a!) self, Scalar other) -> Tensor(a!)
inline at::Tensor & Tensor::xlogy_(const at::Scalar & other) const {
    return at::_ops::xlogy__Scalar_Other::call(const_cast<Tensor&>(*this), other);
}

// aten::log_softmax.int(Tensor self, int dim, ScalarType? dtype=None) -> Tensor
inline at::Tensor Tensor::log_softmax(int64_t dim, ::std::optional<at::ScalarType> dtype) const {
    return at::_ops::log_softmax_int::call(const_cast<Tensor&>(*this), dim, dtype);
}

// aten::log_softmax.Dimname(Tensor self, Dimname dim, *, ScalarType? dtype=None) -> Tensor
inline at::Tensor Tensor::log_softmax(at::Dimname dim, ::std::optional<at::ScalarType> dtype) const {
    return at::_ops::log_softmax_Dimname::call(const_cast<Tensor&>(*this), dim, dtype);
}

// aten::logcumsumexp(Tensor self, int dim) -> Tensor
inline at::Tensor Tensor::logcumsumexp(int64_t dim) const {
    return at::_ops::logcumsumexp::call(const_cast<Tensor&>(*this), dim);
}

// aten::logcumsumexp.dimname(Tensor self, Dimname dim) -> Tensor
inline at::Tensor Tensor::logcumsumexp(at::Dimname dim) const {
    return at::_ops::logcumsumexp_dimname::call(const_cast<Tensor&>(*this), dim);
}

// aten::logsumexp(Tensor self, int[1] dim, bool keepdim=False) -> Tensor
inline at::Tensor Tensor::logsumexp(at::IntArrayRef dim, bool keepdim) const {
    return at::_ops::logsumexp::call(const_cast<Tensor&>(*this), dim, keepdim);
}

// aten::logsumexp.names(Tensor self, Dimname[1] dim, bool keepdim=False) -> Tensor
inline at::Tensor Tensor::logsumexp(at::DimnameList dim, bool keepdim) const {
    return at::_ops::logsumexp_names::call(const_cast<Tensor&>(*this), dim, keepdim);
}

// aten::matmul(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::matmul(const at::Tensor & other) const {
    return at::_ops::matmul::call(const_cast<Tensor&>(*this), other);
}

// aten::matrix_power(Tensor self, int n) -> Tensor
inline at::Tensor Tensor::matrix_power(int64_t n) const {
    return at::_ops::matrix_power::call(const_cast<Tensor&>(*this), n);
}

// aten::matrix_exp(Tensor self) -> Tensor
inline at::Tensor Tensor::matrix_exp() const {
    return at::_ops::matrix_exp::call(const_cast<Tensor&>(*this));
}

// aten::aminmax(Tensor self, *, int? dim=None, bool keepdim=False) -> (Tensor min, Tensor max)
inline ::std::tuple<at::Tensor,at::Tensor> Tensor::aminmax(::std::optional<int64_t> dim, bool keepdim) const {
    return at::_ops::aminmax::call(const_cast<Tensor&>(*this), dim, keepdim);
}

// aten::max.dim(Tensor self, int dim, bool keepdim=False) -> (Tensor values, Tensor indices)
inline ::std::tuple<at::Tensor,at::Tensor> Tensor::max(int64_t dim, bool keepdim) const {
    return at::_ops::max_dim::call(const_cast<Tensor&>(*this), dim, keepdim);
}

// aten::max.names_dim(Tensor self, Dimname dim, bool keepdim=False) -> (Tensor values, Tensor indices)
inline ::std::tuple<at::Tensor,at::Tensor> Tensor::max(at::Dimname dim, bool keepdim) const {
    return at::_ops::max_names_dim::call(const_cast<Tensor&>(*this), dim, keepdim);
}

// aten::amax(Tensor self, int[1] dim=[], bool keepdim=False) -> Tensor
inline at::Tensor Tensor::amax(at::IntArrayRef dim, bool keepdim) const {
    return at::_ops::amax::call(const_cast<Tensor&>(*this), dim, keepdim);
}

// aten::mean(Tensor self, *, ScalarType? dtype=None) -> Tensor
inline at::Tensor Tensor::mean(::std::optional<at::ScalarType> dtype) const {
    return at::_ops::mean::call(const_cast<Tensor&>(*this), dtype);
}

// aten::mean.dim(Tensor self, int[1]? dim, bool keepdim=False, *, ScalarType? dtype=None) -> Tensor
inline at::Tensor Tensor::mean(at::OptionalIntArrayRef dim, bool keepdim, ::std::optional<at::ScalarType> dtype) const {
    return at::_ops::mean_dim::call(const_cast<Tensor&>(*this), dim, keepdim, dtype);
}

// aten::mean.names_dim(Tensor self, Dimname[1] dim, bool keepdim=False, *, ScalarType? dtype=None) -> Tensor
inline at::Tensor Tensor::mean(at::DimnameList dim, bool keepdim, ::std::optional<at::ScalarType> dtype) const {
    return at::_ops::mean_names_dim::call(const_cast<Tensor&>(*this), dim, keepdim, dtype);
}

// aten::nanmean(Tensor self, int[1]? dim=None, bool keepdim=False, *, ScalarType? dtype=None) -> Tensor
inline at::Tensor Tensor::nanmean(at::OptionalIntArrayRef dim, bool keepdim, ::std::optional<at::ScalarType> dtype) const {
    return at::_ops::nanmean::call(const_cast<Tensor&>(*this), dim, keepdim, dtype);
}

// aten::median(Tensor self) -> Tensor
inline at::Tensor Tensor::median() const {
    return at::_ops::median::call(const_cast<Tensor&>(*this));
}

// aten::median.dim(Tensor self, int dim, bool keepdim=False) -> (Tensor values, Tensor indices)
inline ::std::tuple<at::Tensor,at::Tensor> Tensor::median(int64_t dim, bool keepdim) const {
    return at::_ops::median_dim::call(const_cast<Tensor&>(*this), dim, keepdim);
}

// aten::median.names_dim(Tensor self, Dimname dim, bool keepdim=False) -> (Tensor values, Tensor indices)
inline ::std::tuple<at::Tensor,at::Tensor> Tensor::median(at::Dimname dim, bool keepdim) const {
    return at::_ops::median_names_dim::call(const_cast<Tensor&>(*this), dim, keepdim);
}

// aten::nanmedian(Tensor self) -> Tensor
inline at::Tensor Tensor::nanmedian() const {
    return at::_ops::nanmedian::call(const_cast<Tensor&>(*this));
}

// aten::nanmedian.dim(Tensor self, int dim, bool keepdim=False) -> (Tensor values, Tensor indices)
inline ::std::tuple<at::Tensor,at::Tensor> Tensor::nanmedian(int64_t dim, bool keepdim) const {
    return at::_ops::nanmedian_dim::call(const_cast<Tensor&>(*this), dim, keepdim);
}

// aten::nanmedian.names_dim(Tensor self, Dimname dim, bool keepdim=False) -> (Tensor values, Tensor indices)
inline ::std::tuple<at::Tensor,at::Tensor> Tensor::nanmedian(at::Dimname dim, bool keepdim) const {
    return at::_ops::nanmedian_names_dim::call(const_cast<Tensor&>(*this), dim, keepdim);
}

// aten::min.dim(Tensor self, int dim, bool keepdim=False) -> (Tensor values, Tensor indices)
inline ::std::tuple<at::Tensor,at::Tensor> Tensor::min(int64_t dim, bool keepdim) const {
    return at::_ops::min_dim::call(const_cast<Tensor&>(*this), dim, keepdim);
}

// aten::min.names_dim(Tensor self, Dimname dim, bool keepdim=False) -> (Tensor values, Tensor indices)
inline ::std::tuple<at::Tensor,at::Tensor> Tensor::min(at::Dimname dim, bool keepdim) const {
    return at::_ops::min_names_dim::call(const_cast<Tensor&>(*this), dim, keepdim);
}

// aten::amin(Tensor self, int[1] dim=[], bool keepdim=False) -> Tensor
inline at::Tensor Tensor::amin(at::IntArrayRef dim, bool keepdim) const {
    return at::_ops::amin::call(const_cast<Tensor&>(*this), dim, keepdim);
}

// aten::mm(Tensor self, Tensor mat2) -> Tensor
inline at::Tensor Tensor::mm(const at::Tensor & mat2) const {
    return at::_ops::mm::call(const_cast<Tensor&>(*this), mat2);
}

// aten::mode(Tensor self, int dim=-1, bool keepdim=False) -> (Tensor values, Tensor indices)
inline ::std::tuple<at::Tensor,at::Tensor> Tensor::mode(int64_t dim, bool keepdim) const {
    return at::_ops::mode::call(const_cast<Tensor&>(*this), dim, keepdim);
}

// aten::mode.dimname(Tensor self, Dimname dim, bool keepdim=False) -> (Tensor values, Tensor indices)
inline ::std::tuple<at::Tensor,at::Tensor> Tensor::mode(at::Dimname dim, bool keepdim) const {
    return at::_ops::mode_dimname::call(const_cast<Tensor&>(*this), dim, keepdim);
}

// aten::mul.Tensor(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::mul(const at::Tensor & other) const {
    return at::_ops::mul_Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::mul_.Tensor(Tensor(a!) self, Tensor other) -> Tensor(a!)
inline at::Tensor & Tensor::mul_(const at::Tensor & other) const {
    return at::_ops::mul__Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::mul.Scalar(Tensor self, Scalar other) -> Tensor
inline at::Tensor Tensor::mul(const at::Scalar & other) const {
    return at::_ops::mul_Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::mul_.Scalar(Tensor(a!) self, Scalar other) -> Tensor(a!)
inline at::Tensor & Tensor::mul_(const at::Scalar & other) const {
    return at::_ops::mul__Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::multiply.Tensor(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::multiply(const at::Tensor & other) const {
    return at::_ops::multiply_Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::multiply_.Tensor(Tensor(a!) self, Tensor other) -> Tensor(a!)
inline at::Tensor & Tensor::multiply_(const at::Tensor & other) const {
    return at::_ops::multiply__Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::multiply.Scalar(Tensor self, Scalar other) -> Tensor
inline at::Tensor Tensor::multiply(const at::Scalar & other) const {
    return at::_ops::multiply_Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::multiply_.Scalar(Tensor(a!) self, Scalar other) -> Tensor(a!)
inline at::Tensor & Tensor::multiply_(const at::Scalar & other) const {
    return at::_ops::multiply__Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::mv(Tensor self, Tensor vec) -> Tensor
inline at::Tensor Tensor::mv(const at::Tensor & vec) const {
    return at::_ops::mv::call(const_cast<Tensor&>(*this), vec);
}

// aten::mvlgamma(Tensor self, int p) -> Tensor
inline at::Tensor Tensor::mvlgamma(int64_t p) const {
    return at::_ops::mvlgamma::call(const_cast<Tensor&>(*this), p);
}

// aten::mvlgamma_(Tensor(a!) self, int p) -> Tensor(a!)
inline at::Tensor & Tensor::mvlgamma_(int64_t p) const {
    return at::_ops::mvlgamma_::call(const_cast<Tensor&>(*this), p);
}

// aten::narrow_copy(Tensor self, int dim, SymInt start, SymInt length) -> Tensor
inline at::Tensor Tensor::narrow_copy(int64_t dim, int64_t start, int64_t length) const {
    return at::_ops::narrow_copy::call(const_cast<Tensor&>(*this), dim, start, length);
}

// aten::narrow_copy(Tensor self, int dim, SymInt start, SymInt length) -> Tensor
inline at::Tensor Tensor::narrow_copy_symint(int64_t dim, c10::SymInt start, c10::SymInt length) const {
    return at::_ops::narrow_copy::call(const_cast<Tensor&>(*this), dim, start, length);
}

// aten::narrow(Tensor(a) self, int dim, SymInt start, SymInt length) -> Tensor(a)
inline at::Tensor Tensor::narrow(int64_t dim, int64_t start, int64_t length) const {
    return at::_ops::narrow::call(const_cast<Tensor&>(*this), dim, start, length);
}

// aten::narrow(Tensor(a) self, int dim, SymInt start, SymInt length) -> Tensor(a)
inline at::Tensor Tensor::narrow_symint(int64_t dim, c10::SymInt start, c10::SymInt length) const {
    return at::_ops::narrow::call(const_cast<Tensor&>(*this), dim, start, length);
}

// aten::narrow.Tensor(Tensor(a) self, int dim, Tensor start, SymInt length) -> Tensor(a)
inline at::Tensor Tensor::narrow(int64_t dim, const at::Tensor & start, int64_t length) const {
    return at::_ops::narrow_Tensor::call(const_cast<Tensor&>(*this), dim, start, length);
}

// aten::narrow.Tensor(Tensor(a) self, int dim, Tensor start, SymInt length) -> Tensor(a)
inline at::Tensor Tensor::narrow_symint(int64_t dim, const at::Tensor & start, c10::SymInt length) const {
    return at::_ops::narrow_Tensor::call(const_cast<Tensor&>(*this), dim, start, length);
}

// aten::permute(Tensor(a) self, int[] dims) -> Tensor(a)
inline at::Tensor Tensor::permute(at::IntArrayRef dims) const {
    return at::_ops::permute::call(const_cast<Tensor&>(*this), dims);
}

// aten::movedim.intlist(Tensor(a) self, int[] source, int[] destination) -> Tensor(a)
inline at::Tensor Tensor::movedim(at::IntArrayRef source, at::IntArrayRef destination) const {
    return at::_ops::movedim_intlist::call(const_cast<Tensor&>(*this), source, destination);
}

// aten::movedim.int(Tensor(a) self, int source, int destination) -> Tensor(a)
inline at::Tensor Tensor::movedim(int64_t source, int64_t destination) const {
    return at::_ops::movedim_int::call(const_cast<Tensor&>(*this), source, destination);
}

// aten::moveaxis.intlist(Tensor(a) self, int[] source, int[] destination) -> Tensor(a)
inline at::Tensor Tensor::moveaxis(at::IntArrayRef source, at::IntArrayRef destination) const {
    return at::_ops::moveaxis_intlist::call(const_cast<Tensor&>(*this), source, destination);
}

// aten::moveaxis.int(Tensor(a) self, int source, int destination) -> Tensor(a)
inline at::Tensor Tensor::moveaxis(int64_t source, int64_t destination) const {
    return at::_ops::moveaxis_int::call(const_cast<Tensor&>(*this), source, destination);
}

// aten::numpy_T(Tensor(a) self) -> Tensor(a)
inline at::Tensor Tensor::numpy_T() const {
    return at::_ops::numpy_T::call(const_cast<Tensor&>(*this));
}

// aten::matrix_H(Tensor(a) self) -> Tensor(a)
inline at::Tensor Tensor::matrix_H() const {
    return at::_ops::matrix_H::call(const_cast<Tensor&>(*this));
}

// aten::mT(Tensor(a) self) -> Tensor(a)
inline at::Tensor Tensor::mT() const {
    return at::_ops::mT::call(const_cast<Tensor&>(*this));
}

// aten::mH(Tensor(a) self) -> Tensor(a)
inline at::Tensor Tensor::mH() const {
    return at::_ops::mH::call(const_cast<Tensor&>(*this));
}

// aten::adjoint(Tensor(a) self) -> Tensor(a)
inline at::Tensor Tensor::adjoint() const {
    return at::_ops::adjoint::call(const_cast<Tensor&>(*this));
}

// aten::is_pinned(Tensor self, Device? device=None) -> bool
inline bool Tensor::is_pinned(::std::optional<at::Device> device) const {
    return at::_ops::is_pinned::call(const_cast<Tensor&>(*this), device);
}

// aten::pin_memory(Tensor(a) self, Device? device=None) -> Tensor(a)
inline at::Tensor Tensor::pin_memory(::std::optional<at::Device> device) const {
    return at::_ops::pin_memory::call(const_cast<Tensor&>(*this), device);
}

// aten::pinverse(Tensor self, float rcond=1e-15) -> Tensor
inline at::Tensor Tensor::pinverse(double rcond) const {
    return at::_ops::pinverse::call(const_cast<Tensor&>(*this), rcond);
}

// aten::rad2deg(Tensor self) -> Tensor
inline at::Tensor Tensor::rad2deg() const {
    return at::_ops::rad2deg::call(const_cast<Tensor&>(*this));
}

// aten::rad2deg_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::rad2deg_() const {
    return at::_ops::rad2deg_::call(const_cast<Tensor&>(*this));
}

// aten::deg2rad(Tensor self) -> Tensor
inline at::Tensor Tensor::deg2rad() const {
    return at::_ops::deg2rad::call(const_cast<Tensor&>(*this));
}

// aten::deg2rad_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::deg2rad_() const {
    return at::_ops::deg2rad_::call(const_cast<Tensor&>(*this));
}

// aten::ravel(Tensor(a) self) -> Tensor(a)
inline at::Tensor Tensor::ravel() const {
    return at::_ops::ravel::call(const_cast<Tensor&>(*this));
}

// aten::reciprocal(Tensor self) -> Tensor
inline at::Tensor Tensor::reciprocal() const {
    return at::_ops::reciprocal::call(const_cast<Tensor&>(*this));
}

// aten::reciprocal_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::reciprocal_() const {
    return at::_ops::reciprocal_::call(const_cast<Tensor&>(*this));
}

// aten::neg(Tensor self) -> Tensor
inline at::Tensor Tensor::neg() const {
    return at::_ops::neg::call(const_cast<Tensor&>(*this));
}

// aten::neg_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::neg_() const {
    return at::_ops::neg_::call(const_cast<Tensor&>(*this));
}

// aten::negative(Tensor self) -> Tensor
inline at::Tensor Tensor::negative() const {
    return at::_ops::negative::call(const_cast<Tensor&>(*this));
}

// aten::negative_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::negative_() const {
    return at::_ops::negative_::call(const_cast<Tensor&>(*this));
}

// aten::repeat(Tensor self, SymInt[] repeats) -> Tensor
inline at::Tensor Tensor::repeat(at::IntArrayRef repeats) const {
    return at::_ops::repeat::call(const_cast<Tensor&>(*this), c10::fromIntArrayRefSlow(repeats));
}

// aten::repeat(Tensor self, SymInt[] repeats) -> Tensor
inline at::Tensor Tensor::repeat_symint(c10::SymIntArrayRef repeats) const {
    return at::_ops::repeat::call(const_cast<Tensor&>(*this), repeats);
}

// aten::repeat_interleave.self_Tensor(Tensor self, Tensor repeats, int? dim=None, *, SymInt? output_size=None) -> Tensor
inline at::Tensor Tensor::repeat_interleave(const at::Tensor & repeats, ::std::optional<int64_t> dim, ::std::optional<int64_t> output_size) const {
    return at::_ops::repeat_interleave_self_Tensor::call(const_cast<Tensor&>(*this), repeats, dim, output_size.has_value() ? ::std::make_optional(c10::SymInt(*output_size)) : ::std::nullopt);
}

// aten::repeat_interleave.self_Tensor(Tensor self, Tensor repeats, int? dim=None, *, SymInt? output_size=None) -> Tensor
inline at::Tensor Tensor::repeat_interleave_symint(const at::Tensor & repeats, ::std::optional<int64_t> dim, ::std::optional<c10::SymInt> output_size) const {
    return at::_ops::repeat_interleave_self_Tensor::call(const_cast<Tensor&>(*this), repeats, dim, output_size);
}

// aten::repeat_interleave.self_int(Tensor self, SymInt repeats, int? dim=None, *, SymInt? output_size=None) -> Tensor
inline at::Tensor Tensor::repeat_interleave(int64_t repeats, ::std::optional<int64_t> dim, ::std::optional<int64_t> output_size) const {
    return at::_ops::repeat_interleave_self_int::call(const_cast<Tensor&>(*this), repeats, dim, output_size.has_value() ? ::std::make_optional(c10::SymInt(*output_size)) : ::std::nullopt);
}

// aten::repeat_interleave.self_int(Tensor self, SymInt repeats, int? dim=None, *, SymInt? output_size=None) -> Tensor
inline at::Tensor Tensor::repeat_interleave_symint(c10::SymInt repeats, ::std::optional<int64_t> dim, ::std::optional<c10::SymInt> output_size) const {
    return at::_ops::repeat_interleave_self_int::call(const_cast<Tensor&>(*this), repeats, dim, output_size);
}

// aten::reshape(Tensor(a) self, SymInt[] shape) -> Tensor(a)
inline at::Tensor Tensor::reshape(at::IntArrayRef shape) const {
    return at::_ops::reshape::call(const_cast<Tensor&>(*this), c10::fromIntArrayRefSlow(shape));
}

// aten::reshape(Tensor(a) self, SymInt[] shape) -> Tensor(a)
inline at::Tensor Tensor::reshape_symint(c10::SymIntArrayRef shape) const {
    return at::_ops::reshape::call(const_cast<Tensor&>(*this), shape);
}

// aten::_reshape_alias(Tensor(a) self, SymInt[] size, SymInt[] stride) -> Tensor(a)
inline at::Tensor Tensor::_reshape_alias(at::IntArrayRef size, at::IntArrayRef stride) const {
    return at::_ops::_reshape_alias::call(const_cast<Tensor&>(*this), c10::fromIntArrayRefSlow(size), c10::fromIntArrayRefSlow(stride));
}

// aten::_reshape_alias(Tensor(a) self, SymInt[] size, SymInt[] stride) -> Tensor(a)
inline at::Tensor Tensor::_reshape_alias_symint(c10::SymIntArrayRef size, c10::SymIntArrayRef stride) const {
    return at::_ops::_reshape_alias::call(const_cast<Tensor&>(*this), size, stride);
}

// aten::reshape_as(Tensor(a) self, Tensor other) -> Tensor(a)
inline at::Tensor Tensor::reshape_as(const at::Tensor & other) const {
    return at::_ops::reshape_as::call(const_cast<Tensor&>(*this), other);
}

// aten::round(Tensor self) -> Tensor
inline at::Tensor Tensor::round() const {
    return at::_ops::round::call(const_cast<Tensor&>(*this));
}

// aten::round_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::round_() const {
    return at::_ops::round_::call(const_cast<Tensor&>(*this));
}

// aten::round.decimals(Tensor self, *, int decimals) -> Tensor
inline at::Tensor Tensor::round(int64_t decimals) const {
    return at::_ops::round_decimals::call(const_cast<Tensor&>(*this), decimals);
}

// aten::round_.decimals(Tensor(a!) self, *, int decimals) -> Tensor(a!)
inline at::Tensor & Tensor::round_(int64_t decimals) const {
    return at::_ops::round__decimals::call(const_cast<Tensor&>(*this), decimals);
}

// aten::relu(Tensor self) -> Tensor
inline at::Tensor Tensor::relu() const {
    return at::_ops::relu::call(const_cast<Tensor&>(*this));
}

// aten::relu_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::relu_() const {
    return at::_ops::relu_::call(const_cast<Tensor&>(*this));
}

// aten::prelu(Tensor self, Tensor weight) -> Tensor
inline at::Tensor Tensor::prelu(const at::Tensor & weight) const {
    return at::_ops::prelu::call(const_cast<Tensor&>(*this), weight);
}

// aten::hardshrink(Tensor self, Scalar lambd=0.5) -> Tensor
inline at::Tensor Tensor::hardshrink(const at::Scalar & lambd) const {
    return at::_ops::hardshrink::call(const_cast<Tensor&>(*this), lambd);
}

// aten::hardshrink_backward(Tensor grad_out, Tensor self, Scalar lambd) -> Tensor
inline at::Tensor Tensor::hardshrink_backward(const at::Tensor & grad_out, const at::Scalar & lambd) const {
    return at::_ops::hardshrink_backward::call(grad_out, const_cast<Tensor&>(*this), lambd);
}

// aten::rsqrt(Tensor self) -> Tensor
inline at::Tensor Tensor::rsqrt() const {
    return at::_ops::rsqrt::call(const_cast<Tensor&>(*this));
}

// aten::rsqrt_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::rsqrt_() const {
    return at::_ops::rsqrt_::call(const_cast<Tensor&>(*this));
}

// aten::select.Dimname(Tensor(a) self, Dimname dim, int index) -> Tensor(a)
inline at::Tensor Tensor::select(at::Dimname dim, int64_t index) const {
    return at::_ops::select_Dimname::call(const_cast<Tensor&>(*this), dim, index);
}

// aten::select.int(Tensor(a) self, int dim, SymInt index) -> Tensor(a)
inline at::Tensor Tensor::select(int64_t dim, int64_t index) const {
    return at::_ops::select_int::call(const_cast<Tensor&>(*this), dim, index);
}

// aten::select.int(Tensor(a) self, int dim, SymInt index) -> Tensor(a)
inline at::Tensor Tensor::select_symint(int64_t dim, c10::SymInt index) const {
    return at::_ops::select_int::call(const_cast<Tensor&>(*this), dim, index);
}

// aten::sigmoid(Tensor self) -> Tensor
inline at::Tensor Tensor::sigmoid() const {
    return at::_ops::sigmoid::call(const_cast<Tensor&>(*this));
}

// aten::sigmoid_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::sigmoid_() const {
    return at::_ops::sigmoid_::call(const_cast<Tensor&>(*this));
}

// aten::logit(Tensor self, float? eps=None) -> Tensor
inline at::Tensor Tensor::logit(::std::optional<double> eps) const {
    return at::_ops::logit::call(const_cast<Tensor&>(*this), eps);
}

// aten::logit_(Tensor(a!) self, float? eps=None) -> Tensor(a!)
inline at::Tensor & Tensor::logit_(::std::optional<double> eps) const {
    return at::_ops::logit_::call(const_cast<Tensor&>(*this), eps);
}

// aten::sin(Tensor self) -> Tensor
inline at::Tensor Tensor::sin() const {
    return at::_ops::sin::call(const_cast<Tensor&>(*this));
}

// aten::sin_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::sin_() const {
    return at::_ops::sin_::call(const_cast<Tensor&>(*this));
}

// aten::sinc(Tensor self) -> Tensor
inline at::Tensor Tensor::sinc() const {
    return at::_ops::sinc::call(const_cast<Tensor&>(*this));
}

// aten::sinc_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::sinc_() const {
    return at::_ops::sinc_::call(const_cast<Tensor&>(*this));
}

// aten::sinh(Tensor self) -> Tensor
inline at::Tensor Tensor::sinh() const {
    return at::_ops::sinh::call(const_cast<Tensor&>(*this));
}

// aten::sinh_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::sinh_() const {
    return at::_ops::sinh_::call(const_cast<Tensor&>(*this));
}

// aten::detach(Tensor(a) self) -> Tensor(a)
inline at::Tensor Tensor::detach() const {
    return at::_ops::detach::call(const_cast<Tensor&>(*this));
}

// aten::detach_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::detach_() const {
    return at::_ops::detach_::call(const_cast<Tensor&>(*this));
}

// aten::size.Dimname(Tensor self, Dimname dim) -> int
inline int64_t Tensor::size(at::Dimname dim) const {
    return at::_ops::size_Dimname::call(const_cast<Tensor&>(*this), dim);
}

// aten::slice.Tensor(Tensor(a) self, int dim=0, SymInt? start=None, SymInt? end=None, SymInt step=1) -> Tensor(a)
inline at::Tensor Tensor::slice(int64_t dim, ::std::optional<int64_t> start, ::std::optional<int64_t> end, int64_t step) const {
    return at::_ops::slice_Tensor::call(const_cast<Tensor&>(*this), dim, start.has_value() ? ::std::make_optional(c10::SymInt(*start)) : ::std::nullopt, end.has_value() ? ::std::make_optional(c10::SymInt(*end)) : ::std::nullopt, step);
}

// aten::slice.Tensor(Tensor(a) self, int dim=0, SymInt? start=None, SymInt? end=None, SymInt step=1) -> Tensor(a)
inline at::Tensor Tensor::slice_symint(int64_t dim, ::std::optional<c10::SymInt> start, ::std::optional<c10::SymInt> end, c10::SymInt step) const {
    return at::_ops::slice_Tensor::call(const_cast<Tensor&>(*this), dim, start, end, step);
}

// aten::slice_inverse(Tensor(a) self, Tensor src, int dim=0, SymInt? start=None, SymInt? end=None, SymInt step=1) -> Tensor(a)
inline at::Tensor Tensor::slice_inverse(const at::Tensor & src, int64_t dim, ::std::optional<int64_t> start, ::std::optional<int64_t> end, int64_t step) const {
    return at::_ops::slice_inverse::call(const_cast<Tensor&>(*this), src, dim, start.has_value() ? ::std::make_optional(c10::SymInt(*start)) : ::std::nullopt, end.has_value() ? ::std::make_optional(c10::SymInt(*end)) : ::std::nullopt, step);
}

// aten::slice_inverse(Tensor(a) self, Tensor src, int dim=0, SymInt? start=None, SymInt? end=None, SymInt step=1) -> Tensor(a)
inline at::Tensor Tensor::slice_inverse_symint(const at::Tensor & src, int64_t dim, ::std::optional<c10::SymInt> start, ::std::optional<c10::SymInt> end, c10::SymInt step) const {
    return at::_ops::slice_inverse::call(const_cast<Tensor&>(*this), src, dim, start, end, step);
}

// aten::slice_scatter(Tensor self, Tensor src, int dim=0, SymInt? start=None, SymInt? end=None, SymInt step=1) -> Tensor
inline at::Tensor Tensor::slice_scatter(const at::Tensor & src, int64_t dim, ::std::optional<int64_t> start, ::std::optional<int64_t> end, int64_t step) const {
    return at::_ops::slice_scatter::call(const_cast<Tensor&>(*this), src, dim, start.has_value() ? ::std::make_optional(c10::SymInt(*start)) : ::std::nullopt, end.has_value() ? ::std::make_optional(c10::SymInt(*end)) : ::std::nullopt, step);
}

// aten::slice_scatter(Tensor self, Tensor src, int dim=0, SymInt? start=None, SymInt? end=None, SymInt step=1) -> Tensor
inline at::Tensor Tensor::slice_scatter_symint(const at::Tensor & src, int64_t dim, ::std::optional<c10::SymInt> start, ::std::optional<c10::SymInt> end, c10::SymInt step) const {
    return at::_ops::slice_scatter::call(const_cast<Tensor&>(*this), src, dim, start, end, step);
}

// aten::select_scatter(Tensor self, Tensor src, int dim, SymInt index) -> Tensor
inline at::Tensor Tensor::select_scatter(const at::Tensor & src, int64_t dim, int64_t index) const {
    return at::_ops::select_scatter::call(const_cast<Tensor&>(*this), src, dim, index);
}

// aten::select_scatter(Tensor self, Tensor src, int dim, SymInt index) -> Tensor
inline at::Tensor Tensor::select_scatter_symint(const at::Tensor & src, int64_t dim, c10::SymInt index) const {
    return at::_ops::select_scatter::call(const_cast<Tensor&>(*this), src, dim, index);
}

// aten::diagonal_scatter(Tensor self, Tensor src, int offset=0, int dim1=0, int dim2=1) -> Tensor
inline at::Tensor Tensor::diagonal_scatter(const at::Tensor & src, int64_t offset, int64_t dim1, int64_t dim2) const {
    return at::_ops::diagonal_scatter::call(const_cast<Tensor&>(*this), src, offset, dim1, dim2);
}

// aten::as_strided_scatter(Tensor self, Tensor src, SymInt[] size, SymInt[] stride, SymInt? storage_offset=None) -> Tensor
inline at::Tensor Tensor::as_strided_scatter(const at::Tensor & src, at::IntArrayRef size, at::IntArrayRef stride, ::std::optional<int64_t> storage_offset) const {
    return at::_ops::as_strided_scatter::call(const_cast<Tensor&>(*this), src, c10::fromIntArrayRefSlow(size), c10::fromIntArrayRefSlow(stride), storage_offset.has_value() ? ::std::make_optional(c10::SymInt(*storage_offset)) : ::std::nullopt);
}

// aten::as_strided_scatter(Tensor self, Tensor src, SymInt[] size, SymInt[] stride, SymInt? storage_offset=None) -> Tensor
inline at::Tensor Tensor::as_strided_scatter_symint(const at::Tensor & src, c10::SymIntArrayRef size, c10::SymIntArrayRef stride, ::std::optional<c10::SymInt> storage_offset) const {
    return at::_ops::as_strided_scatter::call(const_cast<Tensor&>(*this), src, size, stride, storage_offset);
}

// aten::smm(Tensor self, Tensor mat2) -> Tensor
inline at::Tensor Tensor::smm(const at::Tensor & mat2) const {
    return at::_ops::smm::call(const_cast<Tensor&>(*this), mat2);
}

// aten::softmax.int(Tensor self, int dim, ScalarType? dtype=None) -> Tensor
inline at::Tensor Tensor::softmax(int64_t dim, ::std::optional<at::ScalarType> dtype) const {
    return at::_ops::softmax_int::call(const_cast<Tensor&>(*this), dim, dtype);
}

// aten::softmax.Dimname(Tensor self, Dimname dim, *, ScalarType? dtype=None) -> Tensor
inline at::Tensor Tensor::softmax(at::Dimname dim, ::std::optional<at::ScalarType> dtype) const {
    return at::_ops::softmax_Dimname::call(const_cast<Tensor&>(*this), dim, dtype);
}

// aten::unsafe_split.Tensor(Tensor self, SymInt split_size, int dim=0) -> Tensor[]
inline ::std::vector<at::Tensor> Tensor::unsafe_split(int64_t split_size, int64_t dim) const {
    return at::_ops::unsafe_split_Tensor::call(const_cast<Tensor&>(*this), split_size, dim);
}

// aten::unsafe_split.Tensor(Tensor self, SymInt split_size, int dim=0) -> Tensor[]
inline ::std::vector<at::Tensor> Tensor::unsafe_split_symint(c10::SymInt split_size, int64_t dim) const {
    return at::_ops::unsafe_split_Tensor::call(const_cast<Tensor&>(*this), split_size, dim);
}

// aten::split.Tensor(Tensor(a -> *) self, SymInt split_size, int dim=0) -> Tensor(a)[]
inline ::std::vector<at::Tensor> Tensor::split(int64_t split_size, int64_t dim) const {
    return at::_ops::split_Tensor::call(const_cast<Tensor&>(*this), split_size, dim);
}

// aten::split.Tensor(Tensor(a -> *) self, SymInt split_size, int dim=0) -> Tensor(a)[]
inline ::std::vector<at::Tensor> Tensor::split_symint(c10::SymInt split_size, int64_t dim) const {
    return at::_ops::split_Tensor::call(const_cast<Tensor&>(*this), split_size, dim);
}

// aten::split.sizes(Tensor(a -> *) self, SymInt[] split_size, int dim=0) -> Tensor(a)[]
inline ::std::vector<at::Tensor> Tensor::split(at::IntArrayRef split_size, int64_t dim) const {
    return at::_ops::split_sizes::call(const_cast<Tensor&>(*this), c10::fromIntArrayRefSlow(split_size), dim);
}

// aten::split.sizes(Tensor(a -> *) self, SymInt[] split_size, int dim=0) -> Tensor(a)[]
inline ::std::vector<at::Tensor> Tensor::split_symint(c10::SymIntArrayRef split_size, int64_t dim) const {
    return at::_ops::split_sizes::call(const_cast<Tensor&>(*this), split_size, dim);
}

// aten::unsafe_split_with_sizes(Tensor self, SymInt[] split_sizes, int dim=0) -> Tensor[]
inline ::std::vector<at::Tensor> Tensor::unsafe_split_with_sizes(at::IntArrayRef split_sizes, int64_t dim) const {
    return at::_ops::unsafe_split_with_sizes::call(const_cast<Tensor&>(*this), c10::fromIntArrayRefSlow(split_sizes), dim);
}

// aten::unsafe_split_with_sizes(Tensor self, SymInt[] split_sizes, int dim=0) -> Tensor[]
inline ::std::vector<at::Tensor> Tensor::unsafe_split_with_sizes_symint(c10::SymIntArrayRef split_sizes, int64_t dim) const {
    return at::_ops::unsafe_split_with_sizes::call(const_cast<Tensor&>(*this), split_sizes, dim);
}

// aten::split_with_sizes(Tensor(a -> *) self, SymInt[] split_sizes, int dim=0) -> Tensor(a)[]
inline ::std::vector<at::Tensor> Tensor::split_with_sizes(at::IntArrayRef split_sizes, int64_t dim) const {
    return at::_ops::split_with_sizes::call(const_cast<Tensor&>(*this), c10::fromIntArrayRefSlow(split_sizes), dim);
}

// aten::split_with_sizes(Tensor(a -> *) self, SymInt[] split_sizes, int dim=0) -> Tensor(a)[]
inline ::std::vector<at::Tensor> Tensor::split_with_sizes_symint(c10::SymIntArrayRef split_sizes, int64_t dim) const {
    return at::_ops::split_with_sizes::call(const_cast<Tensor&>(*this), split_sizes, dim);
}

// aten::hsplit.int(Tensor(a -> *) self, int sections) -> Tensor(a)[]
inline ::std::vector<at::Tensor> Tensor::hsplit(int64_t sections) const {
    return at::_ops::hsplit_int::call(const_cast<Tensor&>(*this), sections);
}

// aten::hsplit.array(Tensor(a -> *) self, int[] indices) -> Tensor(a)[]
inline ::std::vector<at::Tensor> Tensor::hsplit(at::IntArrayRef indices) const {
    return at::_ops::hsplit_array::call(const_cast<Tensor&>(*this), indices);
}

// aten::vsplit.int(Tensor(a -> *) self, int sections) -> Tensor(a)[]
inline ::std::vector<at::Tensor> Tensor::vsplit(int64_t sections) const {
    return at::_ops::vsplit_int::call(const_cast<Tensor&>(*this), sections);
}

// aten::vsplit.array(Tensor(a -> *) self, int[] indices) -> Tensor(a)[]
inline ::std::vector<at::Tensor> Tensor::vsplit(at::IntArrayRef indices) const {
    return at::_ops::vsplit_array::call(const_cast<Tensor&>(*this), indices);
}

// aten::dsplit.int(Tensor(a -> *) self, int sections) -> Tensor(a)[]
inline ::std::vector<at::Tensor> Tensor::dsplit(int64_t sections) const {
    return at::_ops::dsplit_int::call(const_cast<Tensor&>(*this), sections);
}

// aten::dsplit.array(Tensor(a -> *) self, int[] indices) -> Tensor(a)[]
inline ::std::vector<at::Tensor> Tensor::dsplit(at::IntArrayRef indices) const {
    return at::_ops::dsplit_array::call(const_cast<Tensor&>(*this), indices);
}

// aten::squeeze(Tensor(a) self) -> Tensor(a)
inline at::Tensor Tensor::squeeze() const {
    return at::_ops::squeeze::call(const_cast<Tensor&>(*this));
}

// aten::squeeze.dim(Tensor(a) self, int dim) -> Tensor(a)
inline at::Tensor Tensor::squeeze(int64_t dim) const {
    return at::_ops::squeeze_dim::call(const_cast<Tensor&>(*this), dim);
}

// aten::squeeze.dimname(Tensor(a) self, Dimname dim) -> Tensor(a)
inline at::Tensor Tensor::squeeze(at::Dimname dim) const {
    return at::_ops::squeeze_dimname::call(const_cast<Tensor&>(*this), dim);
}

// aten::squeeze.dims(Tensor(a) self, int[] dim) -> Tensor(a)
inline at::Tensor Tensor::squeeze(at::IntArrayRef dim) const {
    return at::_ops::squeeze_dims::call(const_cast<Tensor&>(*this), dim);
}

// aten::squeeze_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::squeeze_() const {
    return at::_ops::squeeze_::call(const_cast<Tensor&>(*this));
}

// aten::squeeze_.dim(Tensor(a!) self, int dim) -> Tensor(a!)
inline at::Tensor & Tensor::squeeze_(int64_t dim) const {
    return at::_ops::squeeze__dim::call(const_cast<Tensor&>(*this), dim);
}

// aten::squeeze_.dims(Tensor(a!) self, int[] dim) -> Tensor(a!)
inline at::Tensor & Tensor::squeeze_(at::IntArrayRef dim) const {
    return at::_ops::squeeze__dims::call(const_cast<Tensor&>(*this), dim);
}

// aten::squeeze_.dimname(Tensor(a!) self, Dimname dim) -> Tensor(a!)
inline at::Tensor & Tensor::squeeze_(at::Dimname dim) const {
    return at::_ops::squeeze__dimname::call(const_cast<Tensor&>(*this), dim);
}

// aten::sspaddmm(Tensor self, Tensor mat1, Tensor mat2, *, Scalar beta=1, Scalar alpha=1) -> Tensor
inline at::Tensor Tensor::sspaddmm(const at::Tensor & mat1, const at::Tensor & mat2, const at::Scalar & beta, const at::Scalar & alpha) const {
    return at::_ops::sspaddmm::call(const_cast<Tensor&>(*this), mat1, mat2, beta, alpha);
}

// aten::stft(Tensor self, int n_fft, int? hop_length=None, int? win_length=None, Tensor? window=None, bool normalized=False, bool? onesided=None, bool? return_complex=None, bool? align_to_window=None) -> Tensor
inline at::Tensor Tensor::stft(int64_t n_fft, ::std::optional<int64_t> hop_length, ::std::optional<int64_t> win_length, const ::std::optional<at::Tensor> & window, bool normalized, ::std::optional<bool> onesided, ::std::optional<bool> return_complex, ::std::optional<bool> align_to_window) const {
    return at::_ops::stft::call(const_cast<Tensor&>(*this), n_fft, hop_length, win_length, window, normalized, onesided, return_complex, align_to_window);
}

// aten::stft.center(Tensor self, int n_fft, int? hop_length=None, int? win_length=None, Tensor? window=None, bool center=True, str pad_mode="reflect", bool normalized=False, bool? onesided=None, bool? return_complex=None, bool? align_to_window=None) -> Tensor
inline at::Tensor Tensor::stft(int64_t n_fft, ::std::optional<int64_t> hop_length, ::std::optional<int64_t> win_length, const ::std::optional<at::Tensor> & window, bool center, c10::string_view pad_mode, bool normalized, ::std::optional<bool> onesided, ::std::optional<bool> return_complex, ::std::optional<bool> align_to_window) const {
    return at::_ops::stft_center::call(const_cast<Tensor&>(*this), n_fft, hop_length, win_length, window, center, pad_mode, normalized, onesided, return_complex, align_to_window);
}

// aten::istft(Tensor self, int n_fft, int? hop_length=None, int? win_length=None, Tensor? window=None, bool center=True, bool normalized=False, bool? onesided=None, int? length=None, bool return_complex=False) -> Tensor
inline at::Tensor Tensor::istft(int64_t n_fft, ::std::optional<int64_t> hop_length, ::std::optional<int64_t> win_length, const ::std::optional<at::Tensor> & window, bool center, bool normalized, ::std::optional<bool> onesided, ::std::optional<int64_t> length, bool return_complex) const {
    return at::_ops::istft::call(const_cast<Tensor&>(*this), n_fft, hop_length, win_length, window, center, normalized, onesided, length, return_complex);
}

// aten::stride.Dimname(Tensor self, Dimname dim) -> int
inline int64_t Tensor::stride(at::Dimname dim) const {
    return at::_ops::stride_Dimname::call(const_cast<Tensor&>(*this), dim);
}

// aten::sum(Tensor self, *, ScalarType? dtype=None) -> Tensor
inline at::Tensor Tensor::sum(::std::optional<at::ScalarType> dtype) const {
    return at::_ops::sum::call(const_cast<Tensor&>(*this), dtype);
}

// aten::sum.dim_IntList(Tensor self, int[1]? dim, bool keepdim=False, *, ScalarType? dtype=None) -> Tensor
inline at::Tensor Tensor::sum(at::OptionalIntArrayRef dim, bool keepdim, ::std::optional<at::ScalarType> dtype) const {
    return at::_ops::sum_dim_IntList::call(const_cast<Tensor&>(*this), dim, keepdim, dtype);
}

// aten::sum.dim_DimnameList(Tensor self, Dimname[1] dim, bool keepdim=False, *, ScalarType? dtype=None) -> Tensor
inline at::Tensor Tensor::sum(at::DimnameList dim, bool keepdim, ::std::optional<at::ScalarType> dtype) const {
    return at::_ops::sum_dim_DimnameList::call(const_cast<Tensor&>(*this), dim, keepdim, dtype);
}

// aten::nansum(Tensor self, int[1]? dim=None, bool keepdim=False, *, ScalarType? dtype=None) -> Tensor
inline at::Tensor Tensor::nansum(at::OptionalIntArrayRef dim, bool keepdim, ::std::optional<at::ScalarType> dtype) const {
    return at::_ops::nansum::call(const_cast<Tensor&>(*this), dim, keepdim, dtype);
}

// aten::sum_to_size(Tensor self, SymInt[] size) -> Tensor
inline at::Tensor Tensor::sum_to_size(at::IntArrayRef size) const {
    return at::_ops::sum_to_size::call(const_cast<Tensor&>(*this), c10::fromIntArrayRefSlow(size));
}

// aten::sum_to_size(Tensor self, SymInt[] size) -> Tensor
inline at::Tensor Tensor::sum_to_size_symint(c10::SymIntArrayRef size) const {
    return at::_ops::sum_to_size::call(const_cast<Tensor&>(*this), size);
}

// aten::sqrt(Tensor self) -> Tensor
inline at::Tensor Tensor::sqrt() const {
    return at::_ops::sqrt::call(const_cast<Tensor&>(*this));
}

// aten::sqrt_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::sqrt_() const {
    return at::_ops::sqrt_::call(const_cast<Tensor&>(*this));
}

// aten::square(Tensor self) -> Tensor
inline at::Tensor Tensor::square() const {
    return at::_ops::square::call(const_cast<Tensor&>(*this));
}

// aten::square_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::square_() const {
    return at::_ops::square_::call(const_cast<Tensor&>(*this));
}

// aten::std(Tensor self, bool unbiased=True) -> Tensor
inline at::Tensor Tensor::std(bool unbiased) const {
    return at::_ops::std::call(const_cast<Tensor&>(*this), unbiased);
}

// aten::std.dim(Tensor self, int[1]? dim, bool unbiased=True, bool keepdim=False) -> Tensor
inline at::Tensor Tensor::std(at::OptionalIntArrayRef dim, bool unbiased, bool keepdim) const {
    return at::_ops::std_dim::call(const_cast<Tensor&>(*this), dim, unbiased, keepdim);
}

// aten::std.correction(Tensor self, int[1]? dim=None, *, Scalar? correction=None, bool keepdim=False) -> Tensor
inline at::Tensor Tensor::std(at::OptionalIntArrayRef dim, const ::std::optional<at::Scalar> & correction, bool keepdim) const {
    return at::_ops::std_correction::call(const_cast<Tensor&>(*this), dim, correction, keepdim);
}

// aten::std.names_dim(Tensor self, Dimname[1] dim, bool unbiased=True, bool keepdim=False) -> Tensor
inline at::Tensor Tensor::std(at::DimnameList dim, bool unbiased, bool keepdim) const {
    return at::_ops::std_names_dim::call(const_cast<Tensor&>(*this), dim, unbiased, keepdim);
}

// aten::std.correction_names(Tensor self, Dimname[1] dim, *, Scalar? correction=None, bool keepdim=False) -> Tensor
inline at::Tensor Tensor::std(at::DimnameList dim, const ::std::optional<at::Scalar> & correction, bool keepdim) const {
    return at::_ops::std_correction_names::call(const_cast<Tensor&>(*this), dim, correction, keepdim);
}

// aten::prod(Tensor self, *, ScalarType? dtype=None) -> Tensor
inline at::Tensor Tensor::prod(::std::optional<at::ScalarType> dtype) const {
    return at::_ops::prod::call(const_cast<Tensor&>(*this), dtype);
}

// aten::prod.dim_int(Tensor self, int dim, bool keepdim=False, *, ScalarType? dtype=None) -> Tensor
inline at::Tensor Tensor::prod(int64_t dim, bool keepdim, ::std::optional<at::ScalarType> dtype) const {
    return at::_ops::prod_dim_int::call(const_cast<Tensor&>(*this), dim, keepdim, dtype);
}

// aten::prod.dim_Dimname(Tensor self, Dimname dim, bool keepdim=False, *, ScalarType? dtype=None) -> Tensor
inline at::Tensor Tensor::prod(at::Dimname dim, bool keepdim, ::std::optional<at::ScalarType> dtype) const {
    return at::_ops::prod_dim_Dimname::call(const_cast<Tensor&>(*this), dim, keepdim, dtype);
}

// aten::t(Tensor(a) self) -> Tensor(a)
inline at::Tensor Tensor::t() const {
    return at::_ops::t::call(const_cast<Tensor&>(*this));
}

// aten::t_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::t_() const {
    return at::_ops::t_::call(const_cast<Tensor&>(*this));
}

// aten::tan(Tensor self) -> Tensor
inline at::Tensor Tensor::tan() const {
    return at::_ops::tan::call(const_cast<Tensor&>(*this));
}

// aten::tan_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::tan_() const {
    return at::_ops::tan_::call(const_cast<Tensor&>(*this));
}

// aten::tanh(Tensor self) -> Tensor
inline at::Tensor Tensor::tanh() const {
    return at::_ops::tanh::call(const_cast<Tensor&>(*this));
}

// aten::tanh_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::tanh_() const {
    return at::_ops::tanh_::call(const_cast<Tensor&>(*this));
}

// aten::tile(Tensor self, SymInt[] dims) -> Tensor
inline at::Tensor Tensor::tile(at::IntArrayRef dims) const {
    return at::_ops::tile::call(const_cast<Tensor&>(*this), c10::fromIntArrayRefSlow(dims));
}

// aten::tile(Tensor self, SymInt[] dims) -> Tensor
inline at::Tensor Tensor::tile_symint(c10::SymIntArrayRef dims) const {
    return at::_ops::tile::call(const_cast<Tensor&>(*this), dims);
}

// aten::transpose.int(Tensor(a) self, int dim0, int dim1) -> Tensor(a)
inline at::Tensor Tensor::transpose(int64_t dim0, int64_t dim1) const {
    return at::_ops::transpose_int::call(const_cast<Tensor&>(*this), dim0, dim1);
}

// aten::transpose.Dimname(Tensor(a) self, Dimname dim0, Dimname dim1) -> Tensor(a)
inline at::Tensor Tensor::transpose(at::Dimname dim0, at::Dimname dim1) const {
    return at::_ops::transpose_Dimname::call(const_cast<Tensor&>(*this), dim0, dim1);
}

// aten::transpose_(Tensor(a!) self, int dim0, int dim1) -> Tensor(a!)
inline at::Tensor & Tensor::transpose_(int64_t dim0, int64_t dim1) const {
    return at::_ops::transpose_::call(const_cast<Tensor&>(*this), dim0, dim1);
}

// aten::flip(Tensor self, int[] dims) -> Tensor
inline at::Tensor Tensor::flip(at::IntArrayRef dims) const {
    return at::_ops::flip::call(const_cast<Tensor&>(*this), dims);
}

// aten::fliplr(Tensor self) -> Tensor
inline at::Tensor Tensor::fliplr() const {
    return at::_ops::fliplr::call(const_cast<Tensor&>(*this));
}

// aten::flipud(Tensor self) -> Tensor
inline at::Tensor Tensor::flipud() const {
    return at::_ops::flipud::call(const_cast<Tensor&>(*this));
}

// aten::roll(Tensor self, SymInt[1] shifts, int[1] dims=[]) -> Tensor
inline at::Tensor Tensor::roll(at::IntArrayRef shifts, at::IntArrayRef dims) const {
    return at::_ops::roll::call(const_cast<Tensor&>(*this), c10::fromIntArrayRefSlow(shifts), dims);
}

// aten::roll(Tensor self, SymInt[1] shifts, int[1] dims=[]) -> Tensor
inline at::Tensor Tensor::roll_symint(c10::SymIntArrayRef shifts, at::IntArrayRef dims) const {
    return at::_ops::roll::call(const_cast<Tensor&>(*this), shifts, dims);
}

// aten::rot90(Tensor self, int k=1, int[] dims=[0,1]) -> Tensor
inline at::Tensor Tensor::rot90(int64_t k, at::IntArrayRef dims) const {
    return at::_ops::rot90::call(const_cast<Tensor&>(*this), k, dims);
}

// aten::_nested_tensor_size(Tensor self) -> Tensor
inline at::Tensor Tensor::_nested_tensor_size() const {
    return at::_ops::_nested_tensor_size::call(const_cast<Tensor&>(*this));
}

// aten::_nested_tensor_strides(Tensor self) -> Tensor
inline at::Tensor Tensor::_nested_tensor_strides() const {
    return at::_ops::_nested_tensor_strides::call(const_cast<Tensor&>(*this));
}

// aten::_nested_tensor_storage_offsets(Tensor self) -> Tensor
inline at::Tensor Tensor::_nested_tensor_storage_offsets() const {
    return at::_ops::_nested_tensor_storage_offsets::call(const_cast<Tensor&>(*this));
}

// aten::trunc(Tensor self) -> Tensor
inline at::Tensor Tensor::trunc() const {
    return at::_ops::trunc::call(const_cast<Tensor&>(*this));
}

// aten::trunc_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::trunc_() const {
    return at::_ops::trunc_::call(const_cast<Tensor&>(*this));
}

// aten::fix(Tensor self) -> Tensor
inline at::Tensor Tensor::fix() const {
    return at::_ops::fix::call(const_cast<Tensor&>(*this));
}

// aten::fix_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::fix_() const {
    return at::_ops::fix_::call(const_cast<Tensor&>(*this));
}

// aten::type_as(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::type_as(const at::Tensor & other) const {
    return at::_ops::type_as::call(const_cast<Tensor&>(*this), other);
}

// aten::unsqueeze(Tensor(a) self, int dim) -> Tensor(a)
inline at::Tensor Tensor::unsqueeze(int64_t dim) const {
    return at::_ops::unsqueeze::call(const_cast<Tensor&>(*this), dim);
}

// aten::unsqueeze_(Tensor(a!) self, int dim) -> Tensor(a!)
inline at::Tensor & Tensor::unsqueeze_(int64_t dim) const {
    return at::_ops::unsqueeze_::call(const_cast<Tensor&>(*this), dim);
}

// aten::var(Tensor self, bool unbiased=True) -> Tensor
inline at::Tensor Tensor::var(bool unbiased) const {
    return at::_ops::var::call(const_cast<Tensor&>(*this), unbiased);
}

// aten::var.dim(Tensor self, int[1]? dim, bool unbiased=True, bool keepdim=False) -> Tensor
inline at::Tensor Tensor::var(at::OptionalIntArrayRef dim, bool unbiased, bool keepdim) const {
    return at::_ops::var_dim::call(const_cast<Tensor&>(*this), dim, unbiased, keepdim);
}

// aten::var.correction(Tensor self, int[1]? dim=None, *, Scalar? correction=None, bool keepdim=False) -> Tensor
inline at::Tensor Tensor::var(at::OptionalIntArrayRef dim, const ::std::optional<at::Scalar> & correction, bool keepdim) const {
    return at::_ops::var_correction::call(const_cast<Tensor&>(*this), dim, correction, keepdim);
}

// aten::var.names_dim(Tensor self, Dimname[1] dim, bool unbiased=True, bool keepdim=False) -> Tensor
inline at::Tensor Tensor::var(at::DimnameList dim, bool unbiased, bool keepdim) const {
    return at::_ops::var_names_dim::call(const_cast<Tensor&>(*this), dim, unbiased, keepdim);
}

// aten::var.correction_names(Tensor self, Dimname[1] dim, *, Scalar? correction=None, bool keepdim=False) -> Tensor
inline at::Tensor Tensor::var(at::DimnameList dim, const ::std::optional<at::Scalar> & correction, bool keepdim) const {
    return at::_ops::var_correction_names::call(const_cast<Tensor&>(*this), dim, correction, keepdim);
}

// aten::view_as(Tensor(a) self, Tensor other) -> Tensor(a)
inline at::Tensor Tensor::view_as(const at::Tensor & other) const {
    return at::_ops::view_as::call(const_cast<Tensor&>(*this), other);
}

// aten::where.self(Tensor condition, Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::where(const at::Tensor & condition, const at::Tensor & other) const {
    return at::_ops::where_self::call(condition, const_cast<Tensor&>(*this), other);
}

// aten::where.ScalarOther(Tensor condition, Tensor self, Scalar other) -> Tensor
inline at::Tensor Tensor::where(const at::Tensor & condition, const at::Scalar & other) const {
    return at::_ops::where_ScalarOther::call(condition, const_cast<Tensor&>(*this), other);
}

// aten::norm.ScalarOpt_dtype(Tensor self, Scalar? p, *, ScalarType dtype) -> Tensor
inline at::Tensor Tensor::norm(const ::std::optional<at::Scalar> & p, at::ScalarType dtype) const {
    return at::_ops::norm_ScalarOpt_dtype::call(const_cast<Tensor&>(*this), p, dtype);
}

// aten::norm.Scalar(Tensor self, Scalar p=2) -> Tensor
inline at::Tensor Tensor::norm(const at::Scalar & p) const {
    return at::_ops::norm_Scalar::call(const_cast<Tensor&>(*this), p);
}

// aten::norm.ScalarOpt_dim_dtype(Tensor self, Scalar? p, int[1] dim, bool keepdim, *, ScalarType dtype) -> Tensor
inline at::Tensor Tensor::norm(const ::std::optional<at::Scalar> & p, at::IntArrayRef dim, bool keepdim, at::ScalarType dtype) const {
    return at::_ops::norm_ScalarOpt_dim_dtype::call(const_cast<Tensor&>(*this), p, dim, keepdim, dtype);
}

// aten::norm.ScalarOpt_dim(Tensor self, Scalar? p, int[1] dim, bool keepdim=False) -> Tensor
inline at::Tensor Tensor::norm(const ::std::optional<at::Scalar> & p, at::IntArrayRef dim, bool keepdim) const {
    return at::_ops::norm_ScalarOpt_dim::call(const_cast<Tensor&>(*this), p, dim, keepdim);
}

// aten::norm.names_ScalarOpt_dim_dtype(Tensor self, Scalar? p, Dimname[1] dim, bool keepdim, *, ScalarType dtype) -> Tensor
inline at::Tensor Tensor::norm(const ::std::optional<at::Scalar> & p, at::DimnameList dim, bool keepdim, at::ScalarType dtype) const {
    return at::_ops::norm_names_ScalarOpt_dim_dtype::call(const_cast<Tensor&>(*this), p, dim, keepdim, dtype);
}

// aten::norm.names_ScalarOpt_dim(Tensor self, Scalar? p, Dimname[1] dim, bool keepdim=False) -> Tensor
inline at::Tensor Tensor::norm(const ::std::optional<at::Scalar> & p, at::DimnameList dim, bool keepdim) const {
    return at::_ops::norm_names_ScalarOpt_dim::call(const_cast<Tensor&>(*this), p, dim, keepdim);
}

// aten::frexp.Tensor(Tensor self) -> (Tensor mantissa, Tensor exponent)
inline ::std::tuple<at::Tensor,at::Tensor> Tensor::frexp() const {
    return at::_ops::frexp_Tensor::call(const_cast<Tensor&>(*this));
}

// aten::clone(Tensor self, *, MemoryFormat? memory_format=None) -> Tensor
inline at::Tensor Tensor::clone(::std::optional<at::MemoryFormat> memory_format) const {
    return at::_ops::clone::call(const_cast<Tensor&>(*this), memory_format);
}

// aten::positive(Tensor(a) self) -> Tensor(a)
inline at::Tensor Tensor::positive() const {
    return at::_ops::positive::call(const_cast<Tensor&>(*this));
}

// aten::resize_as_(Tensor(a!) self, Tensor the_template, *, MemoryFormat? memory_format=None) -> Tensor(a!)
inline const at::Tensor & Tensor::resize_as_(const at::Tensor & the_template, ::std::optional<at::MemoryFormat> memory_format) const {
    return at::_ops::resize_as_::call(const_cast<Tensor&>(*this), the_template, memory_format);
}

// aten::resize_as_sparse_(Tensor(a!) self, Tensor the_template) -> Tensor(a!)
inline const at::Tensor & Tensor::resize_as_sparse_(const at::Tensor & the_template) const {
    return at::_ops::resize_as_sparse_::call(const_cast<Tensor&>(*this), the_template);
}

// aten::zero_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::zero_() const {
    return at::_ops::zero_::call(const_cast<Tensor&>(*this));
}

// aten::sub.Tensor(Tensor self, Tensor other, *, Scalar alpha=1) -> Tensor
inline at::Tensor Tensor::sub(const at::Tensor & other, const at::Scalar & alpha) const {
    return at::_ops::sub_Tensor::call(const_cast<Tensor&>(*this), other, alpha);
}

// aten::sub_.Tensor(Tensor(a!) self, Tensor other, *, Scalar alpha=1) -> Tensor(a!)
inline at::Tensor & Tensor::sub_(const at::Tensor & other, const at::Scalar & alpha) const {
    return at::_ops::sub__Tensor::call(const_cast<Tensor&>(*this), other, alpha);
}

// aten::sub.Scalar(Tensor self, Scalar other, Scalar alpha=1) -> Tensor
inline at::Tensor Tensor::sub(const at::Scalar & other, const at::Scalar & alpha) const {
    return at::_ops::sub_Scalar::call(const_cast<Tensor&>(*this), other, alpha);
}

// aten::sub_.Scalar(Tensor(a!) self, Scalar other, Scalar alpha=1) -> Tensor(a!)
inline at::Tensor & Tensor::sub_(const at::Scalar & other, const at::Scalar & alpha) const {
    return at::_ops::sub__Scalar::call(const_cast<Tensor&>(*this), other, alpha);
}

// aten::subtract.Tensor(Tensor self, Tensor other, *, Scalar alpha=1) -> Tensor
inline at::Tensor Tensor::subtract(const at::Tensor & other, const at::Scalar & alpha) const {
    return at::_ops::subtract_Tensor::call(const_cast<Tensor&>(*this), other, alpha);
}

// aten::subtract_.Tensor(Tensor(a!) self, Tensor other, *, Scalar alpha=1) -> Tensor(a!)
inline at::Tensor & Tensor::subtract_(const at::Tensor & other, const at::Scalar & alpha) const {
    return at::_ops::subtract__Tensor::call(const_cast<Tensor&>(*this), other, alpha);
}

// aten::subtract.Scalar(Tensor self, Scalar other, Scalar alpha=1) -> Tensor
inline at::Tensor Tensor::subtract(const at::Scalar & other, const at::Scalar & alpha) const {
    return at::_ops::subtract_Scalar::call(const_cast<Tensor&>(*this), other, alpha);
}

// aten::subtract_.Scalar(Tensor(a!) self, Scalar other, Scalar alpha=1) -> Tensor(a!)
inline at::Tensor & Tensor::subtract_(const at::Scalar & other, const at::Scalar & alpha) const {
    return at::_ops::subtract__Scalar::call(const_cast<Tensor&>(*this), other, alpha);
}

// aten::heaviside(Tensor self, Tensor values) -> Tensor
inline at::Tensor Tensor::heaviside(const at::Tensor & values) const {
    return at::_ops::heaviside::call(const_cast<Tensor&>(*this), values);
}

// aten::heaviside_(Tensor(a!) self, Tensor values) -> Tensor(a!)
inline at::Tensor & Tensor::heaviside_(const at::Tensor & values) const {
    return at::_ops::heaviside_::call(const_cast<Tensor&>(*this), values);
}

// aten::addmm(Tensor self, Tensor mat1, Tensor mat2, *, Scalar beta=1, Scalar alpha=1) -> Tensor
inline at::Tensor Tensor::addmm(const at::Tensor & mat1, const at::Tensor & mat2, const at::Scalar & beta, const at::Scalar & alpha) const {
    return at::_ops::addmm::call(const_cast<Tensor&>(*this), mat1, mat2, beta, alpha);
}

// aten::addmm_(Tensor(a!) self, Tensor mat1, Tensor mat2, *, Scalar beta=1, Scalar alpha=1) -> Tensor(a!)
inline at::Tensor & Tensor::addmm_(const at::Tensor & mat1, const at::Tensor & mat2, const at::Scalar & beta, const at::Scalar & alpha) const {
    return at::_ops::addmm_::call(const_cast<Tensor&>(*this), mat1, mat2, beta, alpha);
}

// aten::_addmm_activation(Tensor self, Tensor mat1, Tensor mat2, *, Scalar beta=1, Scalar alpha=1, bool use_gelu=False) -> Tensor
inline at::Tensor Tensor::_addmm_activation(const at::Tensor & mat1, const at::Tensor & mat2, const at::Scalar & beta, const at::Scalar & alpha, bool use_gelu) const {
    return at::_ops::_addmm_activation::call(const_cast<Tensor&>(*this), mat1, mat2, beta, alpha, use_gelu);
}

// aten::sparse_resize_(Tensor(a!) self, int[] size, int sparse_dim, int dense_dim) -> Tensor(a!)
inline const at::Tensor & Tensor::sparse_resize_(at::IntArrayRef size, int64_t sparse_dim, int64_t dense_dim) const {
    return at::_ops::sparse_resize_::call(const_cast<Tensor&>(*this), size, sparse_dim, dense_dim);
}

// aten::sparse_resize_and_clear_(Tensor(a!) self, int[] size, int sparse_dim, int dense_dim) -> Tensor(a!)
inline const at::Tensor & Tensor::sparse_resize_and_clear_(at::IntArrayRef size, int64_t sparse_dim, int64_t dense_dim) const {
    return at::_ops::sparse_resize_and_clear_::call(const_cast<Tensor&>(*this), size, sparse_dim, dense_dim);
}

// aten::sparse_mask(Tensor self, Tensor mask) -> Tensor
inline at::Tensor Tensor::sparse_mask(const at::Tensor & mask) const {
    return at::_ops::sparse_mask::call(const_cast<Tensor&>(*this), mask);
}

// aten::_sparse_mask_projection(Tensor self, Tensor mask, bool accumulate_matches=False) -> Tensor
inline at::Tensor Tensor::_sparse_mask_projection(const at::Tensor & mask, bool accumulate_matches) const {
    return at::_ops::_sparse_mask_projection::call(const_cast<Tensor&>(*this), mask, accumulate_matches);
}

// aten::to_dense(Tensor self, ScalarType? dtype=None, *, bool? masked_grad=None) -> Tensor
inline at::Tensor Tensor::to_dense(::std::optional<at::ScalarType> dtype, ::std::optional<bool> masked_grad) const {
    return at::_ops::to_dense::call(const_cast<Tensor&>(*this), dtype, masked_grad);
}

// aten::_to_dense(Tensor self, ScalarType? dtype=None, bool? masked_grad=None) -> Tensor
inline at::Tensor Tensor::_to_dense(::std::optional<at::ScalarType> dtype, ::std::optional<bool> masked_grad) const {
    return at::_ops::_to_dense::call(const_cast<Tensor&>(*this), dtype, masked_grad);
}

// aten::sparse_dim(Tensor self) -> int
inline int64_t Tensor::sparse_dim() const {
    return at::_ops::sparse_dim::call(const_cast<Tensor&>(*this));
}

// aten::_dimI(Tensor self) -> int
inline int64_t Tensor::_dimI() const {
    return at::_ops::_dimI::call(const_cast<Tensor&>(*this));
}

// aten::dense_dim(Tensor self) -> int
inline int64_t Tensor::dense_dim() const {
    return at::_ops::dense_dim::call(const_cast<Tensor&>(*this));
}

// aten::_dimV(Tensor self) -> int
inline int64_t Tensor::_dimV() const {
    return at::_ops::_dimV::call(const_cast<Tensor&>(*this));
}

// aten::_nnz(Tensor self) -> int
inline int64_t Tensor::_nnz() const {
    return at::_ops::_nnz::call(const_cast<Tensor&>(*this));
}

// aten::coalesce(Tensor(a) self) -> Tensor(a)
inline at::Tensor Tensor::coalesce() const {
    return at::_ops::coalesce::call(const_cast<Tensor&>(*this));
}

// aten::is_coalesced(Tensor self) -> bool
inline bool Tensor::is_coalesced() const {
    return at::_ops::is_coalesced::call(const_cast<Tensor&>(*this));
}

// aten::_indices(Tensor(a) self) -> Tensor(a)
inline at::Tensor Tensor::_indices() const {
    return at::_ops::_indices::call(const_cast<Tensor&>(*this));
}

// aten::_values(Tensor(a) self) -> Tensor(a)
inline at::Tensor Tensor::_values() const {
    return at::_ops::_values::call(const_cast<Tensor&>(*this));
}

// aten::_coalesced_(Tensor(a!) self, bool coalesced) -> Tensor(a!)
inline at::Tensor & Tensor::_coalesced_(bool coalesced) const {
    return at::_ops::_coalesced_::call(const_cast<Tensor&>(*this), coalesced);
}

// aten::indices(Tensor(a) self) -> Tensor(a)
inline at::Tensor Tensor::indices() const {
    return at::_ops::indices::call(const_cast<Tensor&>(*this));
}

// aten::values(Tensor(a) self) -> Tensor(a)
inline at::Tensor Tensor::values() const {
    return at::_ops::values::call(const_cast<Tensor&>(*this));
}

// aten::crow_indices(Tensor(a) self) -> Tensor(a)
inline at::Tensor Tensor::crow_indices() const {
    return at::_ops::crow_indices::call(const_cast<Tensor&>(*this));
}

// aten::col_indices(Tensor(a) self) -> Tensor(a)
inline at::Tensor Tensor::col_indices() const {
    return at::_ops::col_indices::call(const_cast<Tensor&>(*this));
}

// aten::ccol_indices(Tensor(a) self) -> Tensor(a)
inline at::Tensor Tensor::ccol_indices() const {
    return at::_ops::ccol_indices::call(const_cast<Tensor&>(*this));
}

// aten::row_indices(Tensor(a) self) -> Tensor(a)
inline at::Tensor Tensor::row_indices() const {
    return at::_ops::row_indices::call(const_cast<Tensor&>(*this));
}

// aten::unbind.int(Tensor(a -> *) self, int dim=0) -> Tensor(a)[]
inline ::std::vector<at::Tensor> Tensor::unbind(int64_t dim) const {
    return at::_ops::unbind_int::call(const_cast<Tensor&>(*this), dim);
}

// aten::unbind.Dimname(Tensor(a -> *) self, Dimname dim) -> Tensor(a)[]
inline ::std::vector<at::Tensor> Tensor::unbind(at::Dimname dim) const {
    return at::_ops::unbind_Dimname::call(const_cast<Tensor&>(*this), dim);
}

// aten::to_sparse.sparse_dim(Tensor self, int sparse_dim) -> Tensor
inline at::Tensor Tensor::to_sparse(int64_t sparse_dim) const {
    return at::_ops::to_sparse_sparse_dim::call(const_cast<Tensor&>(*this), sparse_dim);
}

// aten::_to_sparse.sparse_dim(Tensor self, int sparse_dim) -> Tensor
inline at::Tensor Tensor::_to_sparse(int64_t sparse_dim) const {
    return at::_ops::_to_sparse_sparse_dim::call(const_cast<Tensor&>(*this), sparse_dim);
}

// aten::to_sparse(Tensor self, *, Layout? layout=None, int[2]? blocksize=None, int? dense_dim=None) -> Tensor
inline at::Tensor Tensor::to_sparse(::std::optional<at::Layout> layout, at::OptionalIntArrayRef blocksize, ::std::optional<int64_t> dense_dim) const {
    return at::_ops::to_sparse::call(const_cast<Tensor&>(*this), layout, blocksize, dense_dim);
}

// aten::_to_sparse(Tensor self, *, Layout? layout=None, int[2]? blocksize=None, int? dense_dim=None) -> Tensor
inline at::Tensor Tensor::_to_sparse(::std::optional<at::Layout> layout, at::OptionalIntArrayRef blocksize, ::std::optional<int64_t> dense_dim) const {
    return at::_ops::_to_sparse::call(const_cast<Tensor&>(*this), layout, blocksize, dense_dim);
}

// aten::to_sparse_csr(Tensor self, int? dense_dim=None) -> Tensor
inline at::Tensor Tensor::to_sparse_csr(::std::optional<int64_t> dense_dim) const {
    return at::_ops::to_sparse_csr::call(const_cast<Tensor&>(*this), dense_dim);
}

// aten::_to_sparse_csr(Tensor self, int? dense_dim=None) -> Tensor
inline at::Tensor Tensor::_to_sparse_csr(::std::optional<int64_t> dense_dim) const {
    return at::_ops::_to_sparse_csr::call(const_cast<Tensor&>(*this), dense_dim);
}

// aten::to_sparse_csc(Tensor self, int? dense_dim=None) -> Tensor
inline at::Tensor Tensor::to_sparse_csc(::std::optional<int64_t> dense_dim) const {
    return at::_ops::to_sparse_csc::call(const_cast<Tensor&>(*this), dense_dim);
}

// aten::_to_sparse_csc(Tensor self, int? dense_dim=None) -> Tensor
inline at::Tensor Tensor::_to_sparse_csc(::std::optional<int64_t> dense_dim) const {
    return at::_ops::_to_sparse_csc::call(const_cast<Tensor&>(*this), dense_dim);
}

// aten::to_sparse_bsr(Tensor self, int[2] blocksize, int? dense_dim=None) -> Tensor
inline at::Tensor Tensor::to_sparse_bsr(at::IntArrayRef blocksize, ::std::optional<int64_t> dense_dim) const {
    return at::_ops::to_sparse_bsr::call(const_cast<Tensor&>(*this), blocksize, dense_dim);
}

// aten::_to_sparse_bsr(Tensor self, int[2] blocksize, int? dense_dim=None) -> Tensor
inline at::Tensor Tensor::_to_sparse_bsr(at::IntArrayRef blocksize, ::std::optional<int64_t> dense_dim) const {
    return at::_ops::_to_sparse_bsr::call(const_cast<Tensor&>(*this), blocksize, dense_dim);
}

// aten::to_sparse_bsc(Tensor self, int[2] blocksize, int? dense_dim=None) -> Tensor
inline at::Tensor Tensor::to_sparse_bsc(at::IntArrayRef blocksize, ::std::optional<int64_t> dense_dim) const {
    return at::_ops::to_sparse_bsc::call(const_cast<Tensor&>(*this), blocksize, dense_dim);
}

// aten::_to_sparse_bsc(Tensor self, int[2] blocksize, int? dense_dim=None) -> Tensor
inline at::Tensor Tensor::_to_sparse_bsc(at::IntArrayRef blocksize, ::std::optional<int64_t> dense_dim) const {
    return at::_ops::_to_sparse_bsc::call(const_cast<Tensor&>(*this), blocksize, dense_dim);
}

// aten::to_mkldnn(Tensor self, ScalarType? dtype=None) -> Tensor
inline at::Tensor Tensor::to_mkldnn(::std::optional<at::ScalarType> dtype) const {
    return at::_ops::to_mkldnn::call(const_cast<Tensor&>(*this), dtype);
}

// aten::dequantize.self(Tensor self) -> Tensor
inline at::Tensor Tensor::dequantize() const {
    return at::_ops::dequantize_self::call(const_cast<Tensor&>(*this));
}

// aten::q_scale(Tensor self) -> float
inline double Tensor::q_scale() const {
    return at::_ops::q_scale::call(const_cast<Tensor&>(*this));
}

// aten::q_zero_point(Tensor self) -> int
inline int64_t Tensor::q_zero_point() const {
    return at::_ops::q_zero_point::call(const_cast<Tensor&>(*this));
}

// aten::q_per_channel_scales(Tensor self) -> Tensor
inline at::Tensor Tensor::q_per_channel_scales() const {
    return at::_ops::q_per_channel_scales::call(const_cast<Tensor&>(*this));
}

// aten::q_per_channel_zero_points(Tensor self) -> Tensor
inline at::Tensor Tensor::q_per_channel_zero_points() const {
    return at::_ops::q_per_channel_zero_points::call(const_cast<Tensor&>(*this));
}

// aten::q_per_channel_axis(Tensor self) -> int
inline int64_t Tensor::q_per_channel_axis() const {
    return at::_ops::q_per_channel_axis::call(const_cast<Tensor&>(*this));
}

// aten::int_repr(Tensor self) -> Tensor
inline at::Tensor Tensor::int_repr() const {
    return at::_ops::int_repr::call(const_cast<Tensor&>(*this));
}

// aten::qscheme(Tensor self) -> QScheme
inline at::QScheme Tensor::qscheme() const {
    return at::_ops::qscheme::call(const_cast<Tensor&>(*this));
}

// aten::_autocast_to_reduced_precision(Tensor(a) self, bool cuda_enabled, bool cpu_enabled, ScalarType cuda_dtype, ScalarType cpu_dtype) -> Tensor(a)
inline at::Tensor Tensor::_autocast_to_reduced_precision(bool cuda_enabled, bool cpu_enabled, at::ScalarType cuda_dtype, at::ScalarType cpu_dtype) const {
    return at::_ops::_autocast_to_reduced_precision::call(const_cast<Tensor&>(*this), cuda_enabled, cpu_enabled, cuda_dtype, cpu_dtype);
}

// aten::_autocast_to_full_precision(Tensor(a) self, bool cuda_enabled, bool cpu_enabled) -> Tensor(a)
inline at::Tensor Tensor::_autocast_to_full_precision(bool cuda_enabled, bool cpu_enabled) const {
    return at::_ops::_autocast_to_full_precision::call(const_cast<Tensor&>(*this), cuda_enabled, cpu_enabled);
}

// aten::to.dtype_layout(Tensor(a) self, *, ScalarType? dtype=None, Layout? layout=None, Device? device=None, bool? pin_memory=None, bool non_blocking=False, bool copy=False, MemoryFormat? memory_format=None) -> Tensor(a)
inline at::Tensor Tensor::to(at::TensorOptions options, bool non_blocking, bool copy, ::std::optional<at::MemoryFormat> memory_format) const {
    return at::_ops::to_dtype_layout::call(const_cast<Tensor&>(*this), c10::optTypeMetaToScalarType(options.dtype_opt()), options.layout_opt(), options.device_opt(), options.pinned_memory_opt(), non_blocking, copy, c10::impl::check_tensor_options_and_extract_memory_format(options, memory_format));
}

// aten::to.dtype_layout(Tensor(a) self, *, ScalarType? dtype=None, Layout? layout=None, Device? device=None, bool? pin_memory=None, bool non_blocking=False, bool copy=False, MemoryFormat? memory_format=None) -> Tensor(a)
inline at::Tensor Tensor::to(::std::optional<at::ScalarType> dtype, ::std::optional<at::Layout> layout, ::std::optional<at::Device> device, ::std::optional<bool> pin_memory, bool non_blocking, bool copy, ::std::optional<at::MemoryFormat> memory_format) const {
    return at::_ops::to_dtype_layout::call(const_cast<Tensor&>(*this), dtype, layout, device, pin_memory, non_blocking, copy, memory_format);
}

// aten::to.device(Tensor(a) self, Device device, ScalarType dtype, bool non_blocking=False, bool copy=False, MemoryFormat? memory_format=None) -> Tensor(a)
inline at::Tensor Tensor::to(at::Device device, at::ScalarType dtype, bool non_blocking, bool copy, ::std::optional<at::MemoryFormat> memory_format) const {
    return at::_ops::to_device::call(const_cast<Tensor&>(*this), device, dtype, non_blocking, copy, memory_format);
}

// aten::to.dtype(Tensor(a) self, ScalarType dtype, bool non_blocking=False, bool copy=False, MemoryFormat? memory_format=None) -> Tensor(a)
inline at::Tensor Tensor::to(at::ScalarType dtype, bool non_blocking, bool copy, ::std::optional<at::MemoryFormat> memory_format) const {
    return at::_ops::to_dtype::call(const_cast<Tensor&>(*this), dtype, non_blocking, copy, memory_format);
}

// aten::to.other(Tensor(a) self, Tensor other, bool non_blocking=False, bool copy=False, MemoryFormat? memory_format=None) -> Tensor(a)
inline at::Tensor Tensor::to(const at::Tensor & other, bool non_blocking, bool copy, ::std::optional<at::MemoryFormat> memory_format) const {
    return at::_ops::to_other::call(const_cast<Tensor&>(*this), other, non_blocking, copy, memory_format);
}

// aten::item(Tensor self) -> Scalar
inline at::Scalar Tensor::item() const {
    return at::_ops::item::call(const_cast<Tensor&>(*this));
}

// aten::set_.source_Storage(Tensor(a!) self, Storage source) -> Tensor(a!)
inline at::Tensor & Tensor::set_(at::Storage source) const {
    return at::_ops::set__source_Storage::call(const_cast<Tensor&>(*this), source);
}

// aten::set_.source_Storage_storage_offset(Tensor(a!) self, Storage source, SymInt storage_offset, SymInt[] size, SymInt[] stride=[]) -> Tensor(a!)
inline at::Tensor & Tensor::set_(at::Storage source, int64_t storage_offset, at::IntArrayRef size, at::IntArrayRef stride) const {
    return at::_ops::set__source_Storage_storage_offset::call(const_cast<Tensor&>(*this), source, storage_offset, c10::fromIntArrayRefSlow(size), c10::fromIntArrayRefSlow(stride));
}

// aten::set_.source_Storage_storage_offset(Tensor(a!) self, Storage source, SymInt storage_offset, SymInt[] size, SymInt[] stride=[]) -> Tensor(a!)
inline at::Tensor & Tensor::set__symint(at::Storage source, c10::SymInt storage_offset, c10::SymIntArrayRef size, c10::SymIntArrayRef stride) const {
    return at::_ops::set__source_Storage_storage_offset::call(const_cast<Tensor&>(*this), source, storage_offset, size, stride);
}

// aten::set_.source_Tensor_storage_offset(Tensor(a!) self, Tensor source, SymInt storage_offset, SymInt[] size, SymInt[] stride=[]) -> Tensor(a!)
inline at::Tensor & Tensor::set_(const at::Tensor & source, int64_t storage_offset, at::IntArrayRef size, at::IntArrayRef stride) const {
    return at::_ops::set__source_Tensor_storage_offset::call(const_cast<Tensor&>(*this), source, storage_offset, c10::fromIntArrayRefSlow(size), c10::fromIntArrayRefSlow(stride));
}

// aten::set_.source_Tensor_storage_offset(Tensor(a!) self, Tensor source, SymInt storage_offset, SymInt[] size, SymInt[] stride=[]) -> Tensor(a!)
inline at::Tensor & Tensor::set__symint(const at::Tensor & source, c10::SymInt storage_offset, c10::SymIntArrayRef size, c10::SymIntArrayRef stride) const {
    return at::_ops::set__source_Tensor_storage_offset::call(const_cast<Tensor&>(*this), source, storage_offset, size, stride);
}

// aten::set_.source_Tensor(Tensor(a!) self, Tensor source) -> Tensor(a!)
inline at::Tensor & Tensor::set_(const at::Tensor & source) const {
    return at::_ops::set__source_Tensor::call(const_cast<Tensor&>(*this), source);
}

// aten::set_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::set_() const {
    return at::_ops::set_::call(const_cast<Tensor&>(*this));
}

// aten::is_set_to(Tensor self, Tensor tensor) -> bool
inline bool Tensor::is_set_to(const at::Tensor & tensor) const {
    return at::_ops::is_set_to::call(const_cast<Tensor&>(*this), tensor);
}

// aten::masked_fill_.Scalar(Tensor(a!) self, Tensor mask, Scalar value) -> Tensor(a!)
inline at::Tensor & Tensor::masked_fill_(const at::Tensor & mask, const at::Scalar & value) const {
    return at::_ops::masked_fill__Scalar::call(const_cast<Tensor&>(*this), mask, value);
}

// aten::masked_fill.Scalar(Tensor self, Tensor mask, Scalar value) -> Tensor
inline at::Tensor Tensor::masked_fill(const at::Tensor & mask, const at::Scalar & value) const {
    return at::_ops::masked_fill_Scalar::call(const_cast<Tensor&>(*this), mask, value);
}

// aten::masked_fill_.Tensor(Tensor(a!) self, Tensor mask, Tensor value) -> Tensor(a!)
inline at::Tensor & Tensor::masked_fill_(const at::Tensor & mask, const at::Tensor & value) const {
    return at::_ops::masked_fill__Tensor::call(const_cast<Tensor&>(*this), mask, value);
}

// aten::masked_fill.Tensor(Tensor self, Tensor mask, Tensor value) -> Tensor
inline at::Tensor Tensor::masked_fill(const at::Tensor & mask, const at::Tensor & value) const {
    return at::_ops::masked_fill_Tensor::call(const_cast<Tensor&>(*this), mask, value);
}

// aten::masked_scatter_(Tensor(a!) self, Tensor mask, Tensor source) -> Tensor(a!)
inline at::Tensor & Tensor::masked_scatter_(const at::Tensor & mask, const at::Tensor & source) const {
    return at::_ops::masked_scatter_::call(const_cast<Tensor&>(*this), mask, source);
}

// aten::masked_scatter(Tensor self, Tensor mask, Tensor source) -> Tensor
inline at::Tensor Tensor::masked_scatter(const at::Tensor & mask, const at::Tensor & source) const {
    return at::_ops::masked_scatter::call(const_cast<Tensor&>(*this), mask, source);
}

// aten::view(Tensor(a) self, SymInt[] size) -> Tensor(a)
inline at::Tensor Tensor::view(at::IntArrayRef size) const {
    return at::_ops::view::call(const_cast<Tensor&>(*this), c10::fromIntArrayRefSlow(size));
}

// aten::view(Tensor(a) self, SymInt[] size) -> Tensor(a)
inline at::Tensor Tensor::view_symint(c10::SymIntArrayRef size) const {
    return at::_ops::view::call(const_cast<Tensor&>(*this), size);
}

// aten::view.dtype(Tensor(a) self, ScalarType dtype) -> Tensor(a)
inline at::Tensor Tensor::view(at::ScalarType dtype) const {
    return at::_ops::view_dtype::call(const_cast<Tensor&>(*this), dtype);
}

// aten::put_(Tensor(a!) self, Tensor index, Tensor source, bool accumulate=False) -> Tensor(a!)
inline at::Tensor & Tensor::put_(const at::Tensor & index, const at::Tensor & source, bool accumulate) const {
    return at::_ops::put_::call(const_cast<Tensor&>(*this), index, source, accumulate);
}

// aten::put(Tensor self, Tensor index, Tensor source, bool accumulate=False) -> Tensor
inline at::Tensor Tensor::put(const at::Tensor & index, const at::Tensor & source, bool accumulate) const {
    return at::_ops::put::call(const_cast<Tensor&>(*this), index, source, accumulate);
}

// aten::index_add_(Tensor(a!) self, int dim, Tensor index, Tensor source, *, Scalar alpha=1) -> Tensor(a!)
inline at::Tensor & Tensor::index_add_(int64_t dim, const at::Tensor & index, const at::Tensor & source, const at::Scalar & alpha) const {
    return at::_ops::index_add_::call(const_cast<Tensor&>(*this), dim, index, source, alpha);
}

// aten::index_add(Tensor self, int dim, Tensor index, Tensor source, *, Scalar alpha=1) -> Tensor
inline at::Tensor Tensor::index_add(int64_t dim, const at::Tensor & index, const at::Tensor & source, const at::Scalar & alpha) const {
    return at::_ops::index_add::call(const_cast<Tensor&>(*this), dim, index, source, alpha);
}

// aten::index_add.dimname(Tensor self, Dimname dim, Tensor index, Tensor source, *, Scalar alpha=1) -> Tensor
inline at::Tensor Tensor::index_add(at::Dimname dim, const at::Tensor & index, const at::Tensor & source, const at::Scalar & alpha) const {
    return at::_ops::index_add_dimname::call(const_cast<Tensor&>(*this), dim, index, source, alpha);
}

// aten::index_reduce_(Tensor(a!) self, int dim, Tensor index, Tensor source, str reduce, *, bool include_self=True) -> Tensor(a!)
inline at::Tensor & Tensor::index_reduce_(int64_t dim, const at::Tensor & index, const at::Tensor & source, c10::string_view reduce, bool include_self) const {
    return at::_ops::index_reduce_::call(const_cast<Tensor&>(*this), dim, index, source, reduce, include_self);
}

// aten::index_reduce(Tensor self, int dim, Tensor index, Tensor source, str reduce, *, bool include_self=True) -> Tensor
inline at::Tensor Tensor::index_reduce(int64_t dim, const at::Tensor & index, const at::Tensor & source, c10::string_view reduce, bool include_self) const {
    return at::_ops::index_reduce::call(const_cast<Tensor&>(*this), dim, index, source, reduce, include_self);
}

// aten::index_fill_.int_Scalar(Tensor(a!) self, int dim, Tensor index, Scalar value) -> Tensor(a!)
inline at::Tensor & Tensor::index_fill_(int64_t dim, const at::Tensor & index, const at::Scalar & value) const {
    return at::_ops::index_fill__int_Scalar::call(const_cast<Tensor&>(*this), dim, index, value);
}

// aten::index_fill.int_Scalar(Tensor self, int dim, Tensor index, Scalar value) -> Tensor
inline at::Tensor Tensor::index_fill(int64_t dim, const at::Tensor & index, const at::Scalar & value) const {
    return at::_ops::index_fill_int_Scalar::call(const_cast<Tensor&>(*this), dim, index, value);
}

// aten::index_fill_.int_Tensor(Tensor(a!) self, int dim, Tensor index, Tensor value) -> Tensor(a!)
inline at::Tensor & Tensor::index_fill_(int64_t dim, const at::Tensor & index, const at::Tensor & value) const {
    return at::_ops::index_fill__int_Tensor::call(const_cast<Tensor&>(*this), dim, index, value);
}

// aten::index_fill.int_Tensor(Tensor self, int dim, Tensor index, Tensor value) -> Tensor
inline at::Tensor Tensor::index_fill(int64_t dim, const at::Tensor & index, const at::Tensor & value) const {
    return at::_ops::index_fill_int_Tensor::call(const_cast<Tensor&>(*this), dim, index, value);
}

// aten::index_fill_.Dimname_Scalar(Tensor(a!) self, Dimname dim, Tensor index, Scalar value) -> Tensor(a!)
inline at::Tensor & Tensor::index_fill_(at::Dimname dim, const at::Tensor & index, const at::Scalar & value) const {
    return at::_ops::index_fill__Dimname_Scalar::call(const_cast<Tensor&>(*this), dim, index, value);
}

// aten::index_fill_.Dimname_Tensor(Tensor(a!) self, Dimname dim, Tensor index, Tensor value) -> Tensor(a!)
inline at::Tensor & Tensor::index_fill_(at::Dimname dim, const at::Tensor & index, const at::Tensor & value) const {
    return at::_ops::index_fill__Dimname_Tensor::call(const_cast<Tensor&>(*this), dim, index, value);
}

// aten::index_fill.Dimname_Scalar(Tensor self, Dimname dim, Tensor index, Scalar value) -> Tensor
inline at::Tensor Tensor::index_fill(at::Dimname dim, const at::Tensor & index, const at::Scalar & value) const {
    return at::_ops::index_fill_Dimname_Scalar::call(const_cast<Tensor&>(*this), dim, index, value);
}

// aten::index_fill.Dimname_Tensor(Tensor self, Dimname dim, Tensor index, Tensor value) -> Tensor
inline at::Tensor Tensor::index_fill(at::Dimname dim, const at::Tensor & index, const at::Tensor & value) const {
    return at::_ops::index_fill_Dimname_Tensor::call(const_cast<Tensor&>(*this), dim, index, value);
}

// aten::scatter.src(Tensor self, int dim, Tensor index, Tensor src) -> Tensor
inline at::Tensor Tensor::scatter(int64_t dim, const at::Tensor & index, const at::Tensor & src) const {
    return at::_ops::scatter_src::call(const_cast<Tensor&>(*this), dim, index, src);
}

// aten::scatter_.src(Tensor(a!) self, int dim, Tensor index, Tensor src) -> Tensor(a!)
inline at::Tensor & Tensor::scatter_(int64_t dim, const at::Tensor & index, const at::Tensor & src) const {
    return at::_ops::scatter__src::call(const_cast<Tensor&>(*this), dim, index, src);
}

// aten::scatter.value(Tensor self, int dim, Tensor index, Scalar value) -> Tensor
inline at::Tensor Tensor::scatter(int64_t dim, const at::Tensor & index, const at::Scalar & value) const {
    return at::_ops::scatter_value::call(const_cast<Tensor&>(*this), dim, index, value);
}

// aten::scatter_.value(Tensor(a!) self, int dim, Tensor index, Scalar value) -> Tensor(a!)
inline at::Tensor & Tensor::scatter_(int64_t dim, const at::Tensor & index, const at::Scalar & value) const {
    return at::_ops::scatter__value::call(const_cast<Tensor&>(*this), dim, index, value);
}

// aten::scatter.reduce(Tensor self, int dim, Tensor index, Tensor src, *, str reduce) -> Tensor
inline at::Tensor Tensor::scatter(int64_t dim, const at::Tensor & index, const at::Tensor & src, c10::string_view reduce) const {
    return at::_ops::scatter_reduce::call(const_cast<Tensor&>(*this), dim, index, src, reduce);
}

// aten::scatter_.reduce(Tensor(a!) self, int dim, Tensor index, Tensor src, *, str reduce) -> Tensor(a!)
inline at::Tensor & Tensor::scatter_(int64_t dim, const at::Tensor & index, const at::Tensor & src, c10::string_view reduce) const {
    return at::_ops::scatter__reduce::call(const_cast<Tensor&>(*this), dim, index, src, reduce);
}

// aten::scatter.value_reduce(Tensor self, int dim, Tensor index, Scalar value, *, str reduce) -> Tensor
inline at::Tensor Tensor::scatter(int64_t dim, const at::Tensor & index, const at::Scalar & value, c10::string_view reduce) const {
    return at::_ops::scatter_value_reduce::call(const_cast<Tensor&>(*this), dim, index, value, reduce);
}

// aten::scatter_.value_reduce(Tensor(a!) self, int dim, Tensor index, Scalar value, *, str reduce) -> Tensor(a!)
inline at::Tensor & Tensor::scatter_(int64_t dim, const at::Tensor & index, const at::Scalar & value, c10::string_view reduce) const {
    return at::_ops::scatter__value_reduce::call(const_cast<Tensor&>(*this), dim, index, value, reduce);
}

// aten::scatter.dimname_src(Tensor self, Dimname dim, Tensor index, Tensor src) -> Tensor
inline at::Tensor Tensor::scatter(at::Dimname dim, const at::Tensor & index, const at::Tensor & src) const {
    return at::_ops::scatter_dimname_src::call(const_cast<Tensor&>(*this), dim, index, src);
}

// aten::scatter.dimname_value(Tensor self, Dimname dim, Tensor index, Scalar value) -> Tensor
inline at::Tensor Tensor::scatter(at::Dimname dim, const at::Tensor & index, const at::Scalar & value) const {
    return at::_ops::scatter_dimname_value::call(const_cast<Tensor&>(*this), dim, index, value);
}

// aten::scatter_add(Tensor self, int dim, Tensor index, Tensor src) -> Tensor
inline at::Tensor Tensor::scatter_add(int64_t dim, const at::Tensor & index, const at::Tensor & src) const {
    return at::_ops::scatter_add::call(const_cast<Tensor&>(*this), dim, index, src);
}

// aten::scatter_add_(Tensor(a!) self, int dim, Tensor index, Tensor src) -> Tensor(a!)
inline at::Tensor & Tensor::scatter_add_(int64_t dim, const at::Tensor & index, const at::Tensor & src) const {
    return at::_ops::scatter_add_::call(const_cast<Tensor&>(*this), dim, index, src);
}

// aten::scatter_add.dimname(Tensor self, Dimname dim, Tensor index, Tensor src) -> Tensor
inline at::Tensor Tensor::scatter_add(at::Dimname dim, const at::Tensor & index, const at::Tensor & src) const {
    return at::_ops::scatter_add_dimname::call(const_cast<Tensor&>(*this), dim, index, src);
}

// aten::scatter_reduce.two(Tensor self, int dim, Tensor index, Tensor src, str reduce, *, bool include_self=True) -> Tensor
inline at::Tensor Tensor::scatter_reduce(int64_t dim, const at::Tensor & index, const at::Tensor & src, c10::string_view reduce, bool include_self) const {
    return at::_ops::scatter_reduce_two::call(const_cast<Tensor&>(*this), dim, index, src, reduce, include_self);
}

// aten::scatter_reduce_.two(Tensor(a!) self, int dim, Tensor index, Tensor src, str reduce, *, bool include_self=True) -> Tensor(a!)
inline at::Tensor & Tensor::scatter_reduce_(int64_t dim, const at::Tensor & index, const at::Tensor & src, c10::string_view reduce, bool include_self) const {
    return at::_ops::scatter_reduce__two::call(const_cast<Tensor&>(*this), dim, index, src, reduce, include_self);
}

// aten::eq_.Scalar(Tensor(a!) self, Scalar other) -> Tensor(a!)
inline at::Tensor & Tensor::eq_(const at::Scalar & other) const {
    return at::_ops::eq__Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::eq_.Tensor(Tensor(a!) self, Tensor other) -> Tensor(a!)
inline at::Tensor & Tensor::eq_(const at::Tensor & other) const {
    return at::_ops::eq__Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::bitwise_and.Scalar(Tensor self, Scalar other) -> Tensor
inline at::Tensor Tensor::bitwise_and(const at::Scalar & other) const {
    return at::_ops::bitwise_and_Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::bitwise_and.Tensor(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::bitwise_and(const at::Tensor & other) const {
    return at::_ops::bitwise_and_Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::bitwise_and_.Scalar(Tensor(a!) self, Scalar other) -> Tensor(a!)
inline at::Tensor & Tensor::bitwise_and_(const at::Scalar & other) const {
    return at::_ops::bitwise_and__Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::bitwise_and_.Tensor(Tensor(a!) self, Tensor other) -> Tensor(a!)
inline at::Tensor & Tensor::bitwise_and_(const at::Tensor & other) const {
    return at::_ops::bitwise_and__Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::__and__.Scalar(Tensor self, Scalar other) -> Tensor
inline at::Tensor Tensor::__and__(const at::Scalar & other) const {
    return at::_ops::__and___Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::__and__.Tensor(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::__and__(const at::Tensor & other) const {
    return at::_ops::__and___Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::__iand__.Scalar(Tensor(a!) self, Scalar other) -> Tensor(a!)
inline at::Tensor & Tensor::__iand__(const at::Scalar & other) const {
    return at::_ops::__iand___Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::__iand__.Tensor(Tensor(a!) self, Tensor other) -> Tensor(a!)
inline at::Tensor & Tensor::__iand__(const at::Tensor & other) const {
    return at::_ops::__iand___Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::bitwise_or.Scalar(Tensor self, Scalar other) -> Tensor
inline at::Tensor Tensor::bitwise_or(const at::Scalar & other) const {
    return at::_ops::bitwise_or_Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::bitwise_or.Tensor(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::bitwise_or(const at::Tensor & other) const {
    return at::_ops::bitwise_or_Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::bitwise_or_.Scalar(Tensor(a!) self, Scalar other) -> Tensor(a!)
inline at::Tensor & Tensor::bitwise_or_(const at::Scalar & other) const {
    return at::_ops::bitwise_or__Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::bitwise_or_.Tensor(Tensor(a!) self, Tensor other) -> Tensor(a!)
inline at::Tensor & Tensor::bitwise_or_(const at::Tensor & other) const {
    return at::_ops::bitwise_or__Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::__or__.Scalar(Tensor self, Scalar other) -> Tensor
inline at::Tensor Tensor::__or__(const at::Scalar & other) const {
    return at::_ops::__or___Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::__or__.Tensor(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::__or__(const at::Tensor & other) const {
    return at::_ops::__or___Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::__ior__.Scalar(Tensor(a!) self, Scalar other) -> Tensor(a!)
inline at::Tensor & Tensor::__ior__(const at::Scalar & other) const {
    return at::_ops::__ior___Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::__ior__.Tensor(Tensor(a!) self, Tensor other) -> Tensor(a!)
inline at::Tensor & Tensor::__ior__(const at::Tensor & other) const {
    return at::_ops::__ior___Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::bitwise_xor.Scalar(Tensor self, Scalar other) -> Tensor
inline at::Tensor Tensor::bitwise_xor(const at::Scalar & other) const {
    return at::_ops::bitwise_xor_Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::bitwise_xor.Tensor(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::bitwise_xor(const at::Tensor & other) const {
    return at::_ops::bitwise_xor_Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::bitwise_xor_.Scalar(Tensor(a!) self, Scalar other) -> Tensor(a!)
inline at::Tensor & Tensor::bitwise_xor_(const at::Scalar & other) const {
    return at::_ops::bitwise_xor__Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::bitwise_xor_.Tensor(Tensor(a!) self, Tensor other) -> Tensor(a!)
inline at::Tensor & Tensor::bitwise_xor_(const at::Tensor & other) const {
    return at::_ops::bitwise_xor__Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::__xor__.Scalar(Tensor self, Scalar other) -> Tensor
inline at::Tensor Tensor::__xor__(const at::Scalar & other) const {
    return at::_ops::__xor___Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::__xor__.Tensor(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::__xor__(const at::Tensor & other) const {
    return at::_ops::__xor___Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::__ixor__.Scalar(Tensor(a!) self, Scalar other) -> Tensor(a!)
inline at::Tensor & Tensor::__ixor__(const at::Scalar & other) const {
    return at::_ops::__ixor___Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::__ixor__.Tensor(Tensor(a!) self, Tensor other) -> Tensor(a!)
inline at::Tensor & Tensor::__ixor__(const at::Tensor & other) const {
    return at::_ops::__ixor___Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::__lshift__.Scalar(Tensor self, Scalar other) -> Tensor
inline at::Tensor Tensor::__lshift__(const at::Scalar & other) const {
    return at::_ops::__lshift___Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::__lshift__.Tensor(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::__lshift__(const at::Tensor & other) const {
    return at::_ops::__lshift___Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::__ilshift__.Scalar(Tensor(a!) self, Scalar other) -> Tensor(a!)
inline at::Tensor & Tensor::__ilshift__(const at::Scalar & other) const {
    return at::_ops::__ilshift___Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::__ilshift__.Tensor(Tensor(a!) self, Tensor other) -> Tensor(a!)
inline at::Tensor & Tensor::__ilshift__(const at::Tensor & other) const {
    return at::_ops::__ilshift___Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::bitwise_left_shift.Tensor(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::bitwise_left_shift(const at::Tensor & other) const {
    return at::_ops::bitwise_left_shift_Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::bitwise_left_shift_.Tensor(Tensor(a!) self, Tensor other) -> Tensor(a!)
inline at::Tensor & Tensor::bitwise_left_shift_(const at::Tensor & other) const {
    return at::_ops::bitwise_left_shift__Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::bitwise_left_shift.Tensor_Scalar(Tensor self, Scalar other) -> Tensor
inline at::Tensor Tensor::bitwise_left_shift(const at::Scalar & other) const {
    return at::_ops::bitwise_left_shift_Tensor_Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::bitwise_left_shift_.Tensor_Scalar(Tensor(a!) self, Scalar other) -> Tensor(a!)
inline at::Tensor & Tensor::bitwise_left_shift_(const at::Scalar & other) const {
    return at::_ops::bitwise_left_shift__Tensor_Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::__rshift__.Scalar(Tensor self, Scalar other) -> Tensor
inline at::Tensor Tensor::__rshift__(const at::Scalar & other) const {
    return at::_ops::__rshift___Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::__rshift__.Tensor(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::__rshift__(const at::Tensor & other) const {
    return at::_ops::__rshift___Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::__irshift__.Scalar(Tensor(a!) self, Scalar other) -> Tensor(a!)
inline at::Tensor & Tensor::__irshift__(const at::Scalar & other) const {
    return at::_ops::__irshift___Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::__irshift__.Tensor(Tensor(a!) self, Tensor other) -> Tensor(a!)
inline at::Tensor & Tensor::__irshift__(const at::Tensor & other) const {
    return at::_ops::__irshift___Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::bitwise_right_shift.Tensor(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::bitwise_right_shift(const at::Tensor & other) const {
    return at::_ops::bitwise_right_shift_Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::bitwise_right_shift_.Tensor(Tensor(a!) self, Tensor other) -> Tensor(a!)
inline at::Tensor & Tensor::bitwise_right_shift_(const at::Tensor & other) const {
    return at::_ops::bitwise_right_shift__Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::bitwise_right_shift.Tensor_Scalar(Tensor self, Scalar other) -> Tensor
inline at::Tensor Tensor::bitwise_right_shift(const at::Scalar & other) const {
    return at::_ops::bitwise_right_shift_Tensor_Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::bitwise_right_shift_.Tensor_Scalar(Tensor(a!) self, Scalar other) -> Tensor(a!)
inline at::Tensor & Tensor::bitwise_right_shift_(const at::Scalar & other) const {
    return at::_ops::bitwise_right_shift__Tensor_Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::tril_(Tensor(a!) self, int diagonal=0) -> Tensor(a!)
inline at::Tensor & Tensor::tril_(int64_t diagonal) const {
    return at::_ops::tril_::call(const_cast<Tensor&>(*this), diagonal);
}

// aten::triu_(Tensor(a!) self, int diagonal=0) -> Tensor(a!)
inline at::Tensor & Tensor::triu_(int64_t diagonal) const {
    return at::_ops::triu_::call(const_cast<Tensor&>(*this), diagonal);
}

// aten::digamma_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::digamma_() const {
    return at::_ops::digamma_::call(const_cast<Tensor&>(*this));
}

// aten::lerp_.Scalar(Tensor(a!) self, Tensor end, Scalar weight) -> Tensor(a!)
inline at::Tensor & Tensor::lerp_(const at::Tensor & end, const at::Scalar & weight) const {
    return at::_ops::lerp__Scalar::call(const_cast<Tensor&>(*this), end, weight);
}

// aten::lerp_.Tensor(Tensor(a!) self, Tensor end, Tensor weight) -> Tensor(a!)
inline at::Tensor & Tensor::lerp_(const at::Tensor & end, const at::Tensor & weight) const {
    return at::_ops::lerp__Tensor::call(const_cast<Tensor&>(*this), end, weight);
}

// aten::addbmm_(Tensor(a!) self, Tensor batch1, Tensor batch2, *, Scalar beta=1, Scalar alpha=1) -> Tensor(a!)
inline at::Tensor & Tensor::addbmm_(const at::Tensor & batch1, const at::Tensor & batch2, const at::Scalar & beta, const at::Scalar & alpha) const {
    return at::_ops::addbmm_::call(const_cast<Tensor&>(*this), batch1, batch2, beta, alpha);
}

// aten::addbmm(Tensor self, Tensor batch1, Tensor batch2, *, Scalar beta=1, Scalar alpha=1) -> Tensor
inline at::Tensor Tensor::addbmm(const at::Tensor & batch1, const at::Tensor & batch2, const at::Scalar & beta, const at::Scalar & alpha) const {
    return at::_ops::addbmm::call(const_cast<Tensor&>(*this), batch1, batch2, beta, alpha);
}

// aten::random_.from(Tensor(a!) self, int from, int? to, *, Generator? generator=None) -> Tensor(a!)
inline at::Tensor & Tensor::random_(int64_t from, ::std::optional<int64_t> to, ::std::optional<at::Generator> generator) const {
    return at::_ops::random__from::call(const_cast<Tensor&>(*this), from, to, generator);
}

// aten::random_.to(Tensor(a!) self, int to, *, Generator? generator=None) -> Tensor(a!)
inline at::Tensor & Tensor::random_(int64_t to, ::std::optional<at::Generator> generator) const {
    return at::_ops::random__to::call(const_cast<Tensor&>(*this), to, generator);
}

// aten::random_(Tensor(a!) self, *, Generator? generator=None) -> Tensor(a!)
inline at::Tensor & Tensor::random_(::std::optional<at::Generator> generator) const {
    return at::_ops::random_::call(const_cast<Tensor&>(*this), generator);
}

// aten::uniform_(Tensor(a!) self, float from=0, float to=1, *, Generator? generator=None) -> Tensor(a!)
inline at::Tensor & Tensor::uniform_(double from, double to, ::std::optional<at::Generator> generator) const {
    return at::_ops::uniform_::call(const_cast<Tensor&>(*this), from, to, generator);
}

// aten::cauchy_(Tensor(a!) self, float median=0, float sigma=1, *, Generator? generator=None) -> Tensor(a!)
inline at::Tensor & Tensor::cauchy_(double median, double sigma, ::std::optional<at::Generator> generator) const {
    return at::_ops::cauchy_::call(const_cast<Tensor&>(*this), median, sigma, generator);
}

// aten::log_normal_(Tensor(a!) self, float mean=1, float std=2, *, Generator? generator=None) -> Tensor(a!)
inline at::Tensor & Tensor::log_normal_(double mean, double std, ::std::optional<at::Generator> generator) const {
    return at::_ops::log_normal_::call(const_cast<Tensor&>(*this), mean, std, generator);
}

// aten::exponential_(Tensor(a!) self, float lambd=1, *, Generator? generator=None) -> Tensor(a!)
inline at::Tensor & Tensor::exponential_(double lambd, ::std::optional<at::Generator> generator) const {
    return at::_ops::exponential_::call(const_cast<Tensor&>(*this), lambd, generator);
}

// aten::geometric_(Tensor(a!) self, float p, *, Generator? generator=None) -> Tensor(a!)
inline at::Tensor & Tensor::geometric_(double p, ::std::optional<at::Generator> generator) const {
    return at::_ops::geometric_::call(const_cast<Tensor&>(*this), p, generator);
}

// aten::diag(Tensor self, int diagonal=0) -> Tensor
inline at::Tensor Tensor::diag(int64_t diagonal) const {
    return at::_ops::diag::call(const_cast<Tensor&>(*this), diagonal);
}

// aten::cross(Tensor self, Tensor other, int? dim=None) -> Tensor
inline at::Tensor Tensor::cross(const at::Tensor & other, ::std::optional<int64_t> dim) const {
    return at::_ops::cross::call(const_cast<Tensor&>(*this), other, dim);
}

// aten::triu(Tensor self, int diagonal=0) -> Tensor
inline at::Tensor Tensor::triu(int64_t diagonal) const {
    return at::_ops::triu::call(const_cast<Tensor&>(*this), diagonal);
}

// aten::tril(Tensor self, int diagonal=0) -> Tensor
inline at::Tensor Tensor::tril(int64_t diagonal) const {
    return at::_ops::tril::call(const_cast<Tensor&>(*this), diagonal);
}

// aten::trace(Tensor self) -> Tensor
inline at::Tensor Tensor::trace() const {
    return at::_ops::trace::call(const_cast<Tensor&>(*this));
}

// aten::ne.Scalar(Tensor self, Scalar other) -> Tensor
inline at::Tensor Tensor::ne(const at::Scalar & other) const {
    return at::_ops::ne_Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::ne.Tensor(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::ne(const at::Tensor & other) const {
    return at::_ops::ne_Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::ne_.Scalar(Tensor(a!) self, Scalar other) -> Tensor(a!)
inline at::Tensor & Tensor::ne_(const at::Scalar & other) const {
    return at::_ops::ne__Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::ne_.Tensor(Tensor(a!) self, Tensor other) -> Tensor(a!)
inline at::Tensor & Tensor::ne_(const at::Tensor & other) const {
    return at::_ops::ne__Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::not_equal.Scalar(Tensor self, Scalar other) -> Tensor
inline at::Tensor Tensor::not_equal(const at::Scalar & other) const {
    return at::_ops::not_equal_Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::not_equal.Tensor(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::not_equal(const at::Tensor & other) const {
    return at::_ops::not_equal_Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::not_equal_.Scalar(Tensor(a!) self, Scalar other) -> Tensor(a!)
inline at::Tensor & Tensor::not_equal_(const at::Scalar & other) const {
    return at::_ops::not_equal__Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::not_equal_.Tensor(Tensor(a!) self, Tensor other) -> Tensor(a!)
inline at::Tensor & Tensor::not_equal_(const at::Tensor & other) const {
    return at::_ops::not_equal__Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::eq.Scalar(Tensor self, Scalar other) -> Tensor
inline at::Tensor Tensor::eq(const at::Scalar & other) const {
    return at::_ops::eq_Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::eq.Tensor(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::eq(const at::Tensor & other) const {
    return at::_ops::eq_Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::ge.Scalar(Tensor self, Scalar other) -> Tensor
inline at::Tensor Tensor::ge(const at::Scalar & other) const {
    return at::_ops::ge_Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::ge.Tensor(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::ge(const at::Tensor & other) const {
    return at::_ops::ge_Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::ge_.Scalar(Tensor(a!) self, Scalar other) -> Tensor(a!)
inline at::Tensor & Tensor::ge_(const at::Scalar & other) const {
    return at::_ops::ge__Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::ge_.Tensor(Tensor(a!) self, Tensor other) -> Tensor(a!)
inline at::Tensor & Tensor::ge_(const at::Tensor & other) const {
    return at::_ops::ge__Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::greater_equal.Scalar(Tensor self, Scalar other) -> Tensor
inline at::Tensor Tensor::greater_equal(const at::Scalar & other) const {
    return at::_ops::greater_equal_Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::greater_equal.Tensor(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::greater_equal(const at::Tensor & other) const {
    return at::_ops::greater_equal_Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::greater_equal_.Scalar(Tensor(a!) self, Scalar other) -> Tensor(a!)
inline at::Tensor & Tensor::greater_equal_(const at::Scalar & other) const {
    return at::_ops::greater_equal__Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::greater_equal_.Tensor(Tensor(a!) self, Tensor other) -> Tensor(a!)
inline at::Tensor & Tensor::greater_equal_(const at::Tensor & other) const {
    return at::_ops::greater_equal__Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::le.Scalar(Tensor self, Scalar other) -> Tensor
inline at::Tensor Tensor::le(const at::Scalar & other) const {
    return at::_ops::le_Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::le.Tensor(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::le(const at::Tensor & other) const {
    return at::_ops::le_Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::le_.Scalar(Tensor(a!) self, Scalar other) -> Tensor(a!)
inline at::Tensor & Tensor::le_(const at::Scalar & other) const {
    return at::_ops::le__Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::le_.Tensor(Tensor(a!) self, Tensor other) -> Tensor(a!)
inline at::Tensor & Tensor::le_(const at::Tensor & other) const {
    return at::_ops::le__Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::less_equal.Scalar(Tensor self, Scalar other) -> Tensor
inline at::Tensor Tensor::less_equal(const at::Scalar & other) const {
    return at::_ops::less_equal_Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::less_equal.Tensor(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::less_equal(const at::Tensor & other) const {
    return at::_ops::less_equal_Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::less_equal_.Scalar(Tensor(a!) self, Scalar other) -> Tensor(a!)
inline at::Tensor & Tensor::less_equal_(const at::Scalar & other) const {
    return at::_ops::less_equal__Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::less_equal_.Tensor(Tensor(a!) self, Tensor other) -> Tensor(a!)
inline at::Tensor & Tensor::less_equal_(const at::Tensor & other) const {
    return at::_ops::less_equal__Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::gt.Scalar(Tensor self, Scalar other) -> Tensor
inline at::Tensor Tensor::gt(const at::Scalar & other) const {
    return at::_ops::gt_Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::gt.Tensor(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::gt(const at::Tensor & other) const {
    return at::_ops::gt_Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::gt_.Scalar(Tensor(a!) self, Scalar other) -> Tensor(a!)
inline at::Tensor & Tensor::gt_(const at::Scalar & other) const {
    return at::_ops::gt__Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::gt_.Tensor(Tensor(a!) self, Tensor other) -> Tensor(a!)
inline at::Tensor & Tensor::gt_(const at::Tensor & other) const {
    return at::_ops::gt__Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::greater.Scalar(Tensor self, Scalar other) -> Tensor
inline at::Tensor Tensor::greater(const at::Scalar & other) const {
    return at::_ops::greater_Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::greater.Tensor(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::greater(const at::Tensor & other) const {
    return at::_ops::greater_Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::greater_.Scalar(Tensor(a!) self, Scalar other) -> Tensor(a!)
inline at::Tensor & Tensor::greater_(const at::Scalar & other) const {
    return at::_ops::greater__Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::greater_.Tensor(Tensor(a!) self, Tensor other) -> Tensor(a!)
inline at::Tensor & Tensor::greater_(const at::Tensor & other) const {
    return at::_ops::greater__Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::lt.Scalar(Tensor self, Scalar other) -> Tensor
inline at::Tensor Tensor::lt(const at::Scalar & other) const {
    return at::_ops::lt_Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::lt.Tensor(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::lt(const at::Tensor & other) const {
    return at::_ops::lt_Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::lt_.Scalar(Tensor(a!) self, Scalar other) -> Tensor(a!)
inline at::Tensor & Tensor::lt_(const at::Scalar & other) const {
    return at::_ops::lt__Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::lt_.Tensor(Tensor(a!) self, Tensor other) -> Tensor(a!)
inline at::Tensor & Tensor::lt_(const at::Tensor & other) const {
    return at::_ops::lt__Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::less.Scalar(Tensor self, Scalar other) -> Tensor
inline at::Tensor Tensor::less(const at::Scalar & other) const {
    return at::_ops::less_Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::less.Tensor(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::less(const at::Tensor & other) const {
    return at::_ops::less_Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::less_.Scalar(Tensor(a!) self, Scalar other) -> Tensor(a!)
inline at::Tensor & Tensor::less_(const at::Scalar & other) const {
    return at::_ops::less__Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::less_.Tensor(Tensor(a!) self, Tensor other) -> Tensor(a!)
inline at::Tensor & Tensor::less_(const at::Tensor & other) const {
    return at::_ops::less__Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::take(Tensor self, Tensor index) -> Tensor
inline at::Tensor Tensor::take(const at::Tensor & index) const {
    return at::_ops::take::call(const_cast<Tensor&>(*this), index);
}

// aten::take_along_dim(Tensor self, Tensor indices, int? dim=None) -> Tensor
inline at::Tensor Tensor::take_along_dim(const at::Tensor & indices, ::std::optional<int64_t> dim) const {
    return at::_ops::take_along_dim::call(const_cast<Tensor&>(*this), indices, dim);
}

// aten::index_select(Tensor self, int dim, Tensor index) -> Tensor
inline at::Tensor Tensor::index_select(int64_t dim, const at::Tensor & index) const {
    return at::_ops::index_select::call(const_cast<Tensor&>(*this), dim, index);
}

// aten::index_select.dimname(Tensor self, Dimname dim, Tensor index) -> Tensor
inline at::Tensor Tensor::index_select(at::Dimname dim, const at::Tensor & index) const {
    return at::_ops::index_select_dimname::call(const_cast<Tensor&>(*this), dim, index);
}

// aten::masked_select(Tensor self, Tensor mask) -> Tensor
inline at::Tensor Tensor::masked_select(const at::Tensor & mask) const {
    return at::_ops::masked_select::call(const_cast<Tensor&>(*this), mask);
}

// aten::nonzero(Tensor self) -> Tensor
inline at::Tensor Tensor::nonzero() const {
    return at::_ops::nonzero::call(const_cast<Tensor&>(*this));
}

// aten::nonzero_static(Tensor self, *, SymInt size, int fill_value=-1) -> Tensor
inline at::Tensor Tensor::nonzero_static(int64_t size, int64_t fill_value) const {
    return at::_ops::nonzero_static::call(const_cast<Tensor&>(*this), size, fill_value);
}

// aten::nonzero_static(Tensor self, *, SymInt size, int fill_value=-1) -> Tensor
inline at::Tensor Tensor::nonzero_static_symint(c10::SymInt size, int64_t fill_value) const {
    return at::_ops::nonzero_static::call(const_cast<Tensor&>(*this), size, fill_value);
}

// aten::nonzero_numpy(Tensor self) -> Tensor[]
inline ::std::vector<at::Tensor> Tensor::nonzero_numpy() const {
    return at::_ops::nonzero_numpy::call(const_cast<Tensor&>(*this));
}

// aten::argwhere(Tensor self) -> Tensor
inline at::Tensor Tensor::argwhere() const {
    return at::_ops::argwhere::call(const_cast<Tensor&>(*this));
}

// aten::gather(Tensor self, int dim, Tensor index, *, bool sparse_grad=False) -> Tensor
inline at::Tensor Tensor::gather(int64_t dim, const at::Tensor & index, bool sparse_grad) const {
    return at::_ops::gather::call(const_cast<Tensor&>(*this), dim, index, sparse_grad);
}

// aten::gather.dimname(Tensor self, Dimname dim, Tensor index, *, bool sparse_grad=False) -> Tensor
inline at::Tensor Tensor::gather(at::Dimname dim, const at::Tensor & index, bool sparse_grad) const {
    return at::_ops::gather_dimname::call(const_cast<Tensor&>(*this), dim, index, sparse_grad);
}

// aten::addcmul(Tensor self, Tensor tensor1, Tensor tensor2, *, Scalar value=1) -> Tensor
inline at::Tensor Tensor::addcmul(const at::Tensor & tensor1, const at::Tensor & tensor2, const at::Scalar & value) const {
    return at::_ops::addcmul::call(const_cast<Tensor&>(*this), tensor1, tensor2, value);
}

// aten::addcmul_(Tensor(a!) self, Tensor tensor1, Tensor tensor2, *, Scalar value=1) -> Tensor(a!)
inline at::Tensor & Tensor::addcmul_(const at::Tensor & tensor1, const at::Tensor & tensor2, const at::Scalar & value) const {
    return at::_ops::addcmul_::call(const_cast<Tensor&>(*this), tensor1, tensor2, value);
}

// aten::addcdiv(Tensor self, Tensor tensor1, Tensor tensor2, *, Scalar value=1) -> Tensor
inline at::Tensor Tensor::addcdiv(const at::Tensor & tensor1, const at::Tensor & tensor2, const at::Scalar & value) const {
    return at::_ops::addcdiv::call(const_cast<Tensor&>(*this), tensor1, tensor2, value);
}

// aten::addcdiv_(Tensor(a!) self, Tensor tensor1, Tensor tensor2, *, Scalar value=1) -> Tensor(a!)
inline at::Tensor & Tensor::addcdiv_(const at::Tensor & tensor1, const at::Tensor & tensor2, const at::Scalar & value) const {
    return at::_ops::addcdiv_::call(const_cast<Tensor&>(*this), tensor1, tensor2, value);
}

// aten::triangular_solve(Tensor self, Tensor A, bool upper=True, bool transpose=False, bool unitriangular=False) -> (Tensor solution, Tensor cloned_coefficient)
inline ::std::tuple<at::Tensor,at::Tensor> Tensor::triangular_solve(const at::Tensor & A, bool upper, bool transpose, bool unitriangular) const {
    return at::_ops::triangular_solve::call(const_cast<Tensor&>(*this), A, upper, transpose, unitriangular);
}

// aten::svd(Tensor self, bool some=True, bool compute_uv=True) -> (Tensor U, Tensor S, Tensor V)
inline ::std::tuple<at::Tensor,at::Tensor,at::Tensor> Tensor::svd(bool some, bool compute_uv) const {
    return at::_ops::svd::call(const_cast<Tensor&>(*this), some, compute_uv);
}

// aten::swapaxes(Tensor(a) self, int axis0, int axis1) -> Tensor(a)
inline at::Tensor Tensor::swapaxes(int64_t axis0, int64_t axis1) const {
    return at::_ops::swapaxes::call(const_cast<Tensor&>(*this), axis0, axis1);
}

// aten::swapaxes_(Tensor(a!) self, int axis0, int axis1) -> Tensor(a!)
inline at::Tensor & Tensor::swapaxes_(int64_t axis0, int64_t axis1) const {
    return at::_ops::swapaxes_::call(const_cast<Tensor&>(*this), axis0, axis1);
}

// aten::swapdims(Tensor(a) self, int dim0, int dim1) -> Tensor(a)
inline at::Tensor Tensor::swapdims(int64_t dim0, int64_t dim1) const {
    return at::_ops::swapdims::call(const_cast<Tensor&>(*this), dim0, dim1);
}

// aten::swapdims_(Tensor(a!) self, int dim0, int dim1) -> Tensor(a!)
inline at::Tensor & Tensor::swapdims_(int64_t dim0, int64_t dim1) const {
    return at::_ops::swapdims_::call(const_cast<Tensor&>(*this), dim0, dim1);
}

// aten::cholesky(Tensor self, bool upper=False) -> Tensor
inline at::Tensor Tensor::cholesky(bool upper) const {
    return at::_ops::cholesky::call(const_cast<Tensor&>(*this), upper);
}

// aten::cholesky_solve(Tensor self, Tensor input2, bool upper=False) -> Tensor
inline at::Tensor Tensor::cholesky_solve(const at::Tensor & input2, bool upper) const {
    return at::_ops::cholesky_solve::call(const_cast<Tensor&>(*this), input2, upper);
}

// aten::cholesky_inverse(Tensor self, bool upper=False) -> Tensor
inline at::Tensor Tensor::cholesky_inverse(bool upper) const {
    return at::_ops::cholesky_inverse::call(const_cast<Tensor&>(*this), upper);
}

// aten::qr(Tensor self, bool some=True) -> (Tensor Q, Tensor R)
inline ::std::tuple<at::Tensor,at::Tensor> Tensor::qr(bool some) const {
    return at::_ops::qr::call(const_cast<Tensor&>(*this), some);
}

// aten::geqrf(Tensor self) -> (Tensor a, Tensor tau)
inline ::std::tuple<at::Tensor,at::Tensor> Tensor::geqrf() const {
    return at::_ops::geqrf::call(const_cast<Tensor&>(*this));
}

// aten::orgqr(Tensor self, Tensor input2) -> Tensor
inline at::Tensor Tensor::orgqr(const at::Tensor & input2) const {
    return at::_ops::orgqr::call(const_cast<Tensor&>(*this), input2);
}

// aten::ormqr(Tensor self, Tensor input2, Tensor input3, bool left=True, bool transpose=False) -> Tensor
inline at::Tensor Tensor::ormqr(const at::Tensor & input2, const at::Tensor & input3, bool left, bool transpose) const {
    return at::_ops::ormqr::call(const_cast<Tensor&>(*this), input2, input3, left, transpose);
}

// aten::lu_solve(Tensor self, Tensor LU_data, Tensor LU_pivots) -> Tensor
inline at::Tensor Tensor::lu_solve(const at::Tensor & LU_data, const at::Tensor & LU_pivots) const {
    return at::_ops::lu_solve::call(const_cast<Tensor&>(*this), LU_data, LU_pivots);
}

// aten::multinomial(Tensor self, int num_samples, bool replacement=False, *, Generator? generator=None) -> Tensor
inline at::Tensor Tensor::multinomial(int64_t num_samples, bool replacement, ::std::optional<at::Generator> generator) const {
    return at::_ops::multinomial::call(const_cast<Tensor&>(*this), num_samples, replacement, generator);
}

// aten::lgamma_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::lgamma_() const {
    return at::_ops::lgamma_::call(const_cast<Tensor&>(*this));
}

// aten::lgamma(Tensor self) -> Tensor
inline at::Tensor Tensor::lgamma() const {
    return at::_ops::lgamma::call(const_cast<Tensor&>(*this));
}

// aten::digamma(Tensor self) -> Tensor
inline at::Tensor Tensor::digamma() const {
    return at::_ops::digamma::call(const_cast<Tensor&>(*this));
}

// aten::polygamma(int n, Tensor self) -> Tensor
inline at::Tensor Tensor::polygamma(int64_t n) const {
    return at::_ops::polygamma::call(n, const_cast<Tensor&>(*this));
}

// aten::polygamma_(Tensor(a!) self, int n) -> Tensor(a!)
inline at::Tensor & Tensor::polygamma_(int64_t n) const {
    return at::_ops::polygamma_::call(const_cast<Tensor&>(*this), n);
}

// aten::erfinv(Tensor self) -> Tensor
inline at::Tensor Tensor::erfinv() const {
    return at::_ops::erfinv::call(const_cast<Tensor&>(*this));
}

// aten::erfinv_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::erfinv_() const {
    return at::_ops::erfinv_::call(const_cast<Tensor&>(*this));
}

// aten::i0(Tensor self) -> Tensor
inline at::Tensor Tensor::i0() const {
    return at::_ops::i0::call(const_cast<Tensor&>(*this));
}

// aten::i0_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::i0_() const {
    return at::_ops::i0_::call(const_cast<Tensor&>(*this));
}

// aten::sign(Tensor self) -> Tensor
inline at::Tensor Tensor::sign() const {
    return at::_ops::sign::call(const_cast<Tensor&>(*this));
}

// aten::sign_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & Tensor::sign_() const {
    return at::_ops::sign_::call(const_cast<Tensor&>(*this));
}

// aten::signbit(Tensor self) -> Tensor
inline at::Tensor Tensor::signbit() const {
    return at::_ops::signbit::call(const_cast<Tensor&>(*this));
}

// aten::dist(Tensor self, Tensor other, Scalar p=2) -> Tensor
inline at::Tensor Tensor::dist(const at::Tensor & other, const at::Scalar & p) const {
    return at::_ops::dist::call(const_cast<Tensor&>(*this), other, p);
}

// aten::atan2_(Tensor(a!) self, Tensor other) -> Tensor(a!)
inline at::Tensor & Tensor::atan2_(const at::Tensor & other) const {
    return at::_ops::atan2_::call(const_cast<Tensor&>(*this), other);
}

// aten::atan2(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::atan2(const at::Tensor & other) const {
    return at::_ops::atan2::call(const_cast<Tensor&>(*this), other);
}

// aten::arctan2(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::arctan2(const at::Tensor & other) const {
    return at::_ops::arctan2::call(const_cast<Tensor&>(*this), other);
}

// aten::arctan2_(Tensor(a!) self, Tensor other) -> Tensor(a!)
inline at::Tensor & Tensor::arctan2_(const at::Tensor & other) const {
    return at::_ops::arctan2_::call(const_cast<Tensor&>(*this), other);
}

// aten::lerp.Scalar(Tensor self, Tensor end, Scalar weight) -> Tensor
inline at::Tensor Tensor::lerp(const at::Tensor & end, const at::Scalar & weight) const {
    return at::_ops::lerp_Scalar::call(const_cast<Tensor&>(*this), end, weight);
}

// aten::lerp.Tensor(Tensor self, Tensor end, Tensor weight) -> Tensor
inline at::Tensor Tensor::lerp(const at::Tensor & end, const at::Tensor & weight) const {
    return at::_ops::lerp_Tensor::call(const_cast<Tensor&>(*this), end, weight);
}

// aten::histc(Tensor self, int bins=100, Scalar min=0, Scalar max=0) -> Tensor
inline at::Tensor Tensor::histc(int64_t bins, const at::Scalar & min, const at::Scalar & max) const {
    return at::_ops::histc::call(const_cast<Tensor&>(*this), bins, min, max);
}

// aten::histogram.bins_tensor(Tensor self, Tensor bins, *, Tensor? weight=None, bool density=False) -> (Tensor hist, Tensor bin_edges)
inline ::std::tuple<at::Tensor,at::Tensor> Tensor::histogram(const at::Tensor & bins, const ::std::optional<at::Tensor> & weight, bool density) const {
    return at::_ops::histogram_bins_tensor::call(const_cast<Tensor&>(*this), bins, weight, density);
}

// aten::histogram.bin_ct(Tensor self, int bins=100, *, float[]? range=None, Tensor? weight=None, bool density=False) -> (Tensor hist, Tensor bin_edges)
inline ::std::tuple<at::Tensor,at::Tensor> Tensor::histogram(int64_t bins, ::std::optional<at::ArrayRef<double>> range, const ::std::optional<at::Tensor> & weight, bool density) const {
    return at::_ops::histogram_bin_ct::call(const_cast<Tensor&>(*this), bins, range, weight, density);
}

// aten::fmod.Scalar(Tensor self, Scalar other) -> Tensor
inline at::Tensor Tensor::fmod(const at::Scalar & other) const {
    return at::_ops::fmod_Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::fmod_.Scalar(Tensor(a!) self, Scalar other) -> Tensor(a!)
inline at::Tensor & Tensor::fmod_(const at::Scalar & other) const {
    return at::_ops::fmod__Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::fmod.Tensor(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::fmod(const at::Tensor & other) const {
    return at::_ops::fmod_Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::fmod_.Tensor(Tensor(a!) self, Tensor other) -> Tensor(a!)
inline at::Tensor & Tensor::fmod_(const at::Tensor & other) const {
    return at::_ops::fmod__Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::hypot(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::hypot(const at::Tensor & other) const {
    return at::_ops::hypot::call(const_cast<Tensor&>(*this), other);
}

// aten::hypot_(Tensor(a!) self, Tensor other) -> Tensor(a!)
inline at::Tensor & Tensor::hypot_(const at::Tensor & other) const {
    return at::_ops::hypot_::call(const_cast<Tensor&>(*this), other);
}

// aten::igamma(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::igamma(const at::Tensor & other) const {
    return at::_ops::igamma::call(const_cast<Tensor&>(*this), other);
}

// aten::igamma_(Tensor(a!) self, Tensor other) -> Tensor(a!)
inline at::Tensor & Tensor::igamma_(const at::Tensor & other) const {
    return at::_ops::igamma_::call(const_cast<Tensor&>(*this), other);
}

// aten::igammac(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::igammac(const at::Tensor & other) const {
    return at::_ops::igammac::call(const_cast<Tensor&>(*this), other);
}

// aten::igammac_(Tensor(a!) self, Tensor other) -> Tensor(a!)
inline at::Tensor & Tensor::igammac_(const at::Tensor & other) const {
    return at::_ops::igammac_::call(const_cast<Tensor&>(*this), other);
}

// aten::nextafter(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::nextafter(const at::Tensor & other) const {
    return at::_ops::nextafter::call(const_cast<Tensor&>(*this), other);
}

// aten::nextafter_(Tensor(a!) self, Tensor other) -> Tensor(a!)
inline at::Tensor & Tensor::nextafter_(const at::Tensor & other) const {
    return at::_ops::nextafter_::call(const_cast<Tensor&>(*this), other);
}

// aten::remainder.Scalar(Tensor self, Scalar other) -> Tensor
inline at::Tensor Tensor::remainder(const at::Scalar & other) const {
    return at::_ops::remainder_Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::remainder_.Scalar(Tensor(a!) self, Scalar other) -> Tensor(a!)
inline at::Tensor & Tensor::remainder_(const at::Scalar & other) const {
    return at::_ops::remainder__Scalar::call(const_cast<Tensor&>(*this), other);
}

// aten::remainder.Tensor(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::remainder(const at::Tensor & other) const {
    return at::_ops::remainder_Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::remainder_.Tensor(Tensor(a!) self, Tensor other) -> Tensor(a!)
inline at::Tensor & Tensor::remainder_(const at::Tensor & other) const {
    return at::_ops::remainder__Tensor::call(const_cast<Tensor&>(*this), other);
}

// aten::min(Tensor self) -> Tensor
inline at::Tensor Tensor::min() const {
    return at::_ops::min::call(const_cast<Tensor&>(*this));
}

// aten::fmin(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::fmin(const at::Tensor & other) const {
    return at::_ops::fmin::call(const_cast<Tensor&>(*this), other);
}

// aten::max(Tensor self) -> Tensor
inline at::Tensor Tensor::max() const {
    return at::_ops::max::call(const_cast<Tensor&>(*this));
}

// aten::fmax(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::fmax(const at::Tensor & other) const {
    return at::_ops::fmax::call(const_cast<Tensor&>(*this), other);
}

// aten::maximum(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::maximum(const at::Tensor & other) const {
    return at::_ops::maximum::call(const_cast<Tensor&>(*this), other);
}

// aten::max.other(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::max(const at::Tensor & other) const {
    return at::_ops::max_other::call(const_cast<Tensor&>(*this), other);
}

// aten::minimum(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::minimum(const at::Tensor & other) const {
    return at::_ops::minimum::call(const_cast<Tensor&>(*this), other);
}

// aten::min.other(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::min(const at::Tensor & other) const {
    return at::_ops::min_other::call(const_cast<Tensor&>(*this), other);
}

// aten::quantile(Tensor self, Tensor q, int? dim=None, bool keepdim=False, *, str interpolation='linear') -> Tensor
inline at::Tensor Tensor::quantile(const at::Tensor & q, ::std::optional<int64_t> dim, bool keepdim, c10::string_view interpolation) const {
    return at::_ops::quantile::call(const_cast<Tensor&>(*this), q, dim, keepdim, interpolation);
}

// aten::quantile.scalar(Tensor self, float q, int? dim=None, bool keepdim=False, *, str interpolation='linear') -> Tensor
inline at::Tensor Tensor::quantile(double q, ::std::optional<int64_t> dim, bool keepdim, c10::string_view interpolation) const {
    return at::_ops::quantile_scalar::call(const_cast<Tensor&>(*this), q, dim, keepdim, interpolation);
}

// aten::nanquantile(Tensor self, Tensor q, int? dim=None, bool keepdim=False, *, str interpolation='linear') -> Tensor
inline at::Tensor Tensor::nanquantile(const at::Tensor & q, ::std::optional<int64_t> dim, bool keepdim, c10::string_view interpolation) const {
    return at::_ops::nanquantile::call(const_cast<Tensor&>(*this), q, dim, keepdim, interpolation);
}

// aten::nanquantile.scalar(Tensor self, float q, int? dim=None, bool keepdim=False, *, str interpolation='linear') -> Tensor
inline at::Tensor Tensor::nanquantile(double q, ::std::optional<int64_t> dim, bool keepdim, c10::string_view interpolation) const {
    return at::_ops::nanquantile_scalar::call(const_cast<Tensor&>(*this), q, dim, keepdim, interpolation);
}

// aten::sort(Tensor self, int dim=-1, bool descending=False) -> (Tensor values, Tensor indices)
inline ::std::tuple<at::Tensor,at::Tensor> Tensor::sort(int64_t dim, bool descending) const {
    return at::_ops::sort::call(const_cast<Tensor&>(*this), dim, descending);
}

// aten::sort.stable(Tensor self, *, bool? stable, int dim=-1, bool descending=False) -> (Tensor values, Tensor indices)
inline ::std::tuple<at::Tensor,at::Tensor> Tensor::sort(::std::optional<bool> stable, int64_t dim, bool descending) const {
    return at::_ops::sort_stable::call(const_cast<Tensor&>(*this), stable, dim, descending);
}

// aten::sort.dimname(Tensor self, Dimname dim, bool descending=False) -> (Tensor values, Tensor indices)
inline ::std::tuple<at::Tensor,at::Tensor> Tensor::sort(at::Dimname dim, bool descending) const {
    return at::_ops::sort_dimname::call(const_cast<Tensor&>(*this), dim, descending);
}

// aten::sort.dimname_stable(Tensor self, *, bool? stable, Dimname dim, bool descending=False) -> (Tensor values, Tensor indices)
inline ::std::tuple<at::Tensor,at::Tensor> Tensor::sort(::std::optional<bool> stable, at::Dimname dim, bool descending) const {
    return at::_ops::sort_dimname_stable::call(const_cast<Tensor&>(*this), stable, dim, descending);
}

// aten::msort(Tensor self) -> Tensor
inline at::Tensor Tensor::msort() const {
    return at::_ops::msort::call(const_cast<Tensor&>(*this));
}

// aten::argsort(Tensor self, int dim=-1, bool descending=False) -> Tensor
inline at::Tensor Tensor::argsort(int64_t dim, bool descending) const {
    return at::_ops::argsort::call(const_cast<Tensor&>(*this), dim, descending);
}

// aten::argsort.stable(Tensor self, *, bool stable, int dim=-1, bool descending=False) -> Tensor
inline at::Tensor Tensor::argsort(bool stable, int64_t dim, bool descending) const {
    return at::_ops::argsort_stable::call(const_cast<Tensor&>(*this), stable, dim, descending);
}

// aten::argsort.dimname(Tensor self, Dimname dim, bool descending=False) -> Tensor
inline at::Tensor Tensor::argsort(at::Dimname dim, bool descending) const {
    return at::_ops::argsort_dimname::call(const_cast<Tensor&>(*this), dim, descending);
}

// aten::topk(Tensor self, SymInt k, int dim=-1, bool largest=True, bool sorted=True) -> (Tensor values, Tensor indices)
inline ::std::tuple<at::Tensor,at::Tensor> Tensor::topk(int64_t k, int64_t dim, bool largest, bool sorted) const {
    return at::_ops::topk::call(const_cast<Tensor&>(*this), k, dim, largest, sorted);
}

// aten::topk(Tensor self, SymInt k, int dim=-1, bool largest=True, bool sorted=True) -> (Tensor values, Tensor indices)
inline ::std::tuple<at::Tensor,at::Tensor> Tensor::topk_symint(c10::SymInt k, int64_t dim, bool largest, bool sorted) const {
    return at::_ops::topk::call(const_cast<Tensor&>(*this), k, dim, largest, sorted);
}

// aten::all(Tensor self) -> Tensor
inline at::Tensor Tensor::all() const {
    return at::_ops::all::call(const_cast<Tensor&>(*this));
}

// aten::any(Tensor self) -> Tensor
inline at::Tensor Tensor::any() const {
    return at::_ops::any::call(const_cast<Tensor&>(*this));
}

// aten::renorm(Tensor self, Scalar p, int dim, Scalar maxnorm) -> Tensor
inline at::Tensor Tensor::renorm(const at::Scalar & p, int64_t dim, const at::Scalar & maxnorm) const {
    return at::_ops::renorm::call(const_cast<Tensor&>(*this), p, dim, maxnorm);
}

// aten::renorm_(Tensor(a!) self, Scalar p, int dim, Scalar maxnorm) -> Tensor(a!)
inline at::Tensor & Tensor::renorm_(const at::Scalar & p, int64_t dim, const at::Scalar & maxnorm) const {
    return at::_ops::renorm_::call(const_cast<Tensor&>(*this), p, dim, maxnorm);
}

// aten::unfold(Tensor(a) self, int dimension, int size, int step) -> Tensor(a)
inline at::Tensor Tensor::unfold(int64_t dimension, int64_t size, int64_t step) const {
    return at::_ops::unfold::call(const_cast<Tensor&>(*this), dimension, size, step);
}

// aten::equal(Tensor self, Tensor other) -> bool
inline bool Tensor::equal(const at::Tensor & other) const {
    return at::_ops::equal::call(const_cast<Tensor&>(*this), other);
}

// aten::pow.Tensor_Tensor(Tensor self, Tensor exponent) -> Tensor
inline at::Tensor Tensor::pow(const at::Tensor & exponent) const {
    return at::_ops::pow_Tensor_Tensor::call(const_cast<Tensor&>(*this), exponent);
}

// aten::pow.Tensor_Scalar(Tensor self, Scalar exponent) -> Tensor
inline at::Tensor Tensor::pow(const at::Scalar & exponent) const {
    return at::_ops::pow_Tensor_Scalar::call(const_cast<Tensor&>(*this), exponent);
}

// aten::pow_.Scalar(Tensor(a!) self, Scalar exponent) -> Tensor(a!)
inline at::Tensor & Tensor::pow_(const at::Scalar & exponent) const {
    return at::_ops::pow__Scalar::call(const_cast<Tensor&>(*this), exponent);
}

// aten::pow_.Tensor(Tensor(a!) self, Tensor exponent) -> Tensor(a!)
inline at::Tensor & Tensor::pow_(const at::Tensor & exponent) const {
    return at::_ops::pow__Tensor::call(const_cast<Tensor&>(*this), exponent);
}

// aten::float_power.Tensor_Tensor(Tensor self, Tensor exponent) -> Tensor
inline at::Tensor Tensor::float_power(const at::Tensor & exponent) const {
    return at::_ops::float_power_Tensor_Tensor::call(const_cast<Tensor&>(*this), exponent);
}

// aten::float_power.Tensor_Scalar(Tensor self, Scalar exponent) -> Tensor
inline at::Tensor Tensor::float_power(const at::Scalar & exponent) const {
    return at::_ops::float_power_Tensor_Scalar::call(const_cast<Tensor&>(*this), exponent);
}

// aten::float_power_.Scalar(Tensor(a!) self, Scalar exponent) -> Tensor(a!)
inline at::Tensor & Tensor::float_power_(const at::Scalar & exponent) const {
    return at::_ops::float_power__Scalar::call(const_cast<Tensor&>(*this), exponent);
}

// aten::float_power_.Tensor(Tensor(a!) self, Tensor exponent) -> Tensor(a!)
inline at::Tensor & Tensor::float_power_(const at::Tensor & exponent) const {
    return at::_ops::float_power__Tensor::call(const_cast<Tensor&>(*this), exponent);
}

// aten::normal_(Tensor(a!) self, float mean=0, float std=1, *, Generator? generator=None) -> Tensor(a!)
inline at::Tensor & Tensor::normal_(double mean, double std, ::std::optional<at::Generator> generator) const {
    return at::_ops::normal_::call(const_cast<Tensor&>(*this), mean, std, generator);
}

// aten::alias(Tensor(a) self) -> Tensor(a)
inline at::Tensor Tensor::alias() const {
    return at::_ops::alias::call(const_cast<Tensor&>(*this));
}

// aten::isfinite(Tensor self) -> Tensor
inline at::Tensor Tensor::isfinite() const {
    return at::_ops::isfinite::call(const_cast<Tensor&>(*this));
}

// aten::isinf(Tensor self) -> Tensor
inline at::Tensor Tensor::isinf() const {
    return at::_ops::isinf::call(const_cast<Tensor&>(*this));
}

// aten::record_stream(Tensor(a!) self, Stream s) -> ()
inline void Tensor::record_stream(at::Stream s) const {
    return at::_ops::record_stream::call(const_cast<Tensor&>(*this), s);
}

// aten::isposinf(Tensor self) -> Tensor
inline at::Tensor Tensor::isposinf() const {
    return at::_ops::isposinf::call(const_cast<Tensor&>(*this));
}

// aten::isneginf(Tensor self) -> Tensor
inline at::Tensor Tensor::isneginf() const {
    return at::_ops::isneginf::call(const_cast<Tensor&>(*this));
}

// aten::det(Tensor self) -> Tensor
inline at::Tensor Tensor::det() const {
    return at::_ops::det::call(const_cast<Tensor&>(*this));
}

// aten::slogdet(Tensor self) -> (Tensor sign, Tensor logabsdet)
inline ::std::tuple<at::Tensor,at::Tensor> Tensor::slogdet() const {
    return at::_ops::slogdet::call(const_cast<Tensor&>(*this));
}

// aten::logdet(Tensor self) -> Tensor
inline at::Tensor Tensor::logdet() const {
    return at::_ops::logdet::call(const_cast<Tensor&>(*this));
}

// aten::inverse(Tensor self) -> Tensor
inline at::Tensor Tensor::inverse() const {
    return at::_ops::inverse::call(const_cast<Tensor&>(*this));
}

// aten::inner(Tensor self, Tensor other) -> Tensor
inline at::Tensor Tensor::inner(const at::Tensor & other) const {
    return at::_ops::inner::call(const_cast<Tensor&>(*this), other);
}

// aten::outer(Tensor self, Tensor vec2) -> Tensor
inline at::Tensor Tensor::outer(const at::Tensor & vec2) const {
    return at::_ops::outer::call(const_cast<Tensor&>(*this), vec2);
}

// aten::ger(Tensor self, Tensor vec2) -> Tensor
inline at::Tensor Tensor::ger(const at::Tensor & vec2) const {
    return at::_ops::ger::call(const_cast<Tensor&>(*this), vec2);
}

// aten::to_padded_tensor(Tensor self, float padding, SymInt[]? output_size=None) -> Tensor
inline at::Tensor Tensor::to_padded_tensor(double padding, at::OptionalIntArrayRef output_size) const {
    return at::_ops::to_padded_tensor::call(const_cast<Tensor&>(*this), padding, output_size.has_value() ? ::std::make_optional(c10::fromIntArrayRefSlow(*output_size)) : ::std::nullopt);
}

// aten::to_padded_tensor(Tensor self, float padding, SymInt[]? output_size=None) -> Tensor
inline at::Tensor Tensor::to_padded_tensor_symint(double padding, at::OptionalSymIntArrayRef output_size) const {
    return at::_ops::to_padded_tensor::call(const_cast<Tensor&>(*this), padding, output_size);
}
} // namespace at


namespace c10 {
template <>
struct MaybeOwnedTraits<at::Tensor> {
  using owned_type = at::Tensor;
  using borrow_type = at::Tensor;

  static borrow_type createBorrow(const owned_type& from) {
    // NOTE: this can be implemented without the special
    // unsafe_borrow_t Tensor constructor as
    //
    // return borrow_type(c10::intrusive_ptr<at::TensorImpl, at::UndefinedTensorImpl>::reclaim(from.unsafeGetTensorImpl()));
    //
    // but that hurts inlining due to the nullptr check in the
    // Tensor(c10::intrusive_ptr<...>) constructor. We already know
    // that from.impl_ isn't null because from is a valid Tensor, so
    // we needn't do the check again. (using __builtin_assume can
    // avoid this, but wouldn't be portable to MSVC.)
    return borrow_type(borrow_type::unsafe_borrow_t{}, from);
  }

  static void assignBorrow(borrow_type& lhs, const borrow_type& rhs) {
    lhs.unsafeReleaseTensorImpl();
    // See above note: this can be implemented with public API
    // similarly to createBorrow(), but that would hurt inlining.
    lhs = borrow_type(borrow_type::unsafe_borrow_t{}, rhs);
  }

  static void destroyBorrow(borrow_type& toDestroy) {
    toDestroy.unsafeReleaseTensorImpl(); // "leak" it, but it was already +0.
  }

  static const owned_type& referenceFromBorrow(const borrow_type& borrow) {
    return borrow;
  }

  static const owned_type* pointerFromBorrow(const borrow_type& borrow) {
    return &borrow;
  }

  static bool debugBorrowIsValid(const borrow_type& /*borrow*/) {
    return true;
  }
};

template <>
struct ExclusivelyOwnedTraits<at::Tensor> {
  using repr_type = at::Tensor;
  using pointer_type = at::Tensor*;
  using const_pointer_type = const at::Tensor*;

  static repr_type nullRepr() {
    return at::Tensor();
  }

  template <class... Args>
  static repr_type createInPlace(Args&&... args) {
    return at::Tensor(std::forward<Args>(args)...);
  }

  static repr_type moveToRepr(at::Tensor&& x) {
    return std::move(x);
  }

  static void destroyOwned(at::Tensor& x) {
    return ExclusivelyOwnedTraits<at::TensorBase>::destroyOwned(x);
  }

  static at::Tensor take(at::Tensor& x) {
    return std::move(x);
  }

  static pointer_type getImpl(repr_type& x) {
    return &x;
  }

  static const_pointer_type getImpl(const repr_type& x) {
    return &x;
  }
};
} // namespace c10

namespace at {

inline c10::MaybeOwned<Tensor> borrow_from_optional_tensor(
    const std::optional<Tensor>& opt) {
  return opt.has_value()
    ? c10::MaybeOwned<Tensor>::borrowed(*opt)
    : c10::MaybeOwned<Tensor>::owned(std::in_place);
}

inline c10::MaybeOwned<Tensor> Tensor::expect_contiguous(MemoryFormat memory_format) const & {
  if (is_contiguous(memory_format)) {
    return c10::MaybeOwned<Tensor>::borrowed(*this);
  } else {
    return c10::MaybeOwned<Tensor>::owned(__dispatch_contiguous(memory_format));
  }
}
} // namespace at
