# Copyright 2024-present the HuggingFace Inc. team.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from peft.utils import register_peft_method

from .config import VBLoRAConfig
from .layer import Linear, VBLoRALayer
from .model import VBLoRAModel


__all__ = ["Linear", "VBLoRAConfig", "VBLoRALayer", "VBLoRAModel"]

register_peft_method(name="vblora", config_cls=VBLoRAConfig, model_cls=VBLoRAModel)
