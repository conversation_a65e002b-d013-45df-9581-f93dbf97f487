{% extends "page.html" %}


{% block stylesheet %}
{% endblock %}

{% block site %}

<div id="jupyter-main-app" class="container">
    {% if login_available %}
    {# login_available means password-login is allowed. Show the form. #}
    <div class="row">
        <div class="navbar col-sm-8">
            <div class="navbar-inner">
                <div class="container">
                    <div class="center-nav">
                        <form action="{{base_url}}login?next={{next}}" method="post" class="navbar-form pull-left">
                            {{ xsrf_form_html() | safe }}
                            {% if token_available %}
                            <label for="password_input"><strong>{% trans %}Password or token:{% endtrans
                                    %}</strong></label>
                            {% else %}
                            <label for="password_input"><strong>{% trans %}Password:{% endtrans %}</strong></label>
                            {% endif %}
                            <input type="password" name="password" id="password_input" class="form-control">
                            <button type="submit" class="btn btn-default" id="login_submit">{% trans %}Log in{% endtrans
                                %}</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% else %}
    <p>{% trans %}No login available, you shouldn't be seeing this page.{% endtrans %}</p>
    {% endif %}
    {% if message %}
    <div class="row">
        {% for key in message %}
        <div class="message {{key}}">
            {{message[key]}}
        </div>
        {% endfor %}
    </div>
    {% endif %}
    {% if token_available %}
    {% block token_message %}
    <div class="col-sm-6 col-sm-offset-3 text-left rendered_html">
        <h3>
            Token authentication is enabled
        </h3>
        <p>
            If no password has been configured, you need to open the
            server with its login token in the URL, or paste it above.
            This requirement will be lifted if you
            <b><a href='https://jupyter-server.readthedocs.io/en/latest/operators/public-server.html'>
                    enable a password</a></b>.
        </p>
        <p>
            The command:
        <pre>jupyter server list</pre>
        will show you the URLs of running servers with their tokens,
        which you can copy and paste into your browser. For example:
        </p>
        <pre>Currently running servers:
http://localhost:8888/?token=c8de56fa... :: /Users/<USER>/notebooks
</pre>
        <p>
            or you can paste just the token value into the password field on this
            page.
        </p>
        <p>
            See
            <b><a href='https://jupyter-server.readthedocs.io/en/latest/operators/public-server.html'>
                    the documentation on how to enable a password</a>
            </b>
            in place of token authentication,
            if you would like to avoid dealing with random tokens.
        </p>
        <p>
            Cookies are required for authenticated access to the Jupyter server.
        </p>
        {% if allow_password_change %}
        <h3>{% trans %}Setup a Password{% endtrans %}</h3>
        <p> You can also setup a password by entering your token and a new password
            on the fields below:</p>
        <form action="{{base_url}}login?next={{next}}" method="post" class="">
            {{ xsrf_form_html() | safe }}
            <div class="form-group">
                <label for="token_input">
                    <h4>Token</h4>
                </label>
                <input type="password" name="password" id="token_input" class="form-control">
            </div>
            <div class="form-group">
                <label for="new_password_input">
                    <h4>New Password</h4>
                </label>
                <input type="password" name="new_password" id="new_password_input" class="form-control" required>
            </div>
            <div class="form-group">
                <button type="submit" class="btn btn-default" id="login_new_pass_submit">{% trans %}Log in and set new
                    password{% endtrans %}</button>
            </div>
        </form>
        {% endif %}

    </div>
    {% endblock token_message %}
    {% endif %}
</div>

{% endblock %}


{% block script %}
{% endblock %}
