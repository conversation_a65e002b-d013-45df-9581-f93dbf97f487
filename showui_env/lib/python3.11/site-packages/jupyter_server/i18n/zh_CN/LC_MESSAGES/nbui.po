# Translations template for Ju<PERSON><PERSON>.
# Copyright (C) 2017 ORGANIZATION
# This file is distributed under the same license as the Jupyter project.
# <AUTHOR> <EMAIL>, 2017.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Jupyter VERSION\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2017-08-25 02:53-0400\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.5.0\n"

#: notebook/templates/404.html:3
msgid "You are requesting a page that does not exist!"
msgstr "请求的代码不存在!"

#: notebook/templates/edit.html:37
msgid "current mode"
msgstr "当前模式"

#: notebook/templates/edit.html:48 notebook/templates/notebook.html:78
msgid "File"
msgstr "文件"

#: notebook/templates/edit.html:50 notebook/templates/tree.html:57
msgid "New"
msgstr "新建"

#: notebook/templates/edit.html:51
msgid "Save"
msgstr "保存"

#: notebook/templates/edit.html:52 notebook/templates/tree.html:36
msgid "Rename"
msgstr "重命名"

#: notebook/templates/edit.html:53 notebook/templates/tree.html:38
msgid "Download"
msgstr "下载"

#: notebook/templates/edit.html:56 notebook/templates/notebook.html:131
#: notebook/templates/tree.html:41
msgid "Edit"
msgstr "编辑"

#: notebook/templates/edit.html:58
msgid "Find"
msgstr "查找"

#: notebook/templates/edit.html:59
msgid "Find &amp; Replace"
msgstr "查找 &amp; 替换"

#: notebook/templates/edit.html:61
msgid "Key Map"
msgstr "键值对"

#: notebook/templates/edit.html:62
msgid "Default"
msgstr "默认"

#: notebook/templates/edit.html:63
msgid "Sublime Text"
msgstr "代码编辑器"

#: notebook/templates/edit.html:68 notebook/templates/notebook.html:159
#: notebook/templates/tree.html:40
msgid "View"
msgstr "查看"

#: notebook/templates/edit.html:70 notebook/templates/notebook.html:162
msgid "Show/Hide the logo and notebook title (above menu bar)"
msgstr "显示/隐藏 标题和logo"

#: notebook/templates/edit.html:71 notebook/templates/notebook.html:163
msgid "Toggle Header"
msgstr "切换Header"

#: notebook/templates/edit.html:72 notebook/templates/notebook.html:171
msgid "Toggle Line Numbers"
msgstr "切换行号"

#: notebook/templates/edit.html:75
msgid "Language"
msgstr "语言"

#: notebook/templates/error.html:23
msgid "The error was:"
msgstr "错误:"

#: notebook/templates/login.html:24
msgid "Password or token:"
msgstr "密码或者token:"

#: notebook/templates/login.html:26
msgid "Password:"
msgstr "密码:"

#: notebook/templates/login.html:31
msgid "Log in"
msgstr "登录"

#: notebook/templates/login.html:39
msgid "No login available, you shouldn't be seeing this page."
msgstr "还没有登录, 请先登录."

#: notebook/templates/logout.html:31
#, python-format
msgid "Proceed to the <a href=\"%(base_url)s\">dashboard"
msgstr "进入 <a href=\"%(base_url)s\"> 指示板"

#: notebook/templates/logout.html:33
#, python-format
msgid "Proceed to the <a href=\"%(base_url)slogin\">login page"
msgstr "进入 <a href=\"%(base_url)slogin\"> 登录页面"

#: notebook/templates/notebook.html:62
msgid "Menu"
msgstr "菜单"

#: notebook/templates/notebook.html:65 notebook/templates/notebook.html:254
msgid "Kernel"
msgstr "服务"

#: notebook/templates/notebook.html:68
msgid "This notebook is read-only"
msgstr "这个代码是只读的"

#: notebook/templates/notebook.html:81
msgid "New Notebook"
msgstr "新建代码"

#: notebook/templates/notebook.html:85
msgid "Opens a new window with the Dashboard view"
msgstr "以仪表盘视角打开新的窗口"

#: notebook/templates/notebook.html:86
msgid "Open..."
msgstr "打开..."

#: notebook/templates/notebook.html:90
msgid "Open a copy of this notebook's contents and start a new kernel"
msgstr "打开代码内容的副本并启动一个新的服务"

#: notebook/templates/notebook.html:91
msgid "Make a Copy..."
msgstr "复制..."

#: notebook/templates/notebook.html:92
msgid "Rename..."
msgstr "重命名..."

#: notebook/templates/notebook.html:93
msgid "Save and Checkpoint"
msgstr "保存"

#: notebook/templates/notebook.html:96
msgid "Revert to Checkpoint"
msgstr "恢复"

#: notebook/templates/notebook.html:106
msgid "Print Preview"
msgstr "打印预览"

#: notebook/templates/notebook.html:107
msgid "Download as"
msgstr "下载"

#: notebook/templates/notebook.html:109
msgid "Notebook (.ipynb)"
msgstr "代码(.ipynb)"

#: notebook/templates/notebook.html:110
msgid "Script"
msgstr "脚本"

#: notebook/templates/notebook.html:111
msgid "HTML (.html)"
msgstr ""

#: notebook/templates/notebook.html:112
msgid "Markdown (.md)"
msgstr ""

#: notebook/templates/notebook.html:113
msgid "reST (.rst)"
msgstr ""

#: notebook/templates/notebook.html:114
msgid "LaTeX (.tex)"
msgstr ""

#: notebook/templates/notebook.html:115
msgid "PDF via LaTeX (.pdf)"
msgstr ""

#: notebook/templates/notebook.html:118
msgid "Deploy as"
msgstr "部署在"

#: notebook/templates/notebook.html:123
msgid "Trust the output of this notebook"
msgstr "信任代码的输出"

#: notebook/templates/notebook.html:124
msgid "Trust Notebook"
msgstr "信任代码"

#: notebook/templates/notebook.html:127
msgid "Shutdown this notebook's kernel, and close this window"
msgstr "关闭代码服务并关闭窗口"

#: notebook/templates/notebook.html:128
msgid "Close and Halt"
msgstr "关闭"

#: notebook/templates/notebook.html:133
msgid "Cut Cells"
msgstr "剪切代码块"

#: notebook/templates/notebook.html:134
msgid "Copy Cells"
msgstr "复制代码块"

#: notebook/templates/notebook.html:135
msgid "Paste Cells Above"
msgstr "粘贴到上面"

#: notebook/templates/notebook.html:136
msgid "Paste Cells Below"
msgstr "粘贴到下面"

#: notebook/templates/notebook.html:137
msgid "Paste Cells &amp; Replace"
msgstr "粘贴代码块 &amp; 替换"

#: notebook/templates/notebook.html:138
msgid "Delete Cells"
msgstr "删除代码块"

#: notebook/templates/notebook.html:139
msgid "Undo Delete Cells"
msgstr "撤销删除"

#: notebook/templates/notebook.html:141
msgid "Split Cell"
msgstr "分割代码块"

#: notebook/templates/notebook.html:142
msgid "Merge Cell Above"
msgstr "合并上面的代码块"

#: notebook/templates/notebook.html:143
msgid "Merge Cell Below"
msgstr "合并下面的代码块"

#: notebook/templates/notebook.html:145
msgid "Move Cell Up"
msgstr "上移代码块"

#: notebook/templates/notebook.html:146
msgid "Move Cell Down"
msgstr "下移代码块"

#: notebook/templates/notebook.html:148
msgid "Edit Notebook Metadata"
msgstr "编辑界面元数据"

#: notebook/templates/notebook.html:150
msgid "Find and Replace"
msgstr "查找并替换"

#: notebook/templates/notebook.html:152
msgid "Cut Cell Attachments"
msgstr "剪切附件"

#: notebook/templates/notebook.html:153
msgid "Copy Cell Attachments"
msgstr "复制附件"

#: notebook/templates/notebook.html:154
msgid "Paste Cell Attachments"
msgstr "粘贴附件"

#: notebook/templates/notebook.html:156
msgid "Insert Image"
msgstr "插入图片"

#: notebook/templates/notebook.html:166
msgid "Show/Hide the action icons (below menu bar)"
msgstr "显示/隐藏 操作图标"

#: notebook/templates/notebook.html:167
msgid "Toggle Toolbar"
msgstr ""

#: notebook/templates/notebook.html:170
msgid "Show/Hide line numbers in cells"
msgstr "显示/隐藏行号"

#: notebook/templates/notebook.html:174
msgid "Cell Toolbar"
msgstr "单元格工具栏"

#: notebook/templates/notebook.html:179
msgid "Insert"
msgstr "插入"

#: notebook/templates/notebook.html:182
msgid "Insert an empty Code cell above the currently active cell"
msgstr "在当前活动单元上插入一个空的代码单元格"

#: notebook/templates/notebook.html:183
msgid "Insert Cell Above"
msgstr "插入单元格上面"

#: notebook/templates/notebook.html:185
msgid "Insert an empty Code cell below the currently active cell"
msgstr "在当前活动单元下面插入一个空的代码单元格"

#: notebook/templates/notebook.html:186
msgid "Insert Cell Below"
msgstr "插入单元格下面"

#: notebook/templates/notebook.html:189
msgid "Cell"
msgstr "单元格"

#: notebook/templates/notebook.html:191
msgid "Run this cell, and move cursor to the next one"
msgstr "运行这个单元格，并将光标移到下一个"

#: notebook/templates/notebook.html:192
msgid "Run Cells"
msgstr "运行所有单元格"

#: notebook/templates/notebook.html:193
msgid "Run this cell, select below"
msgstr "运行此单元，选择以下选项"

#: notebook/templates/notebook.html:194
msgid "Run Cells and Select Below"
msgstr "运行单元格并自动选择下一个"

#: notebook/templates/notebook.html:195
msgid "Run this cell, insert below"
msgstr "运行单元格并选择以下"

#: notebook/templates/notebook.html:196
msgid "Run Cells and Insert Below"
msgstr "运行单元格并在下面插入"

#: notebook/templates/notebook.html:197
msgid "Run all cells in the notebook"
msgstr "运行所有的单元格"

#: notebook/templates/notebook.html:198
msgid "Run All"
msgstr "运行所有"

#: notebook/templates/notebook.html:199
msgid "Run all cells above (but not including) this cell"
msgstr "运行上面的所有单元(但不包括)这个单元格"

#: notebook/templates/notebook.html:200
msgid "Run All Above"
msgstr "运行上面的代码块"

#: notebook/templates/notebook.html:201
msgid "Run this cell and all cells below it"
msgstr "运行当前及以下代码块"

#: notebook/templates/notebook.html:202
msgid "Run All Below"
msgstr "运行下面的代码块"

#: notebook/templates/notebook.html:205
msgid "All cells in the notebook have a cell type. By default, new cells are created as 'Code' cells"
msgstr "代码里的所有单元格都有一个类型. 默认情况下, 新单元被创建为'Code'单元格"

#: notebook/templates/notebook.html:206
msgid "Cell Type"
msgstr "单元格类型"

#: notebook/templates/notebook.html:209
msgid "Contents will be sent to the kernel for execution, and output will display in the footer of cell"
msgstr "内容将被发送到内核以执行, 输出将显示在单元格的页脚."

#: notebook/templates/notebook.html:212
msgid "Contents will be rendered as HTML and serve as explanatory text"
msgstr "内容将以HTML形式呈现, 并作为解释性文本"

#: notebook/templates/notebook.html:213 notebook/templates/notebook.html:298
msgid "Markdown"
msgstr "标签"

#: notebook/templates/notebook.html:215
msgid "Contents will pass through nbconvert unmodified"
msgstr "内容将通过未经修改的nbconvert"

#: notebook/templates/notebook.html:216
msgid "Raw NBConvert"
msgstr "原生 NBConvert"

#: notebook/templates/notebook.html:220
msgid "Current Outputs"
msgstr "当前输出"

#: notebook/templates/notebook.html:223
msgid "Hide/Show the output of the current cell"
msgstr "隐藏/显示当前单元格输出"

#: notebook/templates/notebook.html:224 notebook/templates/notebook.html:240
msgid "Toggle"
msgstr "切换"

#: notebook/templates/notebook.html:227
msgid "Scroll the output of the current cell"
msgstr "滚动当前单元格的输出"

#: notebook/templates/notebook.html:228 notebook/templates/notebook.html:244
msgid "Toggle Scrolling"
msgstr "切换滚动"

#: notebook/templates/notebook.html:231
msgid "Clear the output of the current cell"
msgstr "清除当前单元格的输出"

#: notebook/templates/notebook.html:232 notebook/templates/notebook.html:248
msgid "Clear"
msgstr "清空"

#: notebook/templates/notebook.html:236
msgid "All Output"
msgstr "所有输出"

#: notebook/templates/notebook.html:239
msgid "Hide/Show the output of all cells"
msgstr "隐藏/显示 所有代码块的输出"

#: notebook/templates/notebook.html:243
msgid "Scroll the output of all cells"
msgstr "滚动所有单元格的输出"

#: notebook/templates/notebook.html:247
msgid "Clear the output of all cells"
msgstr "清空所有代码块的输出"

#: notebook/templates/notebook.html:257
msgid "Send Keyboard Interrupt (CTRL-C) to the Kernel"
msgstr "按下CTRL-C 中断服务"

#: notebook/templates/notebook.html:258
msgid "Interrupt"
msgstr "中断"

#: notebook/templates/notebook.html:261
msgid "Restart the Kernel"
msgstr "重启服务"

#: notebook/templates/notebook.html:262
msgid "Restart"
msgstr "重启"

#: notebook/templates/notebook.html:265
msgid "Restart the Kernel and clear all output"
msgstr "重启服务并清空所有输出"

#: notebook/templates/notebook.html:266
msgid "Restart &amp; Clear Output"
msgstr "重启 &amp; 清空输出"

#: notebook/templates/notebook.html:269
msgid "Restart the Kernel and re-run the notebook"
msgstr "重启服务并且重新运行代码"

#: notebook/templates/notebook.html:270
msgid "Restart &amp; Run All"
msgstr "重启 &amp; 运行所有"

#: notebook/templates/notebook.html:273
msgid "Reconnect to the Kernel"
msgstr "重新连接服务"

#: notebook/templates/notebook.html:274
msgid "Reconnect"
msgstr "重连"

#: notebook/templates/notebook.html:282
msgid "Change kernel"
msgstr "改变服务"

#: notebook/templates/notebook.html:287
msgid "Help"
msgstr "帮助"

#: notebook/templates/notebook.html:290
msgid "A quick tour of the notebook user interface"
msgstr "快速浏览一下notebook用户界面"

#: notebook/templates/notebook.html:290
msgid "User Interface Tour"
msgstr "用户界面之旅"

#: notebook/templates/notebook.html:291
msgid "Opens a tooltip with all keyboard shortcuts"
msgstr "打开所有快捷键提示信息"

#: notebook/templates/notebook.html:291
msgid "Keyboard Shortcuts"
msgstr "快捷键"

#: notebook/templates/notebook.html:292
msgid "Opens a dialog allowing you to edit Keyboard shortcuts"
msgstr "打开对话框编辑快捷键"

#: notebook/templates/notebook.html:292
msgid "Edit Keyboard Shortcuts"
msgstr "编辑快捷键"

#: notebook/templates/notebook.html:297
msgid "Notebook Help"
msgstr "帮助"

#: notebook/templates/notebook.html:303
msgid "Opens in a new window"
msgstr "在新窗口打开"

#: notebook/templates/notebook.html:319
msgid "About Jupyter Notebook"
msgstr "关于本程序"

#: notebook/templates/notebook.html:319
msgid "About"
msgstr "关于"

#: notebook/templates/page.html:114
msgid "Jupyter Notebook requires JavaScript."
msgstr "Jupyter Notebook需要的JavaScript."

#: notebook/templates/page.html:115
msgid "Please enable it to proceed. "
msgstr "请允许它继续."

#: notebook/templates/page.html:122
msgid "dashboard"
msgstr "指示板"

#: notebook/templates/page.html:135
msgid "Logout"
msgstr "注销"

#: notebook/templates/page.html:137
msgid "Login"
msgstr "登录"

#: notebook/templates/tree.html:23
msgid "Files"
msgstr "文件"

#: notebook/templates/tree.html:24
msgid "Running"
msgstr "运行"

#: notebook/templates/tree.html:25
msgid "Clusters"
msgstr "集群"

#: notebook/templates/tree.html:32
msgid "Select items to perform actions on them."
msgstr "选择操作对象."

#: notebook/templates/tree.html:35
msgid "Duplicate selected"
msgstr "复制选择的对象"

#: notebook/templates/tree.html:35
msgid "Duplicate"
msgstr "复制"

#: notebook/templates/tree.html:36
msgid "Rename selected"
msgstr "重命名"

#: notebook/templates/tree.html:37
msgid "Move selected"
msgstr "移动"

#: notebook/templates/tree.html:37
msgid "Move"
msgstr "移动"

#: notebook/templates/tree.html:38
msgid "Download selected"
msgstr "下载"

#: notebook/templates/tree.html:39
msgid "Shutdown selected notebook(s)"
msgstr "停止运行选择的notebook(s)"

#: notebook/templates/notebook.html:278
#: notebook/templates/tree.html:39
msgid "Shutdown"
msgstr "关闭"

#: notebook/templates/tree.html:40
msgid "View selected"
msgstr "查看"

#: notebook/templates/tree.html:41
msgid "Edit selected"
msgstr "编辑"

#: notebook/templates/tree.html:42
msgid "Delete selected"
msgstr "删除"

#: notebook/templates/tree.html:50
msgid "Click to browse for a file to upload."
msgstr "点击浏览文件上传"

#: notebook/templates/tree.html:51
msgid "Upload"
msgstr "上传"

#: notebook/templates/tree.html:65
msgid "Text File"
msgstr "文本文件"

#: notebook/templates/tree.html:68
msgid "Folder"
msgstr "文件夹"

#: notebook/templates/tree.html:72
msgid "Terminal"
msgstr "终端"

#: notebook/templates/tree.html:76
msgid "Terminals Unavailable"
msgstr "终端不可用"

#: notebook/templates/tree.html:82
msgid "Refresh notebook list"
msgstr "刷新笔记列表"

#: notebook/templates/tree.html:90
msgid "Select All / None"
msgstr "全选 / 全部选"

#: notebook/templates/tree.html:93
msgid "Select..."
msgstr "选择..."

#: notebook/templates/tree.html:98
msgid "Select All Folders"
msgstr "选择所有文件夹"

#: notebook/templates/tree.html:98
msgid " Folders"
msgstr "文件夹"

#: notebook/templates/tree.html:99
msgid "Select All Notebooks"
msgstr "选择所有笔记"

#: notebook/templates/tree.html:99
msgid " All Notebooks"
msgstr "所有笔记"

#: notebook/templates/tree.html:100
msgid "Select Running Notebooks"
msgstr "选择运行中的笔记"

#: notebook/templates/tree.html:100
msgid " Running"
msgstr "运行"

#: notebook/templates/tree.html:101
msgid "Select All Files"
msgstr "选择所有文件"

#: notebook/templates/tree.html:101
msgid " Files"
msgstr "文件"

#: notebook/templates/tree.html:114
msgid "Last Modified"
msgstr "最后修改"

#: notebook/templates/tree.html:120
msgid "Name"
msgstr "名字"

#: notebook/templates/tree.html:130
msgid "Currently running Jupyter processes"
msgstr "当前运行Jupyter"

#: notebook/templates/tree.html:134
msgid "Refresh running list"
msgstr "刷新运行列表"

#: notebook/templates/tree.html:150
msgid "There are no terminals running."
msgstr "没有终端运行"

#: notebook/templates/tree.html:152
msgid "Terminals are unavailable."
msgstr "终端不可用"

#: notebook/templates/tree.html:162
msgid "Notebooks"
msgstr "笔记"

#: notebook/templates/tree.html:169
msgid "There are no notebooks running."
msgstr "没有笔记正在运行"

#: notebook/templates/tree.html:178
msgid "Clusters tab is now provided by IPython parallel."
msgstr "集群标签现在由IPython并行提供."

#: notebook/templates/tree.html:179
msgid "See '<a href=\"https://github.com/ipython/ipyparallel\">IPython parallel</a>' for installation details."
msgstr "安装细节查看 '<a href=\"https://github.com/ipython/ipyparallel\">IPython parallel</a>'."
