# Natural Language Toolkit
#
# Copyright (C) 2001-2024 NLTK Project
# Author: <PERSON><PERSON> <<EMAIL>>
# URL: <https://www.nltk.org/>
# For license information, see LICENSE.TXT
"""Language Model Utilities"""

from math import log

NEG_INF = float("-inf")
POS_INF = float("inf")


def log_base2(score):
    """Convenience function for computing logarithms with base 2."""
    if score == 0.0:
        return NEG_INF
    return log(score, 2)
