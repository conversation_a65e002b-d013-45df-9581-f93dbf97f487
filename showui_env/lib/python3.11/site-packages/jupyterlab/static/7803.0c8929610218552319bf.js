"use strict";(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[7803],{57803:(t,e,a)=>{a.r(e);a.d(e,{Tag:()=>o,classHighlighter:()=>K,getStyleTags:()=>y,highlightCode:()=>k,highlightTree:()=>u,styleTags:()=>f,tagHighlighter:()=>p,tags:()=>I});var i=a(66575);var r=a.n(i);let n=0;class o{constructor(t,e,a,i){this.name=t;this.set=e;this.base=a;this.modified=i;this.id=n++}toString(){let{name:t}=this;for(let e of this.modified)if(e.name)t=`${e.name}(${t})`;return t}static define(t,e){let a=typeof t=="string"?t:"?";if(t instanceof o)e=t;if(e===null||e===void 0?void 0:e.base)throw new Error("Can not derive from a modified tag");let i=new o(a,[],null,[]);i.set.push(i);if(e)for(let r of e.set)i.set.push(r);return i}static defineModifier(t){let e=new l(t);return t=>{if(t.modified.indexOf(e)>-1)return t;return l.get(t.base||t,t.modified.concat(e).sort(((t,e)=>t.id-e.id)))}}}let s=0;class l{constructor(t){this.name=t;this.instances=[];this.id=s++}static get(t,e){if(!e.length)return t;let a=e[0].instances.find((a=>a.base==t&&c(e,a.modified)));if(a)return a;let i=[],r=new o(t.name,i,t,e);for(let o of e)o.instances.push(r);let n=h(e);for(let o of t.set)if(!o.modified.length)for(let t of n)i.push(l.get(o,t));return r}}function c(t,e){return t.length==e.length&&t.every(((t,a)=>t==e[a]))}function h(t){let e=[[]];for(let a=0;a<t.length;a++){for(let i=0,r=e.length;i<r;i++){e.push(e[i].concat(t[a]))}}return e.sort(((t,e)=>e.length-t.length))}function f(t){let e=Object.create(null);for(let a in t){let i=t[a];if(!Array.isArray(i))i=[i];for(let t of a.split(" "))if(t){let a=[],r=2,n=t;for(let e=0;;){if(n=="..."&&e>0&&e+3==t.length){r=1;break}let i=/^"(?:[^"\\]|\\.)*?"|[^\/!]+/.exec(n);if(!i)throw new RangeError("Invalid path: "+t);a.push(i[0]=="*"?"":i[0][0]=='"'?JSON.parse(i[0]):i[0]);e+=i[0].length;if(e==t.length)break;let o=t[e++];if(e==t.length&&o=="!"){r=0;break}if(o!="/")throw new RangeError("Invalid path: "+t);n=t.slice(e)}let o=a.length-1,s=a[o];if(!s)throw new RangeError("Invalid path: "+t);let l=new d(i,r,o>0?a.slice(0,o):null);e[s]=l.sort(e[s])}}return g.add(e)}const g=new i.NodeProp;class d{constructor(t,e,a,i){this.tags=t;this.mode=e;this.context=a;this.next=i}get opaque(){return this.mode==0}get inherit(){return this.mode==1}sort(t){if(!t||t.depth<this.depth){this.next=t;return this}t.next=this.sort(t.next);return t}get depth(){return this.context?this.context.length:0}}d.empty=new d([],2,null);function p(t,e){let a=Object.create(null);for(let n of t){if(!Array.isArray(n.tag))a[n.tag.id]=n.class;else for(let t of n.tag)a[t.id]=n.class}let{scope:i,all:r=null}=e||{};return{style:t=>{let e=r;for(let i of t){for(let t of i.set){let i=a[t.id];if(i){e=e?e+" "+i:i;break}}}return e},scope:i}}function m(t,e){let a=null;for(let i of t){let t=i.style(e);if(t)a=a?a+" "+t:t}return a}function u(t,e,a,i=0,r=t.length){let n=new b(i,Array.isArray(e)?e:[e],a);n.highlightRange(t.cursor(),i,r,"",n.highlighters);n.flush(r)}function k(t,e,a,i,r,n=0,o=t.length){let s=n;function l(e,a){if(e<=s)return;for(let n=t.slice(s,e),o=0;;){let t=n.indexOf("\n",o);let e=t<0?n.length:t;if(e>o)i(n.slice(o,e),a);if(t<0)break;r();o=t+1}s=e}u(e,a,((t,e,a)=>{l(t,"");l(e,a)}),n,o);l(o,"")}class b{constructor(t,e,a){this.at=t;this.highlighters=e;this.span=a;this.class=""}startSpan(t,e){if(e!=this.class){this.flush(t);if(t>this.at)this.at=t;this.class=e}}flush(t){if(t>this.at&&this.class)this.span(this.at,t,this.class)}highlightRange(t,e,a,r,n){let{type:o,from:s,to:l}=t;if(s>=a||l<=e)return;if(o.isTop)n=this.highlighters.filter((t=>!t.scope||t.scope(o)));let c=r;let h=y(t)||d.empty;let f=m(n,h.tags);if(f){if(c)c+=" ";c+=f;if(h.mode==1)r+=(r?" ":"")+f}this.startSpan(Math.max(e,s),c);if(h.opaque)return;let g=t.tree&&t.tree.prop(i.NodeProp.mounted);if(g&&g.overlay){let i=t.node.enter(g.overlay[0].from+s,1);let o=this.highlighters.filter((t=>!t.scope||t.scope(g.tree.type)));let h=t.firstChild();for(let f=0,d=s;;f++){let p=f<g.overlay.length?g.overlay[f]:null;let m=p?p.from+s:l;let u=Math.max(e,d),k=Math.min(a,m);if(u<k&&h){while(t.from<k){this.highlightRange(t,u,k,r,n);this.startSpan(Math.min(k,t.to),c);if(t.to>=m||!t.nextSibling())break}}if(!p||m>a)break;d=p.to+s;if(d>e){this.highlightRange(i.cursor(),Math.max(e,p.from+s),Math.min(a,d),"",o);this.startSpan(Math.min(a,d),c)}}if(h)t.parent()}else if(t.firstChild()){if(g)r="";do{if(t.to<=e)continue;if(t.from>=a)break;this.highlightRange(t,e,a,r,n);this.startSpan(Math.min(a,t.to),c)}while(t.nextSibling());t.parent()}}}function y(t){let e=t.type.prop(g);while(e&&e.context&&!t.matchContext(e.context))e=e.next;return e||null}const N=o.define;const w=N(),v=N(),x=N(v),M=N(v),O=N(),S=N(O),C=N(O),R=N(),A=N(R),_=N(),T=N(),j=N(),q=N(j),E=N();const I={comment:w,lineComment:N(w),blockComment:N(w),docComment:N(w),name:v,variableName:N(v),typeName:x,tagName:N(x),propertyName:M,attributeName:N(M),className:N(v),labelName:N(v),namespace:N(v),macroName:N(v),literal:O,string:S,docString:N(S),character:N(S),attributeValue:N(S),number:C,integer:N(C),float:N(C),bool:N(O),regexp:N(O),escape:N(O),color:N(O),url:N(O),keyword:_,self:N(_),null:N(_),atom:N(_),unit:N(_),modifier:N(_),operatorKeyword:N(_),controlKeyword:N(_),definitionKeyword:N(_),moduleKeyword:N(_),operator:T,derefOperator:N(T),arithmeticOperator:N(T),logicOperator:N(T),bitwiseOperator:N(T),compareOperator:N(T),updateOperator:N(T),definitionOperator:N(T),typeOperator:N(T),controlOperator:N(T),punctuation:j,separator:N(j),bracket:q,angleBracket:N(q),squareBracket:N(q),paren:N(q),brace:N(q),content:R,heading:A,heading1:N(A),heading2:N(A),heading3:N(A),heading4:N(A),heading5:N(A),heading6:N(A),contentSeparator:N(R),list:N(R),quote:N(R),emphasis:N(R),strong:N(R),link:N(R),monospace:N(R),strikethrough:N(R),inserted:N(),deleted:N(),changed:N(),invalid:N(),meta:E,documentMeta:N(E),annotation:N(E),processingInstruction:N(E),definition:o.defineModifier("definition"),constant:o.defineModifier("constant"),function:o.defineModifier("function"),standard:o.defineModifier("standard"),local:o.defineModifier("local"),special:o.defineModifier("special")};for(let B in I){let t=I[B];if(t instanceof o)t.name=B}const K=p([{tag:I.link,class:"tok-link"},{tag:I.heading,class:"tok-heading"},{tag:I.emphasis,class:"tok-emphasis"},{tag:I.strong,class:"tok-strong"},{tag:I.keyword,class:"tok-keyword"},{tag:I.atom,class:"tok-atom"},{tag:I.bool,class:"tok-bool"},{tag:I.url,class:"tok-url"},{tag:I.labelName,class:"tok-labelName"},{tag:I.inserted,class:"tok-inserted"},{tag:I.deleted,class:"tok-deleted"},{tag:I.literal,class:"tok-literal"},{tag:I.string,class:"tok-string"},{tag:I.number,class:"tok-number"},{tag:[I.regexp,I.escape,I.special(I.string)],class:"tok-string2"},{tag:I.variableName,class:"tok-variableName"},{tag:I.local(I.variableName),class:"tok-variableName tok-local"},{tag:I.definition(I.variableName),class:"tok-variableName tok-definition"},{tag:I.special(I.variableName),class:"tok-variableName2"},{tag:I.definition(I.propertyName),class:"tok-propertyName tok-definition"},{tag:I.typeName,class:"tok-typeName"},{tag:I.namespace,class:"tok-namespace"},{tag:I.className,class:"tok-className"},{tag:I.macroName,class:"tok-macroName"},{tag:I.propertyName,class:"tok-propertyName"},{tag:I.operator,class:"tok-operator"},{tag:I.comment,class:"tok-comment"},{tag:I.meta,class:"tok-meta"},{tag:I.invalid,class:"tok-invalid"},{tag:I.punctuation,class:"tok-punctuation"}])}}]);