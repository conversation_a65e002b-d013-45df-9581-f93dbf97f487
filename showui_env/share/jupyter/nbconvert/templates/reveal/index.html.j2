{%- extends 'base.html.j2' -%}
{% from 'mathjax.html.j2' import mathjax %}
{% from 'jupyter_widgets.html.j2' import jupyter_widgets %}

{% set reveal_url_prefix = resources.reveal.url_prefix | default('https://unpkg.com/reveal.js@4.0.2', true) %}
{% set reveal_theme = resources.reveal.theme | default('white', true) %}
{% set reveal_transition = resources.reveal.transition | default('slide', true) %}
{% set reveal_number = resources.reveal.number | default('', true) %}
{% set reveal_width = resources.reveal.width | default('960', true) %}
{% set reveal_height = resources.reveal.height | default('700', true) %}
{% set reveal_scroll = resources.reveal.scroll | default(false, true) | json_dumps %}

{%- block header -%}
<!DOCTYPE html>
<html lang="{{ resources.language_code }}">
<head>

{%- block html_head -%}
<meta charset="utf-8" />
<meta http-equiv="X-UA-Compatible" content="chrome=1" />

<meta name="apple-mobile-web-app-capable" content="yes" />
<meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />

{% set nb_title = nb.metadata.get('title', resources['metadata']['name']) | escape_html_keep_quotes %}
<title>{{nb_title}} slides</title>

{%- block html_head_js -%}
{%- block html_head_js_jquery -%}
<script src="{{ resources.jquery_url }}"></script>
{%- endblock html_head_js_jquery -%}
{%- block html_head_js_requirejs -%}
<script src="{{ resources.require_js_url }}"></script>
{%- endblock html_head_js_requirejs -%}
{%- block html_head_js_mermaidjs -%}
<script type="module">
  import mermaid from '{{ resources.mermaid_js_url }}';
  mermaid.initialize({ startOnLoad: true });
</script>
{%- endblock html_head_js_mermaidjs -%}
{%- endblock html_head_js -%}

{% block jupyter_widgets %}
  {%- if "widgets" in nb.metadata -%}
    {{ jupyter_widgets(resources.jupyter_widgets_base_url, resources.html_manager_semver_range, resources.widget_renderer_url) }}
  {%- endif -%}
{% endblock jupyter_widgets %}

<!-- General and theme style sheets -->
<link rel="stylesheet" href="{{ reveal_url_prefix }}/dist/reveal.css">

<!-- If the query includes 'print-pdf', include the PDF print sheet -->
<script>
if( window.location.search.match( /print-pdf/gi ) ) {
        var link = document.createElement( 'link' );
        link.rel = 'stylesheet';
        link.type = 'text/css';
        document.getElementsByTagName( 'head' )[0].appendChild( link );
}
</script>

{% for css in resources.inlining.css -%}
  <style type="text/css">
    {{ css }}
  </style>
{% endfor %}

{% block notebook_css %}
{{ resources.include_css("static/index.css") }}
{% if resources.theme == 'dark' %}
    {{ resources.include_css("static/theme-dark.css") }}
{% else %}
    {{ resources.include_css("static/theme-light.css") }}
{% endif %}
<style type="text/css">
a.anchor-link {
   display: none;
}
.highlight  {
    margin: 0.4em;
}
.jp-Notebook {
    padding: 0;
}
:root {
    --jp-ui-font-size1: 20px;       /* instead of 14px */
    --jp-content-font-size1: 20px;  /* instead of 14px */
    --jp-code-font-size: 19px;      /* instead of 13px */
    --jp-cell-prompt-width: 110px;  /* instead of 64px */
}
@media print {
  body {
    margin: 0;
  }
}
</style>

{{ resources.include_css("static/custom_reveal.css") }}

{% endblock notebook_css %}

{%- block html_head_js_mathjax -%}
{{ mathjax(resources.mathjax_url) }}
{%- endblock html_head_js_mathjax -%}

{%- block html_head_css -%}
{%- endblock html_head_css -%}

{%- endblock html_head -%}

<!-- Reveal Theme -->
<link rel="stylesheet" href="{{ reveal_url_prefix }}/dist/theme/{{reveal_theme}}.css" id="theme">

</head>
{% endblock header%}

{%- block body_header -%}
{% if resources.theme == 'dark' %}
<body class="jp-Notebook" data-jp-theme-light="false" data-jp-theme-name="JupyterLab Dark">
{% else %}
<body class="jp-Notebook" data-jp-theme-light="true" data-jp-theme-name="JupyterLab Light">
{% endif %}
<main>
<div class="reveal">
<div class="slides">
{%- endblock body_header -%}

{% block body_footer %}
</div>
</div>
</main>
</body>
{% endblock body_footer %}

{% block footer %}
{{ super() }}

{% block footer_js %}
<script>
require(
    {
      // it makes sense to wait a little bit when you are loading
      // reveal from a cdn in a slow connection environment
      waitSeconds: 15
    },
    [
      "{{ reveal_url_prefix }}/dist/reveal.js",
      "{{ reveal_url_prefix }}/plugin/notes/notes.js"
    ],

    function(Reveal, RevealNotes){
        // Full list of configuration options available here: https://github.com/hakimel/reveal.js#configuration
        Reveal.initialize({
            controls: true,
            progress: true,
            history: true,
            transition: "{{reveal_transition}}",
            slideNumber: "{{reveal_number}}",
            plugins: [RevealNotes],
            width: {{reveal_width}},
			      height: {{reveal_height}},

        });

        var update = function(event){
          if(MathJax.Hub.getAllJax(Reveal.getCurrentSlide())){
            MathJax.Hub.Rerender(Reveal.getCurrentSlide());
          }
        };

        Reveal.addEventListener('slidechanged', update);

        function setScrollingSlide() {
            var scroll = {{ reveal_scroll }}
            if (scroll === true) {
              var h = $('.reveal').height() * 0.95;
              $('section.present').find('section')
                .filter(function() {
                  return $(this).height() > h;
                })
                .css('height', 'calc(95vh)')
                .css('overflow-y', 'scroll')
                .css('margin-top', '20px');
            }
        }

        // check and set the scrolling slide every time the slide change
        Reveal.addEventListener('slidechanged', setScrollingSlide);
    }
);
</script>
{% endblock footer_js %}
</html>
{% endblock footer %}
