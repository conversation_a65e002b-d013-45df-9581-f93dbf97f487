/*! For license information please see 227.6bd3154334bb91c5ca1c.js.LICENSE.txt */
(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[227],{69119:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.BLANK_URL=e.relativeFirstCharacters=e.whitespaceEscapeCharsRegex=e.urlSchemeRegex=e.ctrlCharactersRegex=e.htmlCtrlEntityRegex=e.htmlEntitiesRegex=e.invalidProtocolRegex=void 0;e.invalidProtocolRegex=/^([^\w]*)(javascript|data|vbscript)/im;e.htmlEntitiesRegex=/&#(\w+)(^\w|;)?/g;e.htmlCtrlEntityRegex=/&(newline|tab);/gi;e.ctrlCharactersRegex=/[\u0000-\u001F\u007F-\u009F\u2000-\u200D\uFEFF]/gim;e.urlSchemeRegex=/^.+(:|&colon;)/gim;e.whitespaceEscapeCharsRegex=/(\\|%5[cC])((%(6[eE]|72|74))|[nrt])/g;e.relativeFirstCharacters=[".","/"];e.BLANK_URL="about:blank"},16750:(t,e,r)=>{"use strict";var i;i={value:true};e.J=void 0;var a=r(69119);function n(t){return a.relativeFirstCharacters.indexOf(t[0])>-1}function o(t){var e=t.replace(a.ctrlCharactersRegex,"");return e.replace(a.htmlEntitiesRegex,(function(t,e){return String.fromCharCode(e)}))}function s(t){return URL.canParse(t)}function l(t){try{return decodeURIComponent(t)}catch(e){return t}}function c(t){if(!t){return a.BLANK_URL}var e;var r=l(t.trim());do{r=o(r).replace(a.htmlCtrlEntityRegex,"").replace(a.ctrlCharactersRegex,"").replace(a.whitespaceEscapeCharsRegex,"").trim();r=l(r);e=r.match(a.ctrlCharactersRegex)||r.match(a.htmlEntitiesRegex)||r.match(a.htmlCtrlEntityRegex)||r.match(a.whitespaceEscapeCharsRegex)}while(e&&e.length>0);var i=r;if(!i){return a.BLANK_URL}if(n(i)){return i}var c=i.trimStart();var h=c.match(a.urlSchemeRegex);if(!h){return i}var d=h[0].toLowerCase().trim();if(a.invalidProtocolRegex.test(d)){return a.BLANK_URL}var u=c.replace(/\\/g,"/");if(d==="mailto:"||d.includes("://")){return u}if(d==="http:"||d==="https:"){if(!s(u)){return a.BLANK_URL}var f=new URL(u);f.protocol=f.protocol.toLowerCase();f.hostname=f.hostname.toLowerCase();return f.toString()}return u}e.J=c},74353:function(t){!function(e,r){true?t.exports=r():0}(this,(function(){"use strict";var t=1e3,e=6e4,r=36e5,i="millisecond",a="second",n="minute",o="hour",s="day",l="week",c="month",h="quarter",d="year",u="date",f="Invalid Date",p=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,g=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,m={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(t){var e=["th","st","nd","rd"],r=t%100;return"["+t+(e[(r-20)%10]||e[r]||e[0])+"]"}},y=function(t,e,r){var i=String(t);return!i||i.length>=e?t:""+Array(e+1-i.length).join(r)+t},b={s:y,z:function(t){var e=-t.utcOffset(),r=Math.abs(e),i=Math.floor(r/60),a=r%60;return(e<=0?"+":"-")+y(i,2,"0")+":"+y(a,2,"0")},m:function t(e,r){if(e.date()<r.date())return-t(r,e);var i=12*(r.year()-e.year())+(r.month()-e.month()),a=e.clone().add(i,c),n=r-a<0,o=e.clone().add(i+(n?-1:1),c);return+(-(i+(r-a)/(n?a-o:o-a))||0)},a:function(t){return t<0?Math.ceil(t)||0:Math.floor(t)},p:function(t){return{M:c,y:d,w:l,d:s,D:u,h:o,m:n,s:a,ms:i,Q:h}[t]||String(t||"").toLowerCase().replace(/s$/,"")},u:function(t){return void 0===t}},x="en",C={};C[x]=m;var v="$isDayjsObject",k=function(t){return t instanceof T||!(!t||!t[v])},w=function t(e,r,i){var a;if(!e)return x;if("string"==typeof e){var n=e.toLowerCase();C[n]&&(a=n),r&&(C[n]=r,a=n);var o=e.split("-");if(!a&&o.length>1)return t(o[0])}else{var s=e.name;C[s]=e,a=s}return!i&&a&&(x=a),a||!i&&x},S=function(t,e){if(k(t))return t.clone();var r="object"==typeof e?e:{};return r.date=t,r.args=arguments,new T(r)},A=b;A.l=w,A.i=k,A.w=function(t,e){return S(t,{locale:e.$L,utc:e.$u,x:e.$x,$offset:e.$offset})};var T=function(){function m(t){this.$L=w(t.locale,null,!0),this.parse(t),this.$x=this.$x||t.x||{},this[v]=!0}var y=m.prototype;return y.parse=function(t){this.$d=function(t){var e=t.date,r=t.utc;if(null===e)return new Date(NaN);if(A.u(e))return new Date;if(e instanceof Date)return new Date(e);if("string"==typeof e&&!/Z$/i.test(e)){var i=e.match(p);if(i){var a=i[2]-1||0,n=(i[7]||"0").substring(0,3);return r?new Date(Date.UTC(i[1],a,i[3]||1,i[4]||0,i[5]||0,i[6]||0,n)):new Date(i[1],a,i[3]||1,i[4]||0,i[5]||0,i[6]||0,n)}}return new Date(e)}(t),this.init()},y.init=function(){var t=this.$d;this.$y=t.getFullYear(),this.$M=t.getMonth(),this.$D=t.getDate(),this.$W=t.getDay(),this.$H=t.getHours(),this.$m=t.getMinutes(),this.$s=t.getSeconds(),this.$ms=t.getMilliseconds()},y.$utils=function(){return A},y.isValid=function(){return!(this.$d.toString()===f)},y.isSame=function(t,e){var r=S(t);return this.startOf(e)<=r&&r<=this.endOf(e)},y.isAfter=function(t,e){return S(t)<this.startOf(e)},y.isBefore=function(t,e){return this.endOf(e)<S(t)},y.$g=function(t,e,r){return A.u(t)?this[e]:this.set(r,t)},y.unix=function(){return Math.floor(this.valueOf()/1e3)},y.valueOf=function(){return this.$d.getTime()},y.startOf=function(t,e){var r=this,i=!!A.u(e)||e,h=A.p(t),f=function(t,e){var a=A.w(r.$u?Date.UTC(r.$y,e,t):new Date(r.$y,e,t),r);return i?a:a.endOf(s)},p=function(t,e){return A.w(r.toDate()[t].apply(r.toDate("s"),(i?[0,0,0,0]:[23,59,59,999]).slice(e)),r)},g=this.$W,m=this.$M,y=this.$D,b="set"+(this.$u?"UTC":"");switch(h){case d:return i?f(1,0):f(31,11);case c:return i?f(1,m):f(0,m+1);case l:var x=this.$locale().weekStart||0,C=(g<x?g+7:g)-x;return f(i?y-C:y+(6-C),m);case s:case u:return p(b+"Hours",0);case o:return p(b+"Minutes",1);case n:return p(b+"Seconds",2);case a:return p(b+"Milliseconds",3);default:return this.clone()}},y.endOf=function(t){return this.startOf(t,!1)},y.$set=function(t,e){var r,l=A.p(t),h="set"+(this.$u?"UTC":""),f=(r={},r[s]=h+"Date",r[u]=h+"Date",r[c]=h+"Month",r[d]=h+"FullYear",r[o]=h+"Hours",r[n]=h+"Minutes",r[a]=h+"Seconds",r[i]=h+"Milliseconds",r)[l],p=l===s?this.$D+(e-this.$W):e;if(l===c||l===d){var g=this.clone().set(u,1);g.$d[f](p),g.init(),this.$d=g.set(u,Math.min(this.$D,g.daysInMonth())).$d}else f&&this.$d[f](p);return this.init(),this},y.set=function(t,e){return this.clone().$set(t,e)},y.get=function(t){return this[A.p(t)]()},y.add=function(i,h){var u,f=this;i=Number(i);var p=A.p(h),g=function(t){var e=S(f);return A.w(e.date(e.date()+Math.round(t*i)),f)};if(p===c)return this.set(c,this.$M+i);if(p===d)return this.set(d,this.$y+i);if(p===s)return g(1);if(p===l)return g(7);var m=(u={},u[n]=e,u[o]=r,u[a]=t,u)[p]||1,y=this.$d.getTime()+i*m;return A.w(y,this)},y.subtract=function(t,e){return this.add(-1*t,e)},y.format=function(t){var e=this,r=this.$locale();if(!this.isValid())return r.invalidDate||f;var i=t||"YYYY-MM-DDTHH:mm:ssZ",a=A.z(this),n=this.$H,o=this.$m,s=this.$M,l=r.weekdays,c=r.months,h=r.meridiem,d=function(t,r,a,n){return t&&(t[r]||t(e,i))||a[r].slice(0,n)},u=function(t){return A.s(n%12||12,t,"0")},p=h||function(t,e,r){var i=t<12?"AM":"PM";return r?i.toLowerCase():i};return i.replace(g,(function(t,i){return i||function(t){switch(t){case"YY":return String(e.$y).slice(-2);case"YYYY":return A.s(e.$y,4,"0");case"M":return s+1;case"MM":return A.s(s+1,2,"0");case"MMM":return d(r.monthsShort,s,c,3);case"MMMM":return d(c,s);case"D":return e.$D;case"DD":return A.s(e.$D,2,"0");case"d":return String(e.$W);case"dd":return d(r.weekdaysMin,e.$W,l,2);case"ddd":return d(r.weekdaysShort,e.$W,l,3);case"dddd":return l[e.$W];case"H":return String(n);case"HH":return A.s(n,2,"0");case"h":return u(1);case"hh":return u(2);case"a":return p(n,o,!0);case"A":return p(n,o,!1);case"m":return String(o);case"mm":return A.s(o,2,"0");case"s":return String(e.$s);case"ss":return A.s(e.$s,2,"0");case"SSS":return A.s(e.$ms,3,"0");case"Z":return a}return null}(t)||a.replace(":","")}))},y.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},y.diff=function(i,u,f){var p,g=this,m=A.p(u),y=S(i),b=(y.utcOffset()-this.utcOffset())*e,x=this-y,C=function(){return A.m(g,y)};switch(m){case d:p=C()/12;break;case c:p=C();break;case h:p=C()/3;break;case l:p=(x-b)/6048e5;break;case s:p=(x-b)/864e5;break;case o:p=x/r;break;case n:p=x/e;break;case a:p=x/t;break;default:p=x}return f?p:A.a(p)},y.daysInMonth=function(){return this.endOf(c).$D},y.$locale=function(){return C[this.$L]},y.locale=function(t,e){if(!t)return this.$L;var r=this.clone(),i=w(t,e,!0);return i&&(r.$L=i),r},y.clone=function(){return A.w(this.$d,this)},y.toDate=function(){return new Date(this.valueOf())},y.toJSON=function(){return this.isValid()?this.toISOString():null},y.toISOString=function(){return this.$d.toISOString()},y.toString=function(){return this.$d.toUTCString()},m}(),B=T.prototype;return S.prototype=B,[["$ms",i],["$s",a],["$m",n],["$H",o],["$W",s],["$M",c],["$y",d],["$D",u]].forEach((function(t){B[t[1]]=function(e){return this.$g(e,t[0],t[1])}})),S.extend=function(t,e){return t.$i||(t(e,T,S),t.$i=!0),S},S.locale=w,S.isDayjs=k,S.unix=function(t){return S(1e3*t)},S.en=C[x],S.Ls=C,S.p={},S}))},84997:(t,e,r)=>{"use strict";r.d(e,{A:()=>ot});const{entries:i,setPrototypeOf:a,isFrozen:n,getPrototypeOf:o,getOwnPropertyDescriptor:s}=Object;let{freeze:l,seal:c,create:h}=Object;let{apply:d,construct:u}=typeof Reflect!=="undefined"&&Reflect;if(!l){l=function t(e){return e}}if(!c){c=function t(e){return e}}if(!d){d=function t(e,r,i){return e.apply(r,i)}}if(!u){u=function t(e,r){return new e(...r)}}const f=B(Array.prototype.forEach);const p=B(Array.prototype.lastIndexOf);const g=B(Array.prototype.pop);const m=B(Array.prototype.push);const y=B(Array.prototype.splice);const b=B(String.prototype.toLowerCase);const x=B(String.prototype.toString);const C=B(String.prototype.match);const v=B(String.prototype.replace);const k=B(String.prototype.indexOf);const w=B(String.prototype.trim);const S=B(Object.prototype.hasOwnProperty);const A=B(RegExp.prototype.test);const T=L(TypeError);function B(t){return function(e){for(var r=arguments.length,i=new Array(r>1?r-1:0),a=1;a<r;a++){i[a-1]=arguments[a]}return d(t,e,i)}}function L(t){return function(){for(var e=arguments.length,r=new Array(e),i=0;i<e;i++){r[i]=arguments[i]}return u(t,r)}}function M(t,e){let r=arguments.length>2&&arguments[2]!==undefined?arguments[2]:b;if(a){a(t,null)}let i=e.length;while(i--){let a=e[i];if(typeof a==="string"){const t=r(a);if(t!==a){if(!n(e)){e[i]=t}a=t}}t[a]=true}return t}function _(t){for(let e=0;e<t.length;e++){const r=S(t,e);if(!r){t[e]=null}}return t}function F(t){const e=h(null);for(const[r,a]of i(t)){const i=S(t,r);if(i){if(Array.isArray(a)){e[r]=_(a)}else if(a&&typeof a==="object"&&a.constructor===Object){e[r]=F(a)}else{e[r]=a}}}return e}function $(t,e){while(t!==null){const r=s(t,e);if(r){if(r.get){return B(r.get)}if(typeof r.value==="function"){return B(r.value)}}t=o(t)}function r(){return null}return r}const E=l(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]);const O=l(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]);const D=l(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]);const I=l(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]);const K=l(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]);const R=l(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]);const P=l(["#text"]);const z=l(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]);const q=l(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]);const N=l(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]);const W=l(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]);const j=c(/\{\{[\w\W]*|[\w\W]*\}\}/gm);const H=c(/<%[\w\W]*|[\w\W]*%>/gm);const Y=c(/\$\{[\w\W]*/gm);const U=c(/^data-[\-\w.\u00B7-\uFFFF]+$/);const G=c(/^aria-[\-\w]+$/);const V=c(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i);const X=c(/^(?:\w+script|data):/i);const Z=c(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g);const J=c(/^html$/i);const Q=c(/^[a-z][.\w]*(-[.\w]+)+$/i);var tt=Object.freeze({__proto__:null,ARIA_ATTR:G,ATTR_WHITESPACE:Z,CUSTOM_ELEMENT:Q,DATA_ATTR:U,DOCTYPE_NAME:J,ERB_EXPR:H,IS_ALLOWED_URI:V,IS_SCRIPT_OR_DATA:X,MUSTACHE_EXPR:j,TMPLIT_EXPR:Y});const et={element:1,attribute:2,text:3,cdataSection:4,entityReference:5,entityNode:6,progressingInstruction:7,comment:8,document:9,documentType:10,documentFragment:11,notation:12};const rt=function t(){return typeof window==="undefined"?null:window};const it=function t(e,r){if(typeof e!=="object"||typeof e.createPolicy!=="function"){return null}let i=null;const a="data-tt-policy-suffix";if(r&&r.hasAttribute(a)){i=r.getAttribute(a)}const n="dompurify"+(i?"#"+i:"");try{return e.createPolicy(n,{createHTML(t){return t},createScriptURL(t){return t}})}catch(o){console.warn("TrustedTypes policy "+n+" could not be created.");return null}};const at=function t(){return{afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}};function nt(){let t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:rt();const e=t=>nt(t);e.version="3.2.4";e.removed=[];if(!t||!t.document||t.document.nodeType!==et.document||!t.Element){e.isSupported=false;return e}let{document:r}=t;const a=r;const n=a.currentScript;const{DocumentFragment:o,HTMLTemplateElement:s,Node:c,Element:d,NodeFilter:u,NamedNodeMap:B=t.NamedNodeMap||t.MozNamedAttrMap,HTMLFormElement:L,DOMParser:_,trustedTypes:j}=t;const H=d.prototype;const Y=$(H,"cloneNode");const U=$(H,"remove");const G=$(H,"nextSibling");const X=$(H,"childNodes");const Z=$(H,"parentNode");if(typeof s==="function"){const t=r.createElement("template");if(t.content&&t.content.ownerDocument){r=t.content.ownerDocument}}let Q;let ot="";const{implementation:st,createNodeIterator:lt,createDocumentFragment:ct,getElementsByTagName:ht}=r;const{importNode:dt}=a;let ut=at();e.isSupported=typeof i==="function"&&typeof Z==="function"&&st&&st.createHTMLDocument!==undefined;const{MUSTACHE_EXPR:ft,ERB_EXPR:pt,TMPLIT_EXPR:gt,DATA_ATTR:mt,ARIA_ATTR:yt,IS_SCRIPT_OR_DATA:bt,ATTR_WHITESPACE:xt,CUSTOM_ELEMENT:Ct}=tt;let{IS_ALLOWED_URI:vt}=tt;let kt=null;const wt=M({},[...E,...O,...D,...K,...P]);let St=null;const At=M({},[...z,...q,...N,...W]);let Tt=Object.seal(h(null,{tagNameCheck:{writable:true,configurable:false,enumerable:true,value:null},attributeNameCheck:{writable:true,configurable:false,enumerable:true,value:null},allowCustomizedBuiltInElements:{writable:true,configurable:false,enumerable:true,value:false}}));let Bt=null;let Lt=null;let Mt=true;let _t=true;let Ft=false;let $t=true;let Et=false;let Ot=true;let Dt=false;let It=false;let Kt=false;let Rt=false;let Pt=false;let zt=false;let qt=true;let Nt=false;const Wt="user-content-";let jt=true;let Ht=false;let Yt={};let Ut=null;const Gt=M({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let Vt=null;const Xt=M({},["audio","video","img","source","image","track"]);let Zt=null;const Jt=M({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]);const Qt="http://www.w3.org/1998/Math/MathML";const te="http://www.w3.org/2000/svg";const ee="http://www.w3.org/1999/xhtml";let re=ee;let ie=false;let ae=null;const ne=M({},[Qt,te,ee],x);let oe=M({},["mi","mo","mn","ms","mtext"]);let se=M({},["annotation-xml"]);const le=M({},["title","style","font","a","script"]);let ce=null;const he=["application/xhtml+xml","text/html"];const de="text/html";let ue=null;let fe=null;const pe=r.createElement("form");const ge=function t(e){return e instanceof RegExp||e instanceof Function};const me=function t(){let e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};if(fe&&fe===e){return}if(!e||typeof e!=="object"){e={}}e=F(e);ce=he.indexOf(e.PARSER_MEDIA_TYPE)===-1?de:e.PARSER_MEDIA_TYPE;ue=ce==="application/xhtml+xml"?x:b;kt=S(e,"ALLOWED_TAGS")?M({},e.ALLOWED_TAGS,ue):wt;St=S(e,"ALLOWED_ATTR")?M({},e.ALLOWED_ATTR,ue):At;ae=S(e,"ALLOWED_NAMESPACES")?M({},e.ALLOWED_NAMESPACES,x):ne;Zt=S(e,"ADD_URI_SAFE_ATTR")?M(F(Jt),e.ADD_URI_SAFE_ATTR,ue):Jt;Vt=S(e,"ADD_DATA_URI_TAGS")?M(F(Xt),e.ADD_DATA_URI_TAGS,ue):Xt;Ut=S(e,"FORBID_CONTENTS")?M({},e.FORBID_CONTENTS,ue):Gt;Bt=S(e,"FORBID_TAGS")?M({},e.FORBID_TAGS,ue):{};Lt=S(e,"FORBID_ATTR")?M({},e.FORBID_ATTR,ue):{};Yt=S(e,"USE_PROFILES")?e.USE_PROFILES:false;Mt=e.ALLOW_ARIA_ATTR!==false;_t=e.ALLOW_DATA_ATTR!==false;Ft=e.ALLOW_UNKNOWN_PROTOCOLS||false;$t=e.ALLOW_SELF_CLOSE_IN_ATTR!==false;Et=e.SAFE_FOR_TEMPLATES||false;Ot=e.SAFE_FOR_XML!==false;Dt=e.WHOLE_DOCUMENT||false;Rt=e.RETURN_DOM||false;Pt=e.RETURN_DOM_FRAGMENT||false;zt=e.RETURN_TRUSTED_TYPE||false;Kt=e.FORCE_BODY||false;qt=e.SANITIZE_DOM!==false;Nt=e.SANITIZE_NAMED_PROPS||false;jt=e.KEEP_CONTENT!==false;Ht=e.IN_PLACE||false;vt=e.ALLOWED_URI_REGEXP||V;re=e.NAMESPACE||ee;oe=e.MATHML_TEXT_INTEGRATION_POINTS||oe;se=e.HTML_INTEGRATION_POINTS||se;Tt=e.CUSTOM_ELEMENT_HANDLING||{};if(e.CUSTOM_ELEMENT_HANDLING&&ge(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)){Tt.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck}if(e.CUSTOM_ELEMENT_HANDLING&&ge(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)){Tt.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck}if(e.CUSTOM_ELEMENT_HANDLING&&typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements==="boolean"){Tt.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements}if(Et){_t=false}if(Pt){Rt=true}if(Yt){kt=M({},P);St=[];if(Yt.html===true){M(kt,E);M(St,z)}if(Yt.svg===true){M(kt,O);M(St,q);M(St,W)}if(Yt.svgFilters===true){M(kt,D);M(St,q);M(St,W)}if(Yt.mathMl===true){M(kt,K);M(St,N);M(St,W)}}if(e.ADD_TAGS){if(kt===wt){kt=F(kt)}M(kt,e.ADD_TAGS,ue)}if(e.ADD_ATTR){if(St===At){St=F(St)}M(St,e.ADD_ATTR,ue)}if(e.ADD_URI_SAFE_ATTR){M(Zt,e.ADD_URI_SAFE_ATTR,ue)}if(e.FORBID_CONTENTS){if(Ut===Gt){Ut=F(Ut)}M(Ut,e.FORBID_CONTENTS,ue)}if(jt){kt["#text"]=true}if(Dt){M(kt,["html","head","body"])}if(kt.table){M(kt,["tbody"]);delete Bt.tbody}if(e.TRUSTED_TYPES_POLICY){if(typeof e.TRUSTED_TYPES_POLICY.createHTML!=="function"){throw T('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.')}if(typeof e.TRUSTED_TYPES_POLICY.createScriptURL!=="function"){throw T('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.')}Q=e.TRUSTED_TYPES_POLICY;ot=Q.createHTML("")}else{if(Q===undefined){Q=it(j,n)}if(Q!==null&&typeof ot==="string"){ot=Q.createHTML("")}}if(l){l(e)}fe=e};const ye=M({},[...O,...D,...I]);const be=M({},[...K,...R]);const xe=function t(e){let r=Z(e);if(!r||!r.tagName){r={namespaceURI:re,tagName:"template"}}const i=b(e.tagName);const a=b(r.tagName);if(!ae[e.namespaceURI]){return false}if(e.namespaceURI===te){if(r.namespaceURI===ee){return i==="svg"}if(r.namespaceURI===Qt){return i==="svg"&&(a==="annotation-xml"||oe[a])}return Boolean(ye[i])}if(e.namespaceURI===Qt){if(r.namespaceURI===ee){return i==="math"}if(r.namespaceURI===te){return i==="math"&&se[a]}return Boolean(be[i])}if(e.namespaceURI===ee){if(r.namespaceURI===te&&!se[a]){return false}if(r.namespaceURI===Qt&&!oe[a]){return false}return!be[i]&&(le[i]||!ye[i])}if(ce==="application/xhtml+xml"&&ae[e.namespaceURI]){return true}return false};const Ce=function t(r){m(e.removed,{element:r});try{Z(r).removeChild(r)}catch(i){U(r)}};const ve=function t(r,i){try{m(e.removed,{attribute:i.getAttributeNode(r),from:i})}catch(a){m(e.removed,{attribute:null,from:i})}i.removeAttribute(r);if(r==="is"){if(Rt||Pt){try{Ce(i)}catch(a){}}else{try{i.setAttribute(r,"")}catch(a){}}}};const ke=function t(e){let i=null;let a=null;if(Kt){e="<remove></remove>"+e}else{const t=C(e,/^[\r\n\t ]+/);a=t&&t[0]}if(ce==="application/xhtml+xml"&&re===ee){e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>"}const n=Q?Q.createHTML(e):e;if(re===ee){try{i=(new _).parseFromString(n,ce)}catch(s){}}if(!i||!i.documentElement){i=st.createDocument(re,"template",null);try{i.documentElement.innerHTML=ie?ot:n}catch(s){}}const o=i.body||i.documentElement;if(e&&a){o.insertBefore(r.createTextNode(a),o.childNodes[0]||null)}if(re===ee){return ht.call(i,Dt?"html":"body")[0]}return Dt?i.documentElement:o};const we=function t(e){return lt.call(e.ownerDocument||e,e,u.SHOW_ELEMENT|u.SHOW_COMMENT|u.SHOW_TEXT|u.SHOW_PROCESSING_INSTRUCTION|u.SHOW_CDATA_SECTION,null)};const Se=function t(e){return e instanceof L&&(typeof e.nodeName!=="string"||typeof e.textContent!=="string"||typeof e.removeChild!=="function"||!(e.attributes instanceof B)||typeof e.removeAttribute!=="function"||typeof e.setAttribute!=="function"||typeof e.namespaceURI!=="string"||typeof e.insertBefore!=="function"||typeof e.hasChildNodes!=="function")};const Ae=function t(e){return typeof c==="function"&&e instanceof c};function Te(t,r,i){f(t,(t=>{t.call(e,r,i,fe)}))}const Be=function t(r){let i=null;Te(ut.beforeSanitizeElements,r,null);if(Se(r)){Ce(r);return true}const a=ue(r.nodeName);Te(ut.uponSanitizeElement,r,{tagName:a,allowedTags:kt});if(r.hasChildNodes()&&!Ae(r.firstElementChild)&&A(/<[/\w]/g,r.innerHTML)&&A(/<[/\w]/g,r.textContent)){Ce(r);return true}if(r.nodeType===et.progressingInstruction){Ce(r);return true}if(Ot&&r.nodeType===et.comment&&A(/<[/\w]/g,r.data)){Ce(r);return true}if(!kt[a]||Bt[a]){if(!Bt[a]&&Me(a)){if(Tt.tagNameCheck instanceof RegExp&&A(Tt.tagNameCheck,a)){return false}if(Tt.tagNameCheck instanceof Function&&Tt.tagNameCheck(a)){return false}}if(jt&&!Ut[a]){const t=Z(r)||r.parentNode;const e=X(r)||r.childNodes;if(e&&t){const i=e.length;for(let a=i-1;a>=0;--a){const i=Y(e[a],true);i.__removalCount=(r.__removalCount||0)+1;t.insertBefore(i,G(r))}}}Ce(r);return true}if(r instanceof d&&!xe(r)){Ce(r);return true}if((a==="noscript"||a==="noembed"||a==="noframes")&&A(/<\/no(script|embed|frames)/i,r.innerHTML)){Ce(r);return true}if(Et&&r.nodeType===et.text){i=r.textContent;f([ft,pt,gt],(t=>{i=v(i,t," ")}));if(r.textContent!==i){m(e.removed,{element:r.cloneNode()});r.textContent=i}}Te(ut.afterSanitizeElements,r,null);return false};const Le=function t(e,i,a){if(qt&&(i==="id"||i==="name")&&(a in r||a in pe)){return false}if(_t&&!Lt[i]&&A(mt,i));else if(Mt&&A(yt,i));else if(!St[i]||Lt[i]){if(Me(e)&&(Tt.tagNameCheck instanceof RegExp&&A(Tt.tagNameCheck,e)||Tt.tagNameCheck instanceof Function&&Tt.tagNameCheck(e))&&(Tt.attributeNameCheck instanceof RegExp&&A(Tt.attributeNameCheck,i)||Tt.attributeNameCheck instanceof Function&&Tt.attributeNameCheck(i))||i==="is"&&Tt.allowCustomizedBuiltInElements&&(Tt.tagNameCheck instanceof RegExp&&A(Tt.tagNameCheck,a)||Tt.tagNameCheck instanceof Function&&Tt.tagNameCheck(a)));else{return false}}else if(Zt[i]);else if(A(vt,v(a,xt,"")));else if((i==="src"||i==="xlink:href"||i==="href")&&e!=="script"&&k(a,"data:")===0&&Vt[e]);else if(Ft&&!A(bt,v(a,xt,"")));else if(a){return false}else;return true};const Me=function t(e){return e!=="annotation-xml"&&C(e,Ct)};const _e=function t(r){Te(ut.beforeSanitizeAttributes,r,null);const{attributes:i}=r;if(!i||Se(r)){return}const a={attrName:"",attrValue:"",keepAttr:true,allowedAttributes:St,forceKeepAttr:undefined};let n=i.length;while(n--){const t=i[n];const{name:s,namespaceURI:l,value:c}=t;const h=ue(s);let d=s==="value"?c:w(c);a.attrName=h;a.attrValue=d;a.keepAttr=true;a.forceKeepAttr=undefined;Te(ut.uponSanitizeAttribute,r,a);d=a.attrValue;if(Nt&&(h==="id"||h==="name")){ve(s,r);d=Wt+d}if(Ot&&A(/((--!?|])>)|<\/(style|title)/i,d)){ve(s,r);continue}if(a.forceKeepAttr){continue}ve(s,r);if(!a.keepAttr){continue}if(!$t&&A(/\/>/i,d)){ve(s,r);continue}if(Et){f([ft,pt,gt],(t=>{d=v(d,t," ")}))}const u=ue(r.nodeName);if(!Le(u,h,d)){continue}if(Q&&typeof j==="object"&&typeof j.getAttributeType==="function"){if(l);else{switch(j.getAttributeType(u,h)){case"TrustedHTML":{d=Q.createHTML(d);break}case"TrustedScriptURL":{d=Q.createScriptURL(d);break}}}}try{if(l){r.setAttributeNS(l,s,d)}else{r.setAttribute(s,d)}if(Se(r)){Ce(r)}else{g(e.removed)}}catch(o){}}Te(ut.afterSanitizeAttributes,r,null)};const Fe=function t(e){let r=null;const i=we(e);Te(ut.beforeSanitizeShadowDOM,e,null);while(r=i.nextNode()){Te(ut.uponSanitizeShadowNode,r,null);Be(r);_e(r);if(r.content instanceof o){t(r.content)}}Te(ut.afterSanitizeShadowDOM,e,null)};e.sanitize=function(t){let r=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};let i=null;let n=null;let s=null;let l=null;ie=!t;if(ie){t="\x3c!--\x3e"}if(typeof t!=="string"&&!Ae(t)){if(typeof t.toString==="function"){t=t.toString();if(typeof t!=="string"){throw T("dirty is not a string, aborting")}}else{throw T("toString is not a function")}}if(!e.isSupported){return t}if(!It){me(r)}e.removed=[];if(typeof t==="string"){Ht=false}if(Ht){if(t.nodeName){const e=ue(t.nodeName);if(!kt[e]||Bt[e]){throw T("root node is forbidden and cannot be sanitized in-place")}}}else if(t instanceof c){i=ke("\x3c!----\x3e");n=i.ownerDocument.importNode(t,true);if(n.nodeType===et.element&&n.nodeName==="BODY"){i=n}else if(n.nodeName==="HTML"){i=n}else{i.appendChild(n)}}else{if(!Rt&&!Et&&!Dt&&t.indexOf("<")===-1){return Q&&zt?Q.createHTML(t):t}i=ke(t);if(!i){return Rt?null:zt?ot:""}}if(i&&Kt){Ce(i.firstChild)}const h=we(Ht?t:i);while(s=h.nextNode()){Be(s);_e(s);if(s.content instanceof o){Fe(s.content)}}if(Ht){return t}if(Rt){if(Pt){l=ct.call(i.ownerDocument);while(i.firstChild){l.appendChild(i.firstChild)}}else{l=i}if(St.shadowroot||St.shadowrootmode){l=dt.call(a,l,true)}return l}let d=Dt?i.outerHTML:i.innerHTML;if(Dt&&kt["!doctype"]&&i.ownerDocument&&i.ownerDocument.doctype&&i.ownerDocument.doctype.name&&A(J,i.ownerDocument.doctype.name)){d="<!DOCTYPE "+i.ownerDocument.doctype.name+">\n"+d}if(Et){f([ft,pt,gt],(t=>{d=v(d,t," ")}))}return Q&&zt?Q.createHTML(d):d};e.setConfig=function(){let t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};me(t);It=true};e.clearConfig=function(){fe=null;It=false};e.isValidAttribute=function(t,e,r){if(!fe){me({})}const i=ue(t);const a=ue(e);return Le(i,a,r)};e.addHook=function(t,e){if(typeof e!=="function"){return}m(ut[t],e)};e.removeHook=function(t,e){if(e!==undefined){const r=p(ut[t],e);return r===-1?undefined:y(ut[t],r,1)[0]}return g(ut[t])};e.removeHooks=function(t){ut[t]=[]};e.removeAllHooks=function(){ut=at()};return e}var ot=nt()},25e3:(t,e,r)=>{"use strict";r.d(e,{A:()=>h});var i=r(57991);var a=r(59773);class n{constructor(){this.type=a.Z.ALL}get(){return this.type}set(t){if(this.type&&this.type!==t)throw new Error("Cannot change both RGB and HSL channels at the same time");this.type=t}reset(){this.type=a.Z.ALL}is(t){return this.type===t}}const o=n;class s{constructor(t,e){this.color=e;this.changed=false;this.data=t;this.type=new o}set(t,e){this.color=e;this.changed=false;this.data=t;this.type.type=a.Z.ALL;return this}_ensureHSL(){const t=this.data;const{h:e,s:r,l:a}=t;if(e===undefined)t.h=i.A.channel.rgb2hsl(t,"h");if(r===undefined)t.s=i.A.channel.rgb2hsl(t,"s");if(a===undefined)t.l=i.A.channel.rgb2hsl(t,"l")}_ensureRGB(){const t=this.data;const{r:e,g:r,b:a}=t;if(e===undefined)t.r=i.A.channel.hsl2rgb(t,"r");if(r===undefined)t.g=i.A.channel.hsl2rgb(t,"g");if(a===undefined)t.b=i.A.channel.hsl2rgb(t,"b")}get r(){const t=this.data;const e=t.r;if(!this.type.is(a.Z.HSL)&&e!==undefined)return e;this._ensureHSL();return i.A.channel.hsl2rgb(t,"r")}get g(){const t=this.data;const e=t.g;if(!this.type.is(a.Z.HSL)&&e!==undefined)return e;this._ensureHSL();return i.A.channel.hsl2rgb(t,"g")}get b(){const t=this.data;const e=t.b;if(!this.type.is(a.Z.HSL)&&e!==undefined)return e;this._ensureHSL();return i.A.channel.hsl2rgb(t,"b")}get h(){const t=this.data;const e=t.h;if(!this.type.is(a.Z.RGB)&&e!==undefined)return e;this._ensureRGB();return i.A.channel.rgb2hsl(t,"h")}get s(){const t=this.data;const e=t.s;if(!this.type.is(a.Z.RGB)&&e!==undefined)return e;this._ensureRGB();return i.A.channel.rgb2hsl(t,"s")}get l(){const t=this.data;const e=t.l;if(!this.type.is(a.Z.RGB)&&e!==undefined)return e;this._ensureRGB();return i.A.channel.rgb2hsl(t,"l")}get a(){return this.data.a}set r(t){this.type.set(a.Z.RGB);this.changed=true;this.data.r=t}set g(t){this.type.set(a.Z.RGB);this.changed=true;this.data.g=t}set b(t){this.type.set(a.Z.RGB);this.changed=true;this.data.b=t}set h(t){this.type.set(a.Z.HSL);this.changed=true;this.data.h=t}set s(t){this.type.set(a.Z.HSL);this.changed=true;this.data.s=t}set l(t){this.type.set(a.Z.HSL);this.changed=true;this.data.l=t}set a(t){this.changed=true;this.data.a=t}}const l=s;const c=new l({r:0,g:0,b:0,a:0},"transparent");const h=c},63221:(t,e,r)=>{"use strict";r.d(e,{A:()=>g});var i=r(25e3);var a=r(59773);const n={re:/^#((?:[a-f0-9]{2}){2,4}|[a-f0-9]{3})$/i,parse:t=>{if(t.charCodeAt(0)!==35)return;const e=t.match(n.re);if(!e)return;const r=e[1];const a=parseInt(r,16);const o=r.length;const s=o%4===0;const l=o>4;const c=l?1:17;const h=l?8:4;const d=s?0:-1;const u=l?255:15;return i.A.set({r:(a>>h*(d+3)&u)*c,g:(a>>h*(d+2)&u)*c,b:(a>>h*(d+1)&u)*c,a:s?(a&u)*c/255:1},t)},stringify:t=>{const{r:e,g:r,b:i,a:n}=t;if(n<1){return`#${a.Y[Math.round(e)]}${a.Y[Math.round(r)]}${a.Y[Math.round(i)]}${a.Y[Math.round(n*255)]}`}else{return`#${a.Y[Math.round(e)]}${a.Y[Math.round(r)]}${a.Y[Math.round(i)]}`}}};const o=n;var s=r(57991);const l={re:/^hsla?\(\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e-?\d+)?(?:deg|grad|rad|turn)?)\s*?(?:,|\s)\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e-?\d+)?%)\s*?(?:,|\s)\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e-?\d+)?%)(?:\s*?(?:,|\/)\s*?\+?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e-?\d+)?(%)?))?\s*?\)$/i,hueRe:/^(.+?)(deg|grad|rad|turn)$/i,_hue2deg:t=>{const e=t.match(l.hueRe);if(e){const[,t,r]=e;switch(r){case"grad":return s.A.channel.clamp.h(parseFloat(t)*.9);case"rad":return s.A.channel.clamp.h(parseFloat(t)*180/Math.PI);case"turn":return s.A.channel.clamp.h(parseFloat(t)*360)}}return s.A.channel.clamp.h(parseFloat(t))},parse:t=>{const e=t.charCodeAt(0);if(e!==104&&e!==72)return;const r=t.match(l.re);if(!r)return;const[,a,n,o,c,h]=r;return i.A.set({h:l._hue2deg(a),s:s.A.channel.clamp.s(parseFloat(n)),l:s.A.channel.clamp.l(parseFloat(o)),a:c?s.A.channel.clamp.a(h?parseFloat(c)/100:parseFloat(c)):1},t)},stringify:t=>{const{h:e,s:r,l:i,a}=t;if(a<1){return`hsla(${s.A.lang.round(e)}, ${s.A.lang.round(r)}%, ${s.A.lang.round(i)}%, ${a})`}else{return`hsl(${s.A.lang.round(e)}, ${s.A.lang.round(r)}%, ${s.A.lang.round(i)}%)`}}};const c=l;const h={colors:{aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyanaqua:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgreen:"#006400",darkgrey:"#a9a9a9",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",green:"#008000",greenyellow:"#adff2f",grey:"#808080",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgreen:"#90ee90",lightgrey:"#d3d3d3",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",transparent:"#00000000",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32"},parse:t=>{t=t.toLowerCase();const e=h.colors[t];if(!e)return;return o.parse(e)},stringify:t=>{const e=o.stringify(t);for(const r in h.colors){if(h.colors[r]===e)return r}return}};const d=h;const u={re:/^rgba?\(\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e\d+)?(%?))\s*?(?:,|\s)\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e\d+)?(%?))\s*?(?:,|\s)\s*?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e\d+)?(%?))(?:\s*?(?:,|\/)\s*?\+?(-?(?:\d+(?:\.\d+)?|(?:\.\d+))(?:e\d+)?(%?)))?\s*?\)$/i,parse:t=>{const e=t.charCodeAt(0);if(e!==114&&e!==82)return;const r=t.match(u.re);if(!r)return;const[,a,n,o,l,c,h,d,f]=r;return i.A.set({r:s.A.channel.clamp.r(n?parseFloat(a)*2.55:parseFloat(a)),g:s.A.channel.clamp.g(l?parseFloat(o)*2.55:parseFloat(o)),b:s.A.channel.clamp.b(h?parseFloat(c)*2.55:parseFloat(c)),a:d?s.A.channel.clamp.a(f?parseFloat(d)/100:parseFloat(d)):1},t)},stringify:t=>{const{r:e,g:r,b:i,a}=t;if(a<1){return`rgba(${s.A.lang.round(e)}, ${s.A.lang.round(r)}, ${s.A.lang.round(i)}, ${s.A.lang.round(a)})`}else{return`rgb(${s.A.lang.round(e)}, ${s.A.lang.round(r)}, ${s.A.lang.round(i)})`}}};const f=u;const p={format:{keyword:d,hex:o,rgb:f,rgba:f,hsl:c,hsla:c},parse:t=>{if(typeof t!=="string")return t;const e=o.parse(t)||f.parse(t)||c.parse(t)||d.parse(t);if(e)return e;throw new Error(`Unsupported color format: "${t}"`)},stringify:t=>{if(!t.changed&&t.color)return t.color;if(t.type.is(a.Z.HSL)||t.data.r===undefined){return c.stringify(t)}else if(t.a<1||!Number.isInteger(t.r)||!Number.isInteger(t.g)||!Number.isInteger(t.b)){return f.stringify(t)}else{return o.stringify(t)}}};const g=p},59773:(t,e,r)=>{"use strict";r.d(e,{Y:()=>a,Z:()=>n});var i=r(57991);const a={};for(let o=0;o<=255;o++)a[o]=i.A.unit.dec2hex(o);const n={ALL:0,RGB:1,HSL:2}},42198:(t,e,r)=>{"use strict";r.d(e,{A:()=>o});var i=r(57991);var a=r(63221);const n=(t,e,r)=>{const n=a.A.parse(t);const o=n[e];const s=i.A.channel.clamp[e](o+r);if(o!==s)n[e]=s;return a.A.stringify(n)};const o=n},69745:(t,e,r)=>{"use strict";r.d(e,{A:()=>o});var i=r(57991);var a=r(63221);const n=(t,e)=>{const r=a.A.parse(t);for(const a in e){r[a]=i.A.channel.clamp[a](e[a])}return a.A.stringify(r)};const o=n},48750:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});var i=r(42198);const a=(t,e)=>(0,i.A)(t,"l",-e);const n=a},63170:(t,e,r)=>{"use strict";r.d(e,{A:()=>h});var i=r(57991);var a=r(63221);const n=t=>{const{r:e,g:r,b:n}=a.A.parse(t);const o=.2126*i.A.channel.toLinear(e)+.7152*i.A.channel.toLinear(r)+.0722*i.A.channel.toLinear(n);return i.A.lang.round(o)};const o=n;const s=t=>o(t)>=.5;const l=s;const c=t=>!l(t);const h=c},77470:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});var i=r(42198);const a=(t,e)=>(0,i.A)(t,"l",e);const n=a},3635:(t,e,r)=>{"use strict";r.d(e,{A:()=>l});var i=r(57991);var a=r(25e3);var n=r(63221);var o=r(69745);const s=(t,e,r=0,s=1)=>{if(typeof t!=="number")return(0,o.A)(t,{a:e});const l=a.A.set({r:i.A.channel.clamp.r(t),g:i.A.channel.clamp.g(e),b:i.A.channel.clamp.b(r),a:i.A.channel.clamp.a(s)});return n.A.stringify(l)};const l=s},57991:(t,e,r)=>{"use strict";r.d(e,{A:()=>h});const i={min:{r:0,g:0,b:0,s:0,l:0,a:0},max:{r:255,g:255,b:255,h:360,s:100,l:100,a:1},clamp:{r:t=>t>=255?255:t<0?0:t,g:t=>t>=255?255:t<0?0:t,b:t=>t>=255?255:t<0?0:t,h:t=>t%360,s:t=>t>=100?100:t<0?0:t,l:t=>t>=100?100:t<0?0:t,a:t=>t>=1?1:t<0?0:t},toLinear:t=>{const e=t/255;return t>.03928?Math.pow((e+.055)/1.055,2.4):e/12.92},hue2rgb:(t,e,r)=>{if(r<0)r+=1;if(r>1)r-=1;if(r<1/6)return t+(e-t)*6*r;if(r<1/2)return e;if(r<2/3)return t+(e-t)*(2/3-r)*6;return t},hsl2rgb:({h:t,s:e,l:r},a)=>{if(!e)return r*2.55;t/=360;e/=100;r/=100;const n=r<.5?r*(1+e):r+e-r*e;const o=2*r-n;switch(a){case"r":return i.hue2rgb(o,n,t+1/3)*255;case"g":return i.hue2rgb(o,n,t)*255;case"b":return i.hue2rgb(o,n,t-1/3)*255}},rgb2hsl:({r:t,g:e,b:r},i)=>{t/=255;e/=255;r/=255;const a=Math.max(t,e,r);const n=Math.min(t,e,r);const o=(a+n)/2;if(i==="l")return o*100;if(a===n)return 0;const s=a-n;const l=o>.5?s/(2-a-n):s/(a+n);if(i==="s")return l*100;switch(a){case t:return((e-r)/s+(e<r?6:0))*60;case e:return((r-t)/s+2)*60;case r:return((t-e)/s+4)*60;default:return-1}}};const a=i;const n={clamp:(t,e,r)=>{if(e>r)return Math.min(e,Math.max(r,t));return Math.min(r,Math.max(e,t))},round:t=>Math.round(t*1e10)/1e10};const o=n;const s={dec2hex:t=>{const e=Math.round(t).toString(16);return e.length>1?e:`0${e}`}};const l=s;const c={channel:a,lang:o,unit:l};const h=c},54951:(t,e,r)=>{"use strict";r.d(e,{A:()=>x});function i(){this.__data__=[];this.size=0}const a=i;var n=r(24461);function o(t,e){var r=t.length;while(r--){if((0,n.A)(t[r][0],e)){return r}}return-1}const s=o;var l=Array.prototype;var c=l.splice;function h(t){var e=this.__data__,r=s(e,t);if(r<0){return false}var i=e.length-1;if(r==i){e.pop()}else{c.call(e,r,1)}--this.size;return true}const d=h;function u(t){var e=this.__data__,r=s(e,t);return r<0?undefined:e[r][1]}const f=u;function p(t){return s(this.__data__,t)>-1}const g=p;function m(t,e){var r=this.__data__,i=s(r,t);if(i<0){++this.size;r.push([t,e])}else{r[i][1]=e}return this}const y=m;function b(t){var e=-1,r=t==null?0:t.length;this.clear();while(++e<r){var i=t[e];this.set(i[0],i[1])}}b.prototype.clear=a;b.prototype["delete"]=d;b.prototype.get=f;b.prototype.has=g;b.prototype.set=y;const x=b},51482:(t,e,r)=>{"use strict";r.d(e,{A:()=>o});var i=r(39023);var a=r(24606);var n=(0,i.A)(a.A,"Map");const o=n},9883:(t,e,r)=>{"use strict";r.d(e,{A:()=>q});var i=r(39023);var a=(0,i.A)(Object,"create");const n=a;function o(){this.__data__=n?n(null):{};this.size=0}const s=o;function l(t){var e=this.has(t)&&delete this.__data__[t];this.size-=e?1:0;return e}const c=l;var h="__lodash_hash_undefined__";var d=Object.prototype;var u=d.hasOwnProperty;function f(t){var e=this.__data__;if(n){var r=e[t];return r===h?undefined:r}return u.call(e,t)?e[t]:undefined}const p=f;var g=Object.prototype;var m=g.hasOwnProperty;function y(t){var e=this.__data__;return n?e[t]!==undefined:m.call(e,t)}const b=y;var x="__lodash_hash_undefined__";function C(t,e){var r=this.__data__;this.size+=this.has(t)?0:1;r[t]=n&&e===undefined?x:e;return this}const v=C;function k(t){var e=-1,r=t==null?0:t.length;this.clear();while(++e<r){var i=t[e];this.set(i[0],i[1])}}k.prototype.clear=s;k.prototype["delete"]=c;k.prototype.get=p;k.prototype.has=b;k.prototype.set=v;const w=k;var S=r(54951);var A=r(51482);function T(){this.size=0;this.__data__={hash:new w,map:new(A.A||S.A),string:new w}}const B=T;function L(t){var e=typeof t;return e=="string"||e=="number"||e=="symbol"||e=="boolean"?t!=="__proto__":t===null}const M=L;function _(t,e){var r=t.__data__;return M(e)?r[typeof e=="string"?"string":"hash"]:r.map}const F=_;function $(t){var e=F(this,t)["delete"](t);this.size-=e?1:0;return e}const E=$;function O(t){return F(this,t).get(t)}const D=O;function I(t){return F(this,t).has(t)}const K=I;function R(t,e){var r=F(this,t),i=r.size;r.set(t,e);this.size+=r.size==i?0:1;return this}const P=R;function z(t){var e=-1,r=t==null?0:t.length;this.clear();while(++e<r){var i=t[e];this.set(i[0],i[1])}}z.prototype.clear=B;z.prototype["delete"]=E;z.prototype.get=D;z.prototype.has=K;z.prototype.set=P;const q=z},88224:(t,e,r)=>{"use strict";r.d(e,{A:()=>o});var i=r(39023);var a=r(24606);var n=(0,i.A)(a.A,"Set");const o=n},28478:(t,e,r)=>{"use strict";r.d(e,{A:()=>b});var i=r(54951);function a(){this.__data__=new i.A;this.size=0}const n=a;function o(t){var e=this.__data__,r=e["delete"](t);this.size=e.size;return r}const s=o;function l(t){return this.__data__.get(t)}const c=l;function h(t){return this.__data__.has(t)}const d=h;var u=r(51482);var f=r(9883);var p=200;function g(t,e){var r=this.__data__;if(r instanceof i.A){var a=r.__data__;if(!u.A||a.length<p-1){a.push([t,e]);this.size=++r.size;return this}r=this.__data__=new f.A(a)}r.set(t,e);this.size=r.size;return this}const m=g;function y(t){var e=this.__data__=new i.A(t);this.size=e.size}y.prototype.clear=n;y.prototype["delete"]=s;y.prototype.get=c;y.prototype.has=d;y.prototype.set=m;const b=y},38066:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});var i=r(24606);var a=i.A.Symbol;const n=a},92615:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});var i=r(24606);var a=i.A.Uint8Array;const n=a},74578:(t,e,r)=>{"use strict";r.d(e,{A:()=>f});function i(t,e){var r=-1,i=Array(t);while(++r<t){i[r]=e(r)}return i}const a=i;var n=r(71528);var o=r(39990);var s=r(50895);var l=r(78912);var c=r(82818);var h=Object.prototype;var d=h.hasOwnProperty;function u(t,e){var r=(0,o.A)(t),i=!r&&(0,n.A)(t),h=!r&&!i&&(0,s.A)(t),u=!r&&!i&&!h&&(0,c.A)(t),f=r||i||h||u,p=f?a(t.length,String):[],g=p.length;for(var m in t){if((e||d.call(t,m))&&!(f&&(m=="length"||h&&(m=="offset"||m=="parent")||u&&(m=="buffer"||m=="byteLength"||m=="byteOffset")||(0,l.A)(m,g)))){p.push(m)}}return p}const f=u},16542:(t,e,r)=>{"use strict";r.d(e,{A:()=>l});var i=r(48657);var a=r(24461);var n=Object.prototype;var o=n.hasOwnProperty;function s(t,e,r){var n=t[e];if(!(o.call(t,e)&&(0,a.A)(n,r))||r===undefined&&!(e in t)){(0,i.A)(t,e,r)}}const l=s},48657:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});var i=r(51348);function a(t,e,r){if(e=="__proto__"&&i.A){(0,i.A)(t,e,{configurable:true,enumerable:true,value:r,writable:true})}else{t[e]=r}}const n=a},40283:(t,e,r)=>{"use strict";r.d(e,{A:()=>o});function i(t){return function(e,r,i){var a=-1,n=Object(e),o=i(e),s=o.length;while(s--){var l=o[t?s:++a];if(r(n[l],l,n)===false){break}}return e}}const a=i;var n=a();const o=n},64128:(t,e,r)=>{"use strict";r.d(e,{A:()=>b});var i=r(38066);var a=Object.prototype;var n=a.hasOwnProperty;var o=a.toString;var s=i.A?i.A.toStringTag:undefined;function l(t){var e=n.call(t,s),r=t[s];try{t[s]=undefined;var i=true}catch(l){}var a=o.call(t);if(i){if(e){t[s]=r}else{delete t[s]}}return a}const c=l;var h=Object.prototype;var d=h.toString;function u(t){return d.call(t)}const f=u;var p="[object Null]",g="[object Undefined]";var m=i.A?i.A.toStringTag:undefined;function y(t){if(t==null){return t===undefined?g:p}return m&&m in Object(t)?c(t):f(t)}const b=y},30568:(t,e,r)=>{"use strict";r.d(e,{A:()=>h});var i=r(690);var a=r(24630);var n=(0,a.A)(Object.keys,Object);const o=n;var s=Object.prototype;var l=s.hasOwnProperty;function c(t){if(!(0,i.A)(t)){return o(t)}var e=[];for(var r in Object(t)){if(l.call(t,r)&&r!="constructor"){e.push(r)}}return e}const h=c},55881:(t,e,r)=>{"use strict";r.d(e,{A:()=>s});var i=r(63077);var a=r(27401);var n=r(4596);function o(t,e){return(0,n.A)((0,a.A)(t,e,i.A),t+"")}const s=o},26132:(t,e,r)=>{"use strict";r.d(e,{A:()=>a});function i(t){return function(e){return t(e)}}const a=i},53458:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});var i=r(92615);function a(t){var e=new t.constructor(t.byteLength);new i.A(e).set(new i.A(t));return e}const n=a},65963:(t,e,r)=>{"use strict";r.d(e,{A:()=>h});var i=r(24606);t=r.hmd(t);var a=typeof exports=="object"&&exports&&!exports.nodeType&&exports;var n=a&&"object"=="object"&&t&&!t.nodeType&&t;var o=n&&n.exports===a;var s=o?i.A.Buffer:undefined,l=s?s.allocUnsafe:undefined;function c(t,e){if(e){return t.slice()}var r=t.length,i=l?l(r):new t.constructor(r);t.copy(i);return i}const h=c},93672:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});var i=r(53458);function a(t,e){var r=e?(0,i.A)(t.buffer):t.buffer;return new t.constructor(r,t.byteOffset,t.length)}const n=a},91810:(t,e,r)=>{"use strict";r.d(e,{A:()=>a});function i(t,e){var r=-1,i=t.length;e||(e=Array(i));while(++r<i){e[r]=t[r]}return e}const a=i},376:(t,e,r)=>{"use strict";r.d(e,{A:()=>o});var i=r(16542);var a=r(48657);function n(t,e,r,n){var o=!r;r||(r={});var s=-1,l=e.length;while(++s<l){var c=e[s];var h=n?n(r[c],t[c],c,r,t):undefined;if(h===undefined){h=t[c]}if(o){(0,a.A)(r,c,h)}else{(0,i.A)(r,c,h)}}return r}const o=n},56280:(t,e,r)=>{"use strict";r.d(e,{A:()=>o});var i=r(55881);var a=r(31943);function n(t){return(0,i.A)((function(e,r){var i=-1,n=r.length,o=n>1?r[n-1]:undefined,s=n>2?r[2]:undefined;o=t.length>3&&typeof o=="function"?(n--,o):undefined;if(s&&(0,a.A)(r[0],r[1],s)){o=n<3?undefined:o;n=1}e=Object(e);while(++i<n){var l=r[i];if(l){t(e,l,i,o)}}return e}))}const o=n},51348:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});var i=r(39023);var a=function(){try{var t=(0,i.A)(Object,"defineProperty");t({},"",{});return t}catch(e){}}();const n=a},7767:(t,e,r)=>{"use strict";r.d(e,{A:()=>a});var i=typeof r.g=="object"&&r.g&&r.g.Object===Object&&r.g;const a=i},39023:(t,e,r)=>{"use strict";r.d(e,{A:()=>S});var i=r(58807);var a=r(24606);var n=a.A["__core-js_shared__"];const o=n;var s=function(){var t=/[^.]+$/.exec(o&&o.keys&&o.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""}();function l(t){return!!s&&s in t}const c=l;var h=r(85356);var d=r(62210);var u=/[\\^$.*+?()[\]{}|]/g;var f=/^\[object .+?Constructor\]$/;var p=Function.prototype,g=Object.prototype;var m=p.toString;var y=g.hasOwnProperty;var b=RegExp("^"+m.call(y).replace(u,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function x(t){if(!(0,h.A)(t)||c(t)){return false}var e=(0,i.A)(t)?b:f;return e.test((0,d.A)(t))}const C=x;function v(t,e){return t==null?undefined:t[e]}const k=v;function w(t,e){var r=k(t,e);return C(r)?r:undefined}const S=w},86848:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});var i=r(24630);var a=(0,i.A)(Object.getPrototypeOf,Object);const n=a},88753:(t,e,r)=>{"use strict";r.d(e,{A:()=>B});var i=r(39023);var a=r(24606);var n=(0,i.A)(a.A,"DataView");const o=n;var s=r(51482);var l=(0,i.A)(a.A,"Promise");const c=l;var h=r(88224);var d=(0,i.A)(a.A,"WeakMap");const u=d;var f=r(64128);var p=r(62210);var g="[object Map]",m="[object Object]",y="[object Promise]",b="[object Set]",x="[object WeakMap]";var C="[object DataView]";var v=(0,p.A)(o),k=(0,p.A)(s.A),w=(0,p.A)(c),S=(0,p.A)(h.A),A=(0,p.A)(u);var T=f.A;if(o&&T(new o(new ArrayBuffer(1)))!=C||s.A&&T(new s.A)!=g||c&&T(c.resolve())!=y||h.A&&T(new h.A)!=b||u&&T(new u)!=x){T=function(t){var e=(0,f.A)(t),r=e==m?t.constructor:undefined,i=r?(0,p.A)(r):"";if(i){switch(i){case v:return C;case k:return g;case w:return y;case S:return b;case A:return x}}return e}}const B=T},92768:(t,e,r)=>{"use strict";r.d(e,{A:()=>h});var i=r(85356);var a=Object.create;var n=function(){function t(){}return function(e){if(!(0,i.A)(e)){return{}}if(a){return a(e)}t.prototype=e;var r=new t;t.prototype=undefined;return r}}();const o=n;var s=r(86848);var l=r(690);function c(t){return typeof t.constructor=="function"&&!(0,l.A)(t)?o((0,s.A)(t)):{}}const h=c},78912:(t,e,r)=>{"use strict";r.d(e,{A:()=>o});var i=9007199254740991;var a=/^(?:0|[1-9]\d*)$/;function n(t,e){var r=typeof t;e=e==null?i:e;return!!e&&(r=="number"||r!="symbol"&&a.test(t))&&(t>-1&&t%1==0&&t<e)}const o=n},31943:(t,e,r)=>{"use strict";r.d(e,{A:()=>l});var i=r(24461);var a=r(21585);var n=r(78912);var o=r(85356);function s(t,e,r){if(!(0,o.A)(r)){return false}var s=typeof e;if(s=="number"?(0,a.A)(r)&&(0,n.A)(e,r.length):s=="string"&&e in r){return(0,i.A)(r[e],t)}return false}const l=s},690:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});var i=Object.prototype;function a(t){var e=t&&t.constructor,r=typeof e=="function"&&e.prototype||i;return t===r}const n=a},89986:(t,e,r)=>{"use strict";r.d(e,{A:()=>c});var i=r(7767);t=r.hmd(t);var a=typeof exports=="object"&&exports&&!exports.nodeType&&exports;var n=a&&"object"=="object"&&t&&!t.nodeType&&t;var o=n&&n.exports===a;var s=o&&i.A.process;var l=function(){try{var t=n&&n.require&&n.require("util").types;if(t){return t}return s&&s.binding&&s.binding("util")}catch(e){}}();const c=l},24630:(t,e,r)=>{"use strict";r.d(e,{A:()=>a});function i(t,e){return function(r){return t(e(r))}}const a=i},27401:(t,e,r)=>{"use strict";r.d(e,{A:()=>s});function i(t,e,r){switch(r.length){case 0:return t.call(e);case 1:return t.call(e,r[0]);case 2:return t.call(e,r[0],r[1]);case 3:return t.call(e,r[0],r[1],r[2])}return t.apply(e,r)}const a=i;var n=Math.max;function o(t,e,r){e=n(e===undefined?t.length-1:e,0);return function(){var i=arguments,o=-1,s=n(i.length-e,0),l=Array(s);while(++o<s){l[o]=i[e+o]}o=-1;var c=Array(e+1);while(++o<e){c[o]=i[o]}c[e]=r(l);return a(t,this,c)}}const s=o},24606:(t,e,r)=>{"use strict";r.d(e,{A:()=>o});var i=r(7767);var a=typeof self=="object"&&self&&self.Object===Object&&self;var n=i.A||a||Function("return this")();const o=n},4596:(t,e,r)=>{"use strict";r.d(e,{A:()=>p});var i=r(33659);var a=r(51348);var n=r(63077);var o=!a.A?n.A:function(t,e){return(0,a.A)(t,"toString",{configurable:true,enumerable:false,value:(0,i.A)(e),writable:true})};const s=o;var l=800,c=16;var h=Date.now;function d(t){var e=0,r=0;return function(){var i=h(),a=c-(i-r);r=i;if(a>0){if(++e>=l){return arguments[0]}}else{e=0}return t.apply(undefined,arguments)}}const u=d;var f=u(s);const p=f},62210:(t,e,r)=>{"use strict";r.d(e,{A:()=>o});var i=Function.prototype;var a=i.toString;function n(t){if(t!=null){try{return a.call(t)}catch(e){}try{return t+""}catch(e){}}return""}const o=n},33659:(t,e,r)=>{"use strict";r.d(e,{A:()=>a});function i(t){return function(){return t}}const a=i},24461:(t,e,r)=>{"use strict";r.d(e,{A:()=>a});function i(t,e){return t===e||t!==t&&e!==e}const a=i},63077:(t,e,r)=>{"use strict";r.d(e,{A:()=>a});function i(t){return t}const a=i},71528:(t,e,r)=>{"use strict";r.d(e,{A:()=>u});var i=r(64128);var a=r(53315);var n="[object Arguments]";function o(t){return(0,a.A)(t)&&(0,i.A)(t)==n}const s=o;var l=Object.prototype;var c=l.hasOwnProperty;var h=l.propertyIsEnumerable;var d=s(function(){return arguments}())?s:function(t){return(0,a.A)(t)&&c.call(t,"callee")&&!h.call(t,"callee")};const u=d},39990:(t,e,r)=>{"use strict";r.d(e,{A:()=>a});var i=Array.isArray;const a=i},21585:(t,e,r)=>{"use strict";r.d(e,{A:()=>o});var i=r(58807);var a=r(43627);function n(t){return t!=null&&(0,a.A)(t.length)&&!(0,i.A)(t)}const o=n},10654:(t,e,r)=>{"use strict";r.d(e,{A:()=>o});var i=r(21585);var a=r(53315);function n(t){return(0,a.A)(t)&&(0,i.A)(t)}const o=n},50895:(t,e,r)=>{"use strict";r.d(e,{A:()=>u});var i=r(24606);function a(){return false}const n=a;t=r.hmd(t);var o=typeof exports=="object"&&exports&&!exports.nodeType&&exports;var s=o&&"object"=="object"&&t&&!t.nodeType&&t;var l=s&&s.exports===o;var c=l?i.A.Buffer:undefined;var h=c?c.isBuffer:undefined;var d=h||n;const u=d},74650:(t,e,r)=>{"use strict";r.d(e,{A:()=>m});var i=r(30568);var a=r(88753);var n=r(71528);var o=r(39990);var s=r(21585);var l=r(50895);var c=r(690);var h=r(82818);var d="[object Map]",u="[object Set]";var f=Object.prototype;var p=f.hasOwnProperty;function g(t){if(t==null){return true}if((0,s.A)(t)&&((0,o.A)(t)||typeof t=="string"||typeof t.splice=="function"||(0,l.A)(t)||(0,h.A)(t)||(0,n.A)(t))){return!t.length}var e=(0,a.A)(t);if(e==d||e==u){return!t.size}if((0,c.A)(t)){return!(0,i.A)(t).length}for(var r in t){if(p.call(t,r)){return false}}return true}const m=g},58807:(t,e,r)=>{"use strict";r.d(e,{A:()=>h});var i=r(64128);var a=r(85356);var n="[object AsyncFunction]",o="[object Function]",s="[object GeneratorFunction]",l="[object Proxy]";function c(t){if(!(0,a.A)(t)){return false}var e=(0,i.A)(t);return e==o||e==s||e==n||e==l}const h=c},43627:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});var i=9007199254740991;function a(t){return typeof t=="number"&&t>-1&&t%1==0&&t<=i}const n=a},85356:(t,e,r)=>{"use strict";r.d(e,{A:()=>a});function i(t){var e=typeof t;return t!=null&&(e=="object"||e=="function")}const a=i},53315:(t,e,r)=>{"use strict";r.d(e,{A:()=>a});function i(t){return t!=null&&typeof t=="object"}const a=i},82818:(t,e,r)=>{"use strict";r.d(e,{A:()=>K});var i=r(64128);var a=r(43627);var n=r(53315);var o="[object Arguments]",s="[object Array]",l="[object Boolean]",c="[object Date]",h="[object Error]",d="[object Function]",u="[object Map]",f="[object Number]",p="[object Object]",g="[object RegExp]",m="[object Set]",y="[object String]",b="[object WeakMap]";var x="[object ArrayBuffer]",C="[object DataView]",v="[object Float32Array]",k="[object Float64Array]",w="[object Int8Array]",S="[object Int16Array]",A="[object Int32Array]",T="[object Uint8Array]",B="[object Uint8ClampedArray]",L="[object Uint16Array]",M="[object Uint32Array]";var _={};_[v]=_[k]=_[w]=_[S]=_[A]=_[T]=_[B]=_[L]=_[M]=true;_[o]=_[s]=_[x]=_[l]=_[C]=_[c]=_[h]=_[d]=_[u]=_[f]=_[p]=_[g]=_[m]=_[y]=_[b]=false;function F(t){return(0,n.A)(t)&&(0,a.A)(t.length)&&!!_[(0,i.A)(t)]}const $=F;var E=r(26132);var O=r(89986);var D=O.A&&O.A.isTypedArray;var I=D?(0,E.A)(D):$;const K=I},13839:(t,e,r)=>{"use strict";r.d(e,{A:()=>p});var i=r(74578);var a=r(85356);var n=r(690);function o(t){var e=[];if(t!=null){for(var r in Object(t)){e.push(r)}}return e}const s=o;var l=Object.prototype;var c=l.hasOwnProperty;function h(t){if(!(0,a.A)(t)){return s(t)}var e=(0,n.A)(t),r=[];for(var i in t){if(!(i=="constructor"&&(e||!c.call(t,i)))){r.push(i)}}return r}const d=h;var u=r(21585);function f(t){return(0,u.A)(t)?(0,i.A)(t,true):d(t)}const p=f},307:(t,e,r)=>{"use strict";r.d(e,{A:()=>o});var i=r(9883);var a="Expected a function";function n(t,e){if(typeof t!="function"||e!=null&&typeof e!="function"){throw new TypeError(a)}var r=function(){var i=arguments,a=e?e.apply(this,i):i[0],n=r.cache;if(n.has(a)){return n.get(a)}var o=t.apply(this,i);r.cache=n.set(a,o)||n;return o};r.cache=new(n.Cache||i.A);return r}n.Cache=i.A;const o=n},96901:(t,e,r)=>{"use strict";r.d(e,{A:()=>W});var i=r(28478);var a=r(48657);var n=r(24461);function o(t,e,r){if(r!==undefined&&!(0,n.A)(t[e],r)||r===undefined&&!(e in t)){(0,a.A)(t,e,r)}}const s=o;var l=r(40283);var c=r(65963);var h=r(93672);var d=r(91810);var u=r(92768);var f=r(71528);var p=r(39990);var g=r(10654);var m=r(50895);var y=r(58807);var b=r(85356);var x=r(64128);var C=r(86848);var v=r(53315);var k="[object Object]";var w=Function.prototype,S=Object.prototype;var A=w.toString;var T=S.hasOwnProperty;var B=A.call(Object);function L(t){if(!(0,v.A)(t)||(0,x.A)(t)!=k){return false}var e=(0,C.A)(t);if(e===null){return true}var r=T.call(e,"constructor")&&e.constructor;return typeof r=="function"&&r instanceof r&&A.call(r)==B}const M=L;var _=r(82818);function F(t,e){if(e==="constructor"&&typeof t[e]==="function"){return}if(e=="__proto__"){return}return t[e]}const $=F;var E=r(376);var O=r(13839);function D(t){return(0,E.A)(t,(0,O.A)(t))}const I=D;function K(t,e,r,i,a,n,o){var l=$(t,r),x=$(e,r),C=o.get(x);if(C){s(t,r,C);return}var v=n?n(l,x,r+"",t,e,o):undefined;var k=v===undefined;if(k){var w=(0,p.A)(x),S=!w&&(0,m.A)(x),A=!w&&!S&&(0,_.A)(x);v=x;if(w||S||A){if((0,p.A)(l)){v=l}else if((0,g.A)(l)){v=(0,d.A)(l)}else if(S){k=false;v=(0,c.A)(x,true)}else if(A){k=false;v=(0,h.A)(x,true)}else{v=[]}}else if(M(x)||(0,f.A)(x)){v=l;if((0,f.A)(l)){v=I(l)}else if(!(0,b.A)(l)||(0,y.A)(l)){v=(0,u.A)(x)}}else{k=false}}if(k){o.set(x,v);a(v,x,i,n,o);o["delete"](x)}s(t,r,v)}const R=K;function P(t,e,r,a,n){if(t===e){return}(0,l.A)(e,(function(o,l){n||(n=new i.A);if((0,b.A)(o)){R(t,e,l,r,P,a,n)}else{var c=a?a($(t,l),o,l+"",t,e,n):undefined;if(c===undefined){c=o}s(t,l,c)}}),O.A)}const z=P;var q=r(56280);var N=(0,q.A)((function(t,e,r){z(t,e,r)}));const W=N},59357:(t,e,r)=>{"use strict";r.d(e,{n:()=>i});var i={name:"mermaid",version:"11.6.0",description:"Markdown-ish syntax for generating flowcharts, mindmaps, sequence diagrams, class diagrams, gantt charts, git graphs and more.",type:"module",module:"./dist/mermaid.core.mjs",types:"./dist/mermaid.d.ts",exports:{".":{types:"./dist/mermaid.d.ts",import:"./dist/mermaid.core.mjs",default:"./dist/mermaid.core.mjs"},"./*":"./*"},keywords:["diagram","markdown","flowchart","sequence diagram","gantt","class diagram","git graph","mindmap","packet diagram","c4 diagram","er diagram","pie chart","pie diagram","quadrant chart","requirement diagram","graph"],scripts:{clean:"rimraf dist",dev:"pnpm -w dev","docs:code":"typedoc src/defaultConfig.ts src/config.ts src/mermaid.ts && prettier --write ./src/docs/config/setup","docs:build":"rimraf ../../docs && pnpm docs:code && pnpm docs:spellcheck && tsx scripts/docs.cli.mts","docs:verify":"pnpm docs:code && pnpm docs:spellcheck && tsx scripts/docs.cli.mts --verify","docs:pre:vitepress":"pnpm --filter ./src/docs prefetch && rimraf src/vitepress && pnpm docs:code && tsx scripts/docs.cli.mts --vitepress && pnpm --filter ./src/vitepress install --no-frozen-lockfile --ignore-scripts","docs:build:vitepress":"pnpm docs:pre:vitepress && (cd src/vitepress && pnpm run build) && cpy --flat src/docs/landing/ ./src/vitepress/.vitepress/dist/landing","docs:dev":'pnpm docs:pre:vitepress && concurrently "pnpm --filter ./src/vitepress dev" "tsx scripts/docs.cli.mts --watch --vitepress"',"docs:dev:docker":'pnpm docs:pre:vitepress && concurrently "pnpm --filter ./src/vitepress dev:docker" "tsx scripts/docs.cli.mts --watch --vitepress"',"docs:serve":"pnpm docs:build:vitepress && vitepress serve src/vitepress","docs:spellcheck":'cspell "src/docs/**/*.md"',"docs:release-version":"tsx scripts/update-release-version.mts","docs:verify-version":"tsx scripts/update-release-version.mts --verify","types:build-config":"tsx scripts/create-types-from-json-schema.mts","types:verify-config":"tsx scripts/create-types-from-json-schema.mts --verify",checkCircle:"npx madge --circular ./src",prepublishOnly:"pnpm docs:verify-version"},repository:{type:"git",url:"https://github.com/mermaid-js/mermaid"},author:"Knut Sveidqvist",license:"MIT",standard:{ignore:["**/parser/*.js","dist/**/*.js","cypress/**/*.js"],globals:["page"]},dependencies:{"@braintree/sanitize-url":"^7.0.4","@iconify/utils":"^2.1.33","@mermaid-js/parser":"workspace:^","@types/d3":"^7.4.3",cytoscape:"^3.29.3","cytoscape-cose-bilkent":"^4.1.0","cytoscape-fcose":"^2.2.0",d3:"^7.9.0","d3-sankey":"^0.12.3","dagre-d3-es":"7.0.11",dayjs:"^1.11.13",dompurify:"^3.2.4",katex:"^0.16.9",khroma:"^2.1.0","lodash-es":"^4.17.21",marked:"^15.0.7",roughjs:"^4.6.6",stylis:"^4.3.6","ts-dedent":"^2.2.0",uuid:"^11.1.0"},devDependencies:{"@adobe/jsonschema2md":"^8.0.2","@iconify/types":"^2.0.0","@types/cytoscape":"^3.21.9","@types/cytoscape-fcose":"^2.2.4","@types/d3-sankey":"^0.12.4","@types/d3-scale":"^4.0.9","@types/d3-scale-chromatic":"^3.1.0","@types/d3-selection":"^3.0.11","@types/d3-shape":"^3.1.7","@types/jsdom":"^21.1.7","@types/katex":"^0.16.7","@types/lodash-es":"^4.17.12","@types/micromatch":"^4.0.9","@types/stylis":"^4.2.7","@types/uuid":"^10.0.0",ajv:"^8.17.1",chokidar:"^4.0.3",concurrently:"^9.1.2","csstree-validator":"^4.0.1",globby:"^14.0.2",jison:"^0.4.18","js-base64":"^3.7.7",jsdom:"^26.0.0","json-schema-to-typescript":"^15.0.4",micromatch:"^4.0.8","path-browserify":"^1.0.1",prettier:"^3.5.2",remark:"^15.0.1","remark-frontmatter":"^5.0.0","remark-gfm":"^4.0.1",rimraf:"^6.0.1","start-server-and-test":"^2.0.10","type-fest":"^4.35.0",typedoc:"^0.27.8","typedoc-plugin-markdown":"^4.4.2",typescript:"~5.7.3","unist-util-flatmap":"^1.0.0","unist-util-visit":"^5.0.0",vitepress:"^1.0.2","vitepress-plugin-search":"1.0.4-alpha.22"},files:["dist/","README.md"],publishConfig:{access:"public"}}},97366:(t,e,r)=>{"use strict";r.d(e,{H:()=>ti,r:()=>Qr});var i=r(75905);function a(t){return typeof t==="undefined"||t===null}(0,i.K2)(a,"isNothing");function n(t){return typeof t==="object"&&t!==null}(0,i.K2)(n,"isObject");function o(t){if(Array.isArray(t))return t;else if(a(t))return[];return[t]}(0,i.K2)(o,"toArray");function s(t,e){var r,i,a,n;if(e){n=Object.keys(e);for(r=0,i=n.length;r<i;r+=1){a=n[r];t[a]=e[a]}}return t}(0,i.K2)(s,"extend");function l(t,e){var r="",i;for(i=0;i<e;i+=1){r+=t}return r}(0,i.K2)(l,"repeat");function c(t){return t===0&&Number.NEGATIVE_INFINITY===1/t}(0,i.K2)(c,"isNegativeZero");var h=a;var d=n;var u=o;var f=l;var p=c;var g=s;var m={isNothing:h,isObject:d,toArray:u,repeat:f,isNegativeZero:p,extend:g};function y(t,e){var r="",i=t.reason||"(unknown reason)";if(!t.mark)return i;if(t.mark.name){r+='in "'+t.mark.name+'" '}r+="("+(t.mark.line+1)+":"+(t.mark.column+1)+")";if(!e&&t.mark.snippet){r+="\n\n"+t.mark.snippet}return i+" "+r}(0,i.K2)(y,"formatError");function b(t,e){Error.call(this);this.name="YAMLException";this.reason=t;this.mark=e;this.message=y(this,false);if(Error.captureStackTrace){Error.captureStackTrace(this,this.constructor)}else{this.stack=(new Error).stack||""}}(0,i.K2)(b,"YAMLException$1");b.prototype=Object.create(Error.prototype);b.prototype.constructor=b;b.prototype.toString=(0,i.K2)((function t(e){return this.name+": "+y(this,e)}),"toString");var x=b;function C(t,e,r,i,a){var n="";var o="";var s=Math.floor(a/2)-1;if(i-e>s){n=" ... ";e=i-s+n.length}if(r-i>s){o=" ...";r=i+s-o.length}return{str:n+t.slice(e,r).replace(/\t/g,"→")+o,pos:i-e+n.length}}(0,i.K2)(C,"getLine");function v(t,e){return m.repeat(" ",e-t.length)+t}(0,i.K2)(v,"padStart");function k(t,e){e=Object.create(e||null);if(!t.buffer)return null;if(!e.maxLength)e.maxLength=79;if(typeof e.indent!=="number")e.indent=1;if(typeof e.linesBefore!=="number")e.linesBefore=3;if(typeof e.linesAfter!=="number")e.linesAfter=2;var r=/\r?\n|\r|\0/g;var i=[0];var a=[];var n;var o=-1;while(n=r.exec(t.buffer)){a.push(n.index);i.push(n.index+n[0].length);if(t.position<=n.index&&o<0){o=i.length-2}}if(o<0)o=i.length-1;var s="",l,c;var h=Math.min(t.line+e.linesAfter,a.length).toString().length;var d=e.maxLength-(e.indent+h+3);for(l=1;l<=e.linesBefore;l++){if(o-l<0)break;c=C(t.buffer,i[o-l],a[o-l],t.position-(i[o]-i[o-l]),d);s=m.repeat(" ",e.indent)+v((t.line-l+1).toString(),h)+" | "+c.str+"\n"+s}c=C(t.buffer,i[o],a[o],t.position,d);s+=m.repeat(" ",e.indent)+v((t.line+1).toString(),h)+" | "+c.str+"\n";s+=m.repeat("-",e.indent+h+3+c.pos)+"^\n";for(l=1;l<=e.linesAfter;l++){if(o+l>=a.length)break;c=C(t.buffer,i[o+l],a[o+l],t.position-(i[o]-i[o+l]),d);s+=m.repeat(" ",e.indent)+v((t.line+l+1).toString(),h)+" | "+c.str+"\n"}return s.replace(/\n$/,"")}(0,i.K2)(k,"makeSnippet");var w=k;var S=["kind","multi","resolve","construct","instanceOf","predicate","represent","representName","defaultStyle","styleAliases"];var A=["scalar","sequence","mapping"];function T(t){var e={};if(t!==null){Object.keys(t).forEach((function(r){t[r].forEach((function(t){e[String(t)]=r}))}))}return e}(0,i.K2)(T,"compileStyleAliases");function B(t,e){e=e||{};Object.keys(e).forEach((function(e){if(S.indexOf(e)===-1){throw new x('Unknown option "'+e+'" is met in definition of "'+t+'" YAML type.')}}));this.options=e;this.tag=t;this.kind=e["kind"]||null;this.resolve=e["resolve"]||function(){return true};this.construct=e["construct"]||function(t){return t};this.instanceOf=e["instanceOf"]||null;this.predicate=e["predicate"]||null;this.represent=e["represent"]||null;this.representName=e["representName"]||null;this.defaultStyle=e["defaultStyle"]||null;this.multi=e["multi"]||false;this.styleAliases=T(e["styleAliases"]||null);if(A.indexOf(this.kind)===-1){throw new x('Unknown kind "'+this.kind+'" is specified for "'+t+'" YAML type.')}}(0,i.K2)(B,"Type$1");var L=B;function M(t,e){var r=[];t[e].forEach((function(t){var e=r.length;r.forEach((function(r,i){if(r.tag===t.tag&&r.kind===t.kind&&r.multi===t.multi){e=i}}));r[e]=t}));return r}(0,i.K2)(M,"compileList");function _(){var t={scalar:{},sequence:{},mapping:{},fallback:{},multi:{scalar:[],sequence:[],mapping:[],fallback:[]}},e,r;function a(e){if(e.multi){t.multi[e.kind].push(e);t.multi["fallback"].push(e)}else{t[e.kind][e.tag]=t["fallback"][e.tag]=e}}(0,i.K2)(a,"collectType");for(e=0,r=arguments.length;e<r;e+=1){arguments[e].forEach(a)}return t}(0,i.K2)(_,"compileMap");function F(t){return this.extend(t)}(0,i.K2)(F,"Schema$1");F.prototype.extend=(0,i.K2)((function t(e){var r=[];var i=[];if(e instanceof L){i.push(e)}else if(Array.isArray(e)){i=i.concat(e)}else if(e&&(Array.isArray(e.implicit)||Array.isArray(e.explicit))){if(e.implicit)r=r.concat(e.implicit);if(e.explicit)i=i.concat(e.explicit)}else{throw new x("Schema.extend argument should be a Type, [ Type ], or a schema definition ({ implicit: [...], explicit: [...] })")}r.forEach((function(t){if(!(t instanceof L)){throw new x("Specified list of YAML types (or a single Type object) contains a non-Type object.")}if(t.loadKind&&t.loadKind!=="scalar"){throw new x("There is a non-scalar type in the implicit list of a schema. Implicit resolving of such types is not supported.")}if(t.multi){throw new x("There is a multi type in the implicit list of a schema. Multi tags can only be listed as explicit.")}}));i.forEach((function(t){if(!(t instanceof L)){throw new x("Specified list of YAML types (or a single Type object) contains a non-Type object.")}}));var a=Object.create(F.prototype);a.implicit=(this.implicit||[]).concat(r);a.explicit=(this.explicit||[]).concat(i);a.compiledImplicit=M(a,"implicit");a.compiledExplicit=M(a,"explicit");a.compiledTypeMap=_(a.compiledImplicit,a.compiledExplicit);return a}),"extend");var $=F;var E=new L("tag:yaml.org,2002:str",{kind:"scalar",construct:(0,i.K2)((function(t){return t!==null?t:""}),"construct")});var O=new L("tag:yaml.org,2002:seq",{kind:"sequence",construct:(0,i.K2)((function(t){return t!==null?t:[]}),"construct")});var D=new L("tag:yaml.org,2002:map",{kind:"mapping",construct:(0,i.K2)((function(t){return t!==null?t:{}}),"construct")});var I=new $({explicit:[E,O,D]});function K(t){if(t===null)return true;var e=t.length;return e===1&&t==="~"||e===4&&(t==="null"||t==="Null"||t==="NULL")}(0,i.K2)(K,"resolveYamlNull");function R(){return null}(0,i.K2)(R,"constructYamlNull");function P(t){return t===null}(0,i.K2)(P,"isNull");var z=new L("tag:yaml.org,2002:null",{kind:"scalar",resolve:K,construct:R,predicate:P,represent:{canonical:(0,i.K2)((function(){return"~"}),"canonical"),lowercase:(0,i.K2)((function(){return"null"}),"lowercase"),uppercase:(0,i.K2)((function(){return"NULL"}),"uppercase"),camelcase:(0,i.K2)((function(){return"Null"}),"camelcase"),empty:(0,i.K2)((function(){return""}),"empty")},defaultStyle:"lowercase"});function q(t){if(t===null)return false;var e=t.length;return e===4&&(t==="true"||t==="True"||t==="TRUE")||e===5&&(t==="false"||t==="False"||t==="FALSE")}(0,i.K2)(q,"resolveYamlBoolean");function N(t){return t==="true"||t==="True"||t==="TRUE"}(0,i.K2)(N,"constructYamlBoolean");function W(t){return Object.prototype.toString.call(t)==="[object Boolean]"}(0,i.K2)(W,"isBoolean");var j=new L("tag:yaml.org,2002:bool",{kind:"scalar",resolve:q,construct:N,predicate:W,represent:{lowercase:(0,i.K2)((function(t){return t?"true":"false"}),"lowercase"),uppercase:(0,i.K2)((function(t){return t?"TRUE":"FALSE"}),"uppercase"),camelcase:(0,i.K2)((function(t){return t?"True":"False"}),"camelcase")},defaultStyle:"lowercase"});function H(t){return 48<=t&&t<=57||65<=t&&t<=70||97<=t&&t<=102}(0,i.K2)(H,"isHexCode");function Y(t){return 48<=t&&t<=55}(0,i.K2)(Y,"isOctCode");function U(t){return 48<=t&&t<=57}(0,i.K2)(U,"isDecCode");function G(t){if(t===null)return false;var e=t.length,r=0,i=false,a;if(!e)return false;a=t[r];if(a==="-"||a==="+"){a=t[++r]}if(a==="0"){if(r+1===e)return true;a=t[++r];if(a==="b"){r++;for(;r<e;r++){a=t[r];if(a==="_")continue;if(a!=="0"&&a!=="1")return false;i=true}return i&&a!=="_"}if(a==="x"){r++;for(;r<e;r++){a=t[r];if(a==="_")continue;if(!H(t.charCodeAt(r)))return false;i=true}return i&&a!=="_"}if(a==="o"){r++;for(;r<e;r++){a=t[r];if(a==="_")continue;if(!Y(t.charCodeAt(r)))return false;i=true}return i&&a!=="_"}}if(a==="_")return false;for(;r<e;r++){a=t[r];if(a==="_")continue;if(!U(t.charCodeAt(r))){return false}i=true}if(!i||a==="_")return false;return true}(0,i.K2)(G,"resolveYamlInteger");function V(t){var e=t,r=1,i;if(e.indexOf("_")!==-1){e=e.replace(/_/g,"")}i=e[0];if(i==="-"||i==="+"){if(i==="-")r=-1;e=e.slice(1);i=e[0]}if(e==="0")return 0;if(i==="0"){if(e[1]==="b")return r*parseInt(e.slice(2),2);if(e[1]==="x")return r*parseInt(e.slice(2),16);if(e[1]==="o")return r*parseInt(e.slice(2),8)}return r*parseInt(e,10)}(0,i.K2)(V,"constructYamlInteger");function X(t){return Object.prototype.toString.call(t)==="[object Number]"&&(t%1===0&&!m.isNegativeZero(t))}(0,i.K2)(X,"isInteger");var Z=new L("tag:yaml.org,2002:int",{kind:"scalar",resolve:G,construct:V,predicate:X,represent:{binary:(0,i.K2)((function(t){return t>=0?"0b"+t.toString(2):"-0b"+t.toString(2).slice(1)}),"binary"),octal:(0,i.K2)((function(t){return t>=0?"0o"+t.toString(8):"-0o"+t.toString(8).slice(1)}),"octal"),decimal:(0,i.K2)((function(t){return t.toString(10)}),"decimal"),hexadecimal:(0,i.K2)((function(t){return t>=0?"0x"+t.toString(16).toUpperCase():"-0x"+t.toString(16).toUpperCase().slice(1)}),"hexadecimal")},defaultStyle:"decimal",styleAliases:{binary:[2,"bin"],octal:[8,"oct"],decimal:[10,"dec"],hexadecimal:[16,"hex"]}});var J=new RegExp("^(?:[-+]?(?:[0-9][0-9_]*)(?:\\.[0-9_]*)?(?:[eE][-+]?[0-9]+)?|\\.[0-9_]+(?:[eE][-+]?[0-9]+)?|[-+]?\\.(?:inf|Inf|INF)|\\.(?:nan|NaN|NAN))$");function Q(t){if(t===null)return false;if(!J.test(t)||t[t.length-1]==="_"){return false}return true}(0,i.K2)(Q,"resolveYamlFloat");function tt(t){var e,r;e=t.replace(/_/g,"").toLowerCase();r=e[0]==="-"?-1:1;if("+-".indexOf(e[0])>=0){e=e.slice(1)}if(e===".inf"){return r===1?Number.POSITIVE_INFINITY:Number.NEGATIVE_INFINITY}else if(e===".nan"){return NaN}return r*parseFloat(e,10)}(0,i.K2)(tt,"constructYamlFloat");var et=/^[-+]?[0-9]+e/;function rt(t,e){var r;if(isNaN(t)){switch(e){case"lowercase":return".nan";case"uppercase":return".NAN";case"camelcase":return".NaN"}}else if(Number.POSITIVE_INFINITY===t){switch(e){case"lowercase":return".inf";case"uppercase":return".INF";case"camelcase":return".Inf"}}else if(Number.NEGATIVE_INFINITY===t){switch(e){case"lowercase":return"-.inf";case"uppercase":return"-.INF";case"camelcase":return"-.Inf"}}else if(m.isNegativeZero(t)){return"-0.0"}r=t.toString(10);return et.test(r)?r.replace("e",".e"):r}(0,i.K2)(rt,"representYamlFloat");function it(t){return Object.prototype.toString.call(t)==="[object Number]"&&(t%1!==0||m.isNegativeZero(t))}(0,i.K2)(it,"isFloat");var at=new L("tag:yaml.org,2002:float",{kind:"scalar",resolve:Q,construct:tt,predicate:it,represent:rt,defaultStyle:"lowercase"});var nt=I.extend({implicit:[z,j,Z,at]});var ot=nt;var st=new RegExp("^([0-9][0-9][0-9][0-9])-([0-9][0-9])-([0-9][0-9])$");var lt=new RegExp("^([0-9][0-9][0-9][0-9])-([0-9][0-9]?)-([0-9][0-9]?)(?:[Tt]|[ \\t]+)([0-9][0-9]?):([0-9][0-9]):([0-9][0-9])(?:\\.([0-9]*))?(?:[ \\t]*(Z|([-+])([0-9][0-9]?)(?::([0-9][0-9]))?))?$");function ct(t){if(t===null)return false;if(st.exec(t)!==null)return true;if(lt.exec(t)!==null)return true;return false}(0,i.K2)(ct,"resolveYamlTimestamp");function ht(t){var e,r,i,a,n,o,s,l=0,c=null,h,d,u;e=st.exec(t);if(e===null)e=lt.exec(t);if(e===null)throw new Error("Date resolve error");r=+e[1];i=+e[2]-1;a=+e[3];if(!e[4]){return new Date(Date.UTC(r,i,a))}n=+e[4];o=+e[5];s=+e[6];if(e[7]){l=e[7].slice(0,3);while(l.length<3){l+="0"}l=+l}if(e[9]){h=+e[10];d=+(e[11]||0);c=(h*60+d)*6e4;if(e[9]==="-")c=-c}u=new Date(Date.UTC(r,i,a,n,o,s,l));if(c)u.setTime(u.getTime()-c);return u}(0,i.K2)(ht,"constructYamlTimestamp");function dt(t){return t.toISOString()}(0,i.K2)(dt,"representYamlTimestamp");var ut=new L("tag:yaml.org,2002:timestamp",{kind:"scalar",resolve:ct,construct:ht,instanceOf:Date,represent:dt});function ft(t){return t==="<<"||t===null}(0,i.K2)(ft,"resolveYamlMerge");var pt=new L("tag:yaml.org,2002:merge",{kind:"scalar",resolve:ft});var gt="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\n\r";function mt(t){if(t===null)return false;var e,r,i=0,a=t.length,n=gt;for(r=0;r<a;r++){e=n.indexOf(t.charAt(r));if(e>64)continue;if(e<0)return false;i+=6}return i%8===0}(0,i.K2)(mt,"resolveYamlBinary");function yt(t){var e,r,i=t.replace(/[\r\n=]/g,""),a=i.length,n=gt,o=0,s=[];for(e=0;e<a;e++){if(e%4===0&&e){s.push(o>>16&255);s.push(o>>8&255);s.push(o&255)}o=o<<6|n.indexOf(i.charAt(e))}r=a%4*6;if(r===0){s.push(o>>16&255);s.push(o>>8&255);s.push(o&255)}else if(r===18){s.push(o>>10&255);s.push(o>>2&255)}else if(r===12){s.push(o>>4&255)}return new Uint8Array(s)}(0,i.K2)(yt,"constructYamlBinary");function bt(t){var e="",r=0,i,a,n=t.length,o=gt;for(i=0;i<n;i++){if(i%3===0&&i){e+=o[r>>18&63];e+=o[r>>12&63];e+=o[r>>6&63];e+=o[r&63]}r=(r<<8)+t[i]}a=n%3;if(a===0){e+=o[r>>18&63];e+=o[r>>12&63];e+=o[r>>6&63];e+=o[r&63]}else if(a===2){e+=o[r>>10&63];e+=o[r>>4&63];e+=o[r<<2&63];e+=o[64]}else if(a===1){e+=o[r>>2&63];e+=o[r<<4&63];e+=o[64];e+=o[64]}return e}(0,i.K2)(bt,"representYamlBinary");function xt(t){return Object.prototype.toString.call(t)==="[object Uint8Array]"}(0,i.K2)(xt,"isBinary");var Ct=new L("tag:yaml.org,2002:binary",{kind:"scalar",resolve:mt,construct:yt,predicate:xt,represent:bt});var vt=Object.prototype.hasOwnProperty;var kt=Object.prototype.toString;function wt(t){if(t===null)return true;var e=[],r,i,a,n,o,s=t;for(r=0,i=s.length;r<i;r+=1){a=s[r];o=false;if(kt.call(a)!=="[object Object]")return false;for(n in a){if(vt.call(a,n)){if(!o)o=true;else return false}}if(!o)return false;if(e.indexOf(n)===-1)e.push(n);else return false}return true}(0,i.K2)(wt,"resolveYamlOmap");function St(t){return t!==null?t:[]}(0,i.K2)(St,"constructYamlOmap");var At=new L("tag:yaml.org,2002:omap",{kind:"sequence",resolve:wt,construct:St});var Tt=Object.prototype.toString;function Bt(t){if(t===null)return true;var e,r,i,a,n,o=t;n=new Array(o.length);for(e=0,r=o.length;e<r;e+=1){i=o[e];if(Tt.call(i)!=="[object Object]")return false;a=Object.keys(i);if(a.length!==1)return false;n[e]=[a[0],i[a[0]]]}return true}(0,i.K2)(Bt,"resolveYamlPairs");function Lt(t){if(t===null)return[];var e,r,i,a,n,o=t;n=new Array(o.length);for(e=0,r=o.length;e<r;e+=1){i=o[e];a=Object.keys(i);n[e]=[a[0],i[a[0]]]}return n}(0,i.K2)(Lt,"constructYamlPairs");var Mt=new L("tag:yaml.org,2002:pairs",{kind:"sequence",resolve:Bt,construct:Lt});var _t=Object.prototype.hasOwnProperty;function Ft(t){if(t===null)return true;var e,r=t;for(e in r){if(_t.call(r,e)){if(r[e]!==null)return false}}return true}(0,i.K2)(Ft,"resolveYamlSet");function $t(t){return t!==null?t:{}}(0,i.K2)($t,"constructYamlSet");var Et=new L("tag:yaml.org,2002:set",{kind:"mapping",resolve:Ft,construct:$t});var Ot=ot.extend({implicit:[ut,pt],explicit:[Ct,At,Mt,Et]});var Dt=Object.prototype.hasOwnProperty;var It=1;var Kt=2;var Rt=3;var Pt=4;var zt=1;var qt=2;var Nt=3;var Wt=/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x84\x86-\x9F\uFFFE\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/;var jt=/[\x85\u2028\u2029]/;var Ht=/[,\[\]\{\}]/;var Yt=/^(?:!|!!|![a-z\-]+!)$/i;var Ut=/^(?:!|[^,\[\]\{\}])(?:%[0-9a-f]{2}|[0-9a-z\-#;\/\?:@&=\+\$,_\.!~\*'\(\)\[\]])*$/i;function Gt(t){return Object.prototype.toString.call(t)}(0,i.K2)(Gt,"_class");function Vt(t){return t===10||t===13}(0,i.K2)(Vt,"is_EOL");function Xt(t){return t===9||t===32}(0,i.K2)(Xt,"is_WHITE_SPACE");function Zt(t){return t===9||t===32||t===10||t===13}(0,i.K2)(Zt,"is_WS_OR_EOL");function Jt(t){return t===44||t===91||t===93||t===123||t===125}(0,i.K2)(Jt,"is_FLOW_INDICATOR");function Qt(t){var e;if(48<=t&&t<=57){return t-48}e=t|32;if(97<=e&&e<=102){return e-97+10}return-1}(0,i.K2)(Qt,"fromHexCode");function te(t){if(t===120){return 2}if(t===117){return 4}if(t===85){return 8}return 0}(0,i.K2)(te,"escapedHexLen");function ee(t){if(48<=t&&t<=57){return t-48}return-1}(0,i.K2)(ee,"fromDecimalCode");function re(t){return t===48?"\0":t===97?"":t===98?"\b":t===116?"\t":t===9?"\t":t===110?"\n":t===118?"\v":t===102?"\f":t===114?"\r":t===101?"":t===32?" ":t===34?'"':t===47?"/":t===92?"\\":t===78?"":t===95?" ":t===76?"\u2028":t===80?"\u2029":""}(0,i.K2)(re,"simpleEscapeSequence");function ie(t){if(t<=65535){return String.fromCharCode(t)}return String.fromCharCode((t-65536>>10)+55296,(t-65536&1023)+56320)}(0,i.K2)(ie,"charFromCodepoint");var ae=new Array(256);var ne=new Array(256);for(oe=0;oe<256;oe++){ae[oe]=re(oe)?1:0;ne[oe]=re(oe)}var oe;function se(t,e){this.input=t;this.filename=e["filename"]||null;this.schema=e["schema"]||Ot;this.onWarning=e["onWarning"]||null;this.legacy=e["legacy"]||false;this.json=e["json"]||false;this.listener=e["listener"]||null;this.implicitTypes=this.schema.compiledImplicit;this.typeMap=this.schema.compiledTypeMap;this.length=t.length;this.position=0;this.line=0;this.lineStart=0;this.lineIndent=0;this.firstTabInLine=-1;this.documents=[]}(0,i.K2)(se,"State$1");function le(t,e){var r={name:t.filename,buffer:t.input.slice(0,-1),position:t.position,line:t.line,column:t.position-t.lineStart};r.snippet=w(r);return new x(e,r)}(0,i.K2)(le,"generateError");function ce(t,e){throw le(t,e)}(0,i.K2)(ce,"throwError");function he(t,e){if(t.onWarning){t.onWarning.call(null,le(t,e))}}(0,i.K2)(he,"throwWarning");var de={YAML:(0,i.K2)((function t(e,r,i){var a,n,o;if(e.version!==null){ce(e,"duplication of %YAML directive")}if(i.length!==1){ce(e,"YAML directive accepts exactly one argument")}a=/^([0-9]+)\.([0-9]+)$/.exec(i[0]);if(a===null){ce(e,"ill-formed argument of the YAML directive")}n=parseInt(a[1],10);o=parseInt(a[2],10);if(n!==1){ce(e,"unacceptable YAML version of the document")}e.version=i[0];e.checkLineBreaks=o<2;if(o!==1&&o!==2){he(e,"unsupported YAML version of the document")}}),"handleYamlDirective"),TAG:(0,i.K2)((function t(e,r,i){var a,n;if(i.length!==2){ce(e,"TAG directive accepts exactly two arguments")}a=i[0];n=i[1];if(!Yt.test(a)){ce(e,"ill-formed tag handle (first argument) of the TAG directive")}if(Dt.call(e.tagMap,a)){ce(e,'there is a previously declared suffix for "'+a+'" tag handle')}if(!Ut.test(n)){ce(e,"ill-formed tag prefix (second argument) of the TAG directive")}try{n=decodeURIComponent(n)}catch(o){ce(e,"tag prefix is malformed: "+n)}e.tagMap[a]=n}),"handleTagDirective")};function ue(t,e,r,i){var a,n,o,s;if(e<r){s=t.input.slice(e,r);if(i){for(a=0,n=s.length;a<n;a+=1){o=s.charCodeAt(a);if(!(o===9||32<=o&&o<=1114111)){ce(t,"expected valid JSON character")}}}else if(Wt.test(s)){ce(t,"the stream contains non-printable characters")}t.result+=s}}(0,i.K2)(ue,"captureSegment");function fe(t,e,r,i){var a,n,o,s;if(!m.isObject(r)){ce(t,"cannot merge mappings; the provided source object is unacceptable")}a=Object.keys(r);for(o=0,s=a.length;o<s;o+=1){n=a[o];if(!Dt.call(e,n)){e[n]=r[n];i[n]=true}}}(0,i.K2)(fe,"mergeMappings");function pe(t,e,r,i,a,n,o,s,l){var c,h;if(Array.isArray(a)){a=Array.prototype.slice.call(a);for(c=0,h=a.length;c<h;c+=1){if(Array.isArray(a[c])){ce(t,"nested arrays are not supported inside keys")}if(typeof a==="object"&&Gt(a[c])==="[object Object]"){a[c]="[object Object]"}}}if(typeof a==="object"&&Gt(a)==="[object Object]"){a="[object Object]"}a=String(a);if(e===null){e={}}if(i==="tag:yaml.org,2002:merge"){if(Array.isArray(n)){for(c=0,h=n.length;c<h;c+=1){fe(t,e,n[c],r)}}else{fe(t,e,n,r)}}else{if(!t.json&&!Dt.call(r,a)&&Dt.call(e,a)){t.line=o||t.line;t.lineStart=s||t.lineStart;t.position=l||t.position;ce(t,"duplicated mapping key")}if(a==="__proto__"){Object.defineProperty(e,a,{configurable:true,enumerable:true,writable:true,value:n})}else{e[a]=n}delete r[a]}return e}(0,i.K2)(pe,"storeMappingPair");function ge(t){var e;e=t.input.charCodeAt(t.position);if(e===10){t.position++}else if(e===13){t.position++;if(t.input.charCodeAt(t.position)===10){t.position++}}else{ce(t,"a line break is expected")}t.line+=1;t.lineStart=t.position;t.firstTabInLine=-1}(0,i.K2)(ge,"readLineBreak");function me(t,e,r){var i=0,a=t.input.charCodeAt(t.position);while(a!==0){while(Xt(a)){if(a===9&&t.firstTabInLine===-1){t.firstTabInLine=t.position}a=t.input.charCodeAt(++t.position)}if(e&&a===35){do{a=t.input.charCodeAt(++t.position)}while(a!==10&&a!==13&&a!==0)}if(Vt(a)){ge(t);a=t.input.charCodeAt(t.position);i++;t.lineIndent=0;while(a===32){t.lineIndent++;a=t.input.charCodeAt(++t.position)}}else{break}}if(r!==-1&&i!==0&&t.lineIndent<r){he(t,"deficient indentation")}return i}(0,i.K2)(me,"skipSeparationSpace");function ye(t){var e=t.position,r;r=t.input.charCodeAt(e);if((r===45||r===46)&&r===t.input.charCodeAt(e+1)&&r===t.input.charCodeAt(e+2)){e+=3;r=t.input.charCodeAt(e);if(r===0||Zt(r)){return true}}return false}(0,i.K2)(ye,"testDocumentSeparator");function be(t,e){if(e===1){t.result+=" "}else if(e>1){t.result+=m.repeat("\n",e-1)}}(0,i.K2)(be,"writeFoldedLines");function xe(t,e,r){var i,a,n,o,s,l,c,h,d=t.kind,u=t.result,f;f=t.input.charCodeAt(t.position);if(Zt(f)||Jt(f)||f===35||f===38||f===42||f===33||f===124||f===62||f===39||f===34||f===37||f===64||f===96){return false}if(f===63||f===45){a=t.input.charCodeAt(t.position+1);if(Zt(a)||r&&Jt(a)){return false}}t.kind="scalar";t.result="";n=o=t.position;s=false;while(f!==0){if(f===58){a=t.input.charCodeAt(t.position+1);if(Zt(a)||r&&Jt(a)){break}}else if(f===35){i=t.input.charCodeAt(t.position-1);if(Zt(i)){break}}else if(t.position===t.lineStart&&ye(t)||r&&Jt(f)){break}else if(Vt(f)){l=t.line;c=t.lineStart;h=t.lineIndent;me(t,false,-1);if(t.lineIndent>=e){s=true;f=t.input.charCodeAt(t.position);continue}else{t.position=o;t.line=l;t.lineStart=c;t.lineIndent=h;break}}if(s){ue(t,n,o,false);be(t,t.line-l);n=o=t.position;s=false}if(!Xt(f)){o=t.position+1}f=t.input.charCodeAt(++t.position)}ue(t,n,o,false);if(t.result){return true}t.kind=d;t.result=u;return false}(0,i.K2)(xe,"readPlainScalar");function Ce(t,e){var r,i,a;r=t.input.charCodeAt(t.position);if(r!==39){return false}t.kind="scalar";t.result="";t.position++;i=a=t.position;while((r=t.input.charCodeAt(t.position))!==0){if(r===39){ue(t,i,t.position,true);r=t.input.charCodeAt(++t.position);if(r===39){i=t.position;t.position++;a=t.position}else{return true}}else if(Vt(r)){ue(t,i,a,true);be(t,me(t,false,e));i=a=t.position}else if(t.position===t.lineStart&&ye(t)){ce(t,"unexpected end of the document within a single quoted scalar")}else{t.position++;a=t.position}}ce(t,"unexpected end of the stream within a single quoted scalar")}(0,i.K2)(Ce,"readSingleQuotedScalar");function ve(t,e){var r,i,a,n,o,s;s=t.input.charCodeAt(t.position);if(s!==34){return false}t.kind="scalar";t.result="";t.position++;r=i=t.position;while((s=t.input.charCodeAt(t.position))!==0){if(s===34){ue(t,r,t.position,true);t.position++;return true}else if(s===92){ue(t,r,t.position,true);s=t.input.charCodeAt(++t.position);if(Vt(s)){me(t,false,e)}else if(s<256&&ae[s]){t.result+=ne[s];t.position++}else if((o=te(s))>0){a=o;n=0;for(;a>0;a--){s=t.input.charCodeAt(++t.position);if((o=Qt(s))>=0){n=(n<<4)+o}else{ce(t,"expected hexadecimal character")}}t.result+=ie(n);t.position++}else{ce(t,"unknown escape sequence")}r=i=t.position}else if(Vt(s)){ue(t,r,i,true);be(t,me(t,false,e));r=i=t.position}else if(t.position===t.lineStart&&ye(t)){ce(t,"unexpected end of the document within a double quoted scalar")}else{t.position++;i=t.position}}ce(t,"unexpected end of the stream within a double quoted scalar")}(0,i.K2)(ve,"readDoubleQuotedScalar");function ke(t,e){var r=true,i,a,n,o=t.tag,s,l=t.anchor,c,h,d,u,f,p=Object.create(null),g,m,y,b;b=t.input.charCodeAt(t.position);if(b===91){h=93;f=false;s=[]}else if(b===123){h=125;f=true;s={}}else{return false}if(t.anchor!==null){t.anchorMap[t.anchor]=s}b=t.input.charCodeAt(++t.position);while(b!==0){me(t,true,e);b=t.input.charCodeAt(t.position);if(b===h){t.position++;t.tag=o;t.anchor=l;t.kind=f?"mapping":"sequence";t.result=s;return true}else if(!r){ce(t,"missed comma between flow collection entries")}else if(b===44){ce(t,"expected the node content, but found ','")}m=g=y=null;d=u=false;if(b===63){c=t.input.charCodeAt(t.position+1);if(Zt(c)){d=u=true;t.position++;me(t,true,e)}}i=t.line;a=t.lineStart;n=t.position;Me(t,e,It,false,true);m=t.tag;g=t.result;me(t,true,e);b=t.input.charCodeAt(t.position);if((u||t.line===i)&&b===58){d=true;b=t.input.charCodeAt(++t.position);me(t,true,e);Me(t,e,It,false,true);y=t.result}if(f){pe(t,s,p,m,g,y,i,a,n)}else if(d){s.push(pe(t,null,p,m,g,y,i,a,n))}else{s.push(g)}me(t,true,e);b=t.input.charCodeAt(t.position);if(b===44){r=true;b=t.input.charCodeAt(++t.position)}else{r=false}}ce(t,"unexpected end of the stream within a flow collection")}(0,i.K2)(ke,"readFlowCollection");function we(t,e){var r,i,a=zt,n=false,o=false,s=e,l=0,c=false,h,d;d=t.input.charCodeAt(t.position);if(d===124){i=false}else if(d===62){i=true}else{return false}t.kind="scalar";t.result="";while(d!==0){d=t.input.charCodeAt(++t.position);if(d===43||d===45){if(zt===a){a=d===43?Nt:qt}else{ce(t,"repeat of a chomping mode identifier")}}else if((h=ee(d))>=0){if(h===0){ce(t,"bad explicit indentation width of a block scalar; it cannot be less than one")}else if(!o){s=e+h-1;o=true}else{ce(t,"repeat of an indentation width identifier")}}else{break}}if(Xt(d)){do{d=t.input.charCodeAt(++t.position)}while(Xt(d));if(d===35){do{d=t.input.charCodeAt(++t.position)}while(!Vt(d)&&d!==0)}}while(d!==0){ge(t);t.lineIndent=0;d=t.input.charCodeAt(t.position);while((!o||t.lineIndent<s)&&d===32){t.lineIndent++;d=t.input.charCodeAt(++t.position)}if(!o&&t.lineIndent>s){s=t.lineIndent}if(Vt(d)){l++;continue}if(t.lineIndent<s){if(a===Nt){t.result+=m.repeat("\n",n?1+l:l)}else if(a===zt){if(n){t.result+="\n"}}break}if(i){if(Xt(d)){c=true;t.result+=m.repeat("\n",n?1+l:l)}else if(c){c=false;t.result+=m.repeat("\n",l+1)}else if(l===0){if(n){t.result+=" "}}else{t.result+=m.repeat("\n",l)}}else{t.result+=m.repeat("\n",n?1+l:l)}n=true;o=true;l=0;r=t.position;while(!Vt(d)&&d!==0){d=t.input.charCodeAt(++t.position)}ue(t,r,t.position,false)}return true}(0,i.K2)(we,"readBlockScalar");function Se(t,e){var r,i=t.tag,a=t.anchor,n=[],o,s=false,l;if(t.firstTabInLine!==-1)return false;if(t.anchor!==null){t.anchorMap[t.anchor]=n}l=t.input.charCodeAt(t.position);while(l!==0){if(t.firstTabInLine!==-1){t.position=t.firstTabInLine;ce(t,"tab characters must not be used in indentation")}if(l!==45){break}o=t.input.charCodeAt(t.position+1);if(!Zt(o)){break}s=true;t.position++;if(me(t,true,-1)){if(t.lineIndent<=e){n.push(null);l=t.input.charCodeAt(t.position);continue}}r=t.line;Me(t,e,Rt,false,true);n.push(t.result);me(t,true,-1);l=t.input.charCodeAt(t.position);if((t.line===r||t.lineIndent>e)&&l!==0){ce(t,"bad indentation of a sequence entry")}else if(t.lineIndent<e){break}}if(s){t.tag=i;t.anchor=a;t.kind="sequence";t.result=n;return true}return false}(0,i.K2)(Se,"readBlockSequence");function Ae(t,e,r){var i,a,n,o,s,l,c=t.tag,h=t.anchor,d={},u=Object.create(null),f=null,p=null,g=null,m=false,y=false,b;if(t.firstTabInLine!==-1)return false;if(t.anchor!==null){t.anchorMap[t.anchor]=d}b=t.input.charCodeAt(t.position);while(b!==0){if(!m&&t.firstTabInLine!==-1){t.position=t.firstTabInLine;ce(t,"tab characters must not be used in indentation")}i=t.input.charCodeAt(t.position+1);n=t.line;if((b===63||b===58)&&Zt(i)){if(b===63){if(m){pe(t,d,u,f,p,null,o,s,l);f=p=g=null}y=true;m=true;a=true}else if(m){m=false;a=true}else{ce(t,"incomplete explicit mapping pair; a key node is missed; or followed by a non-tabulated empty line")}t.position+=1;b=i}else{o=t.line;s=t.lineStart;l=t.position;if(!Me(t,r,Kt,false,true)){break}if(t.line===n){b=t.input.charCodeAt(t.position);while(Xt(b)){b=t.input.charCodeAt(++t.position)}if(b===58){b=t.input.charCodeAt(++t.position);if(!Zt(b)){ce(t,"a whitespace character is expected after the key-value separator within a block mapping")}if(m){pe(t,d,u,f,p,null,o,s,l);f=p=g=null}y=true;m=false;a=false;f=t.tag;p=t.result}else if(y){ce(t,"can not read an implicit mapping pair; a colon is missed")}else{t.tag=c;t.anchor=h;return true}}else if(y){ce(t,"can not read a block mapping entry; a multiline key may not be an implicit key")}else{t.tag=c;t.anchor=h;return true}}if(t.line===n||t.lineIndent>e){if(m){o=t.line;s=t.lineStart;l=t.position}if(Me(t,e,Pt,true,a)){if(m){p=t.result}else{g=t.result}}if(!m){pe(t,d,u,f,p,g,o,s,l);f=p=g=null}me(t,true,-1);b=t.input.charCodeAt(t.position)}if((t.line===n||t.lineIndent>e)&&b!==0){ce(t,"bad indentation of a mapping entry")}else if(t.lineIndent<e){break}}if(m){pe(t,d,u,f,p,null,o,s,l)}if(y){t.tag=c;t.anchor=h;t.kind="mapping";t.result=d}return y}(0,i.K2)(Ae,"readBlockMapping");function Te(t){var e,r=false,i=false,a,n,o;o=t.input.charCodeAt(t.position);if(o!==33)return false;if(t.tag!==null){ce(t,"duplication of a tag property")}o=t.input.charCodeAt(++t.position);if(o===60){r=true;o=t.input.charCodeAt(++t.position)}else if(o===33){i=true;a="!!";o=t.input.charCodeAt(++t.position)}else{a="!"}e=t.position;if(r){do{o=t.input.charCodeAt(++t.position)}while(o!==0&&o!==62);if(t.position<t.length){n=t.input.slice(e,t.position);o=t.input.charCodeAt(++t.position)}else{ce(t,"unexpected end of the stream within a verbatim tag")}}else{while(o!==0&&!Zt(o)){if(o===33){if(!i){a=t.input.slice(e-1,t.position+1);if(!Yt.test(a)){ce(t,"named tag handle cannot contain such characters")}i=true;e=t.position+1}else{ce(t,"tag suffix cannot contain exclamation marks")}}o=t.input.charCodeAt(++t.position)}n=t.input.slice(e,t.position);if(Ht.test(n)){ce(t,"tag suffix cannot contain flow indicator characters")}}if(n&&!Ut.test(n)){ce(t,"tag name cannot contain such characters: "+n)}try{n=decodeURIComponent(n)}catch(s){ce(t,"tag name is malformed: "+n)}if(r){t.tag=n}else if(Dt.call(t.tagMap,a)){t.tag=t.tagMap[a]+n}else if(a==="!"){t.tag="!"+n}else if(a==="!!"){t.tag="tag:yaml.org,2002:"+n}else{ce(t,'undeclared tag handle "'+a+'"')}return true}(0,i.K2)(Te,"readTagProperty");function Be(t){var e,r;r=t.input.charCodeAt(t.position);if(r!==38)return false;if(t.anchor!==null){ce(t,"duplication of an anchor property")}r=t.input.charCodeAt(++t.position);e=t.position;while(r!==0&&!Zt(r)&&!Jt(r)){r=t.input.charCodeAt(++t.position)}if(t.position===e){ce(t,"name of an anchor node must contain at least one character")}t.anchor=t.input.slice(e,t.position);return true}(0,i.K2)(Be,"readAnchorProperty");function Le(t){var e,r,i;i=t.input.charCodeAt(t.position);if(i!==42)return false;i=t.input.charCodeAt(++t.position);e=t.position;while(i!==0&&!Zt(i)&&!Jt(i)){i=t.input.charCodeAt(++t.position)}if(t.position===e){ce(t,"name of an alias node must contain at least one character")}r=t.input.slice(e,t.position);if(!Dt.call(t.anchorMap,r)){ce(t,'unidentified alias "'+r+'"')}t.result=t.anchorMap[r];me(t,true,-1);return true}(0,i.K2)(Le,"readAlias");function Me(t,e,r,i,a){var n,o,s,l=1,c=false,h=false,d,u,f,p,g,m;if(t.listener!==null){t.listener("open",t)}t.tag=null;t.anchor=null;t.kind=null;t.result=null;n=o=s=Pt===r||Rt===r;if(i){if(me(t,true,-1)){c=true;if(t.lineIndent>e){l=1}else if(t.lineIndent===e){l=0}else if(t.lineIndent<e){l=-1}}}if(l===1){while(Te(t)||Be(t)){if(me(t,true,-1)){c=true;s=n;if(t.lineIndent>e){l=1}else if(t.lineIndent===e){l=0}else if(t.lineIndent<e){l=-1}}else{s=false}}}if(s){s=c||a}if(l===1||Pt===r){if(It===r||Kt===r){g=e}else{g=e+1}m=t.position-t.lineStart;if(l===1){if(s&&(Se(t,m)||Ae(t,m,g))||ke(t,g)){h=true}else{if(o&&we(t,g)||Ce(t,g)||ve(t,g)){h=true}else if(Le(t)){h=true;if(t.tag!==null||t.anchor!==null){ce(t,"alias node should not have any properties")}}else if(xe(t,g,It===r)){h=true;if(t.tag===null){t.tag="?"}}if(t.anchor!==null){t.anchorMap[t.anchor]=t.result}}}else if(l===0){h=s&&Se(t,m)}}if(t.tag===null){if(t.anchor!==null){t.anchorMap[t.anchor]=t.result}}else if(t.tag==="?"){if(t.result!==null&&t.kind!=="scalar"){ce(t,'unacceptable node kind for !<?> tag; it should be "scalar", not "'+t.kind+'"')}for(d=0,u=t.implicitTypes.length;d<u;d+=1){p=t.implicitTypes[d];if(p.resolve(t.result)){t.result=p.construct(t.result);t.tag=p.tag;if(t.anchor!==null){t.anchorMap[t.anchor]=t.result}break}}}else if(t.tag!=="!"){if(Dt.call(t.typeMap[t.kind||"fallback"],t.tag)){p=t.typeMap[t.kind||"fallback"][t.tag]}else{p=null;f=t.typeMap.multi[t.kind||"fallback"];for(d=0,u=f.length;d<u;d+=1){if(t.tag.slice(0,f[d].tag.length)===f[d].tag){p=f[d];break}}}if(!p){ce(t,"unknown tag !<"+t.tag+">")}if(t.result!==null&&p.kind!==t.kind){ce(t,"unacceptable node kind for !<"+t.tag+'> tag; it should be "'+p.kind+'", not "'+t.kind+'"')}if(!p.resolve(t.result,t.tag)){ce(t,"cannot resolve a node with !<"+t.tag+"> explicit tag")}else{t.result=p.construct(t.result,t.tag);if(t.anchor!==null){t.anchorMap[t.anchor]=t.result}}}if(t.listener!==null){t.listener("close",t)}return t.tag!==null||t.anchor!==null||h}(0,i.K2)(Me,"composeNode");function _e(t){var e=t.position,r,i,a,n=false,o;t.version=null;t.checkLineBreaks=t.legacy;t.tagMap=Object.create(null);t.anchorMap=Object.create(null);while((o=t.input.charCodeAt(t.position))!==0){me(t,true,-1);o=t.input.charCodeAt(t.position);if(t.lineIndent>0||o!==37){break}n=true;o=t.input.charCodeAt(++t.position);r=t.position;while(o!==0&&!Zt(o)){o=t.input.charCodeAt(++t.position)}i=t.input.slice(r,t.position);a=[];if(i.length<1){ce(t,"directive name must not be less than one character in length")}while(o!==0){while(Xt(o)){o=t.input.charCodeAt(++t.position)}if(o===35){do{o=t.input.charCodeAt(++t.position)}while(o!==0&&!Vt(o));break}if(Vt(o))break;r=t.position;while(o!==0&&!Zt(o)){o=t.input.charCodeAt(++t.position)}a.push(t.input.slice(r,t.position))}if(o!==0)ge(t);if(Dt.call(de,i)){de[i](t,i,a)}else{he(t,'unknown document directive "'+i+'"')}}me(t,true,-1);if(t.lineIndent===0&&t.input.charCodeAt(t.position)===45&&t.input.charCodeAt(t.position+1)===45&&t.input.charCodeAt(t.position+2)===45){t.position+=3;me(t,true,-1)}else if(n){ce(t,"directives end mark is expected")}Me(t,t.lineIndent-1,Pt,false,true);me(t,true,-1);if(t.checkLineBreaks&&jt.test(t.input.slice(e,t.position))){he(t,"non-ASCII line breaks are interpreted as content")}t.documents.push(t.result);if(t.position===t.lineStart&&ye(t)){if(t.input.charCodeAt(t.position)===46){t.position+=3;me(t,true,-1)}return}if(t.position<t.length-1){ce(t,"end of the stream or a document separator is expected")}else{return}}(0,i.K2)(_e,"readDocument");function Fe(t,e){t=String(t);e=e||{};if(t.length!==0){if(t.charCodeAt(t.length-1)!==10&&t.charCodeAt(t.length-1)!==13){t+="\n"}if(t.charCodeAt(0)===65279){t=t.slice(1)}}var r=new se(t,e);var i=t.indexOf("\0");if(i!==-1){r.position=i;ce(r,"null byte is not allowed in input")}r.input+="\0";while(r.input.charCodeAt(r.position)===32){r.lineIndent+=1;r.position+=1}while(r.position<r.length-1){_e(r)}return r.documents}(0,i.K2)(Fe,"loadDocuments");function $e(t,e,r){if(e!==null&&typeof e==="object"&&typeof r==="undefined"){r=e;e=null}var i=Fe(t,r);if(typeof e!=="function"){return i}for(var a=0,n=i.length;a<n;a+=1){e(i[a])}}(0,i.K2)($e,"loadAll$1");function Ee(t,e){var r=Fe(t,e);if(r.length===0){return void 0}else if(r.length===1){return r[0]}throw new x("expected a single document in the stream, but found more")}(0,i.K2)(Ee,"load$1");var Oe=$e;var De=Ee;var Ie={loadAll:Oe,load:De};var Ke=Object.prototype.toString;var Re=Object.prototype.hasOwnProperty;var Pe=65279;var ze=9;var qe=10;var Ne=13;var We=32;var je=33;var He=34;var Ye=35;var Ue=37;var Ge=38;var Ve=39;var Xe=42;var Ze=44;var Je=45;var Qe=58;var tr=61;var er=62;var rr=63;var ir=64;var ar=91;var nr=93;var or=96;var sr=123;var lr=124;var cr=125;var hr={};hr[0]="\\0";hr[7]="\\a";hr[8]="\\b";hr[9]="\\t";hr[10]="\\n";hr[11]="\\v";hr[12]="\\f";hr[13]="\\r";hr[27]="\\e";hr[34]='\\"';hr[92]="\\\\";hr[133]="\\N";hr[160]="\\_";hr[8232]="\\L";hr[8233]="\\P";var dr=["y","Y","yes","Yes","YES","on","On","ON","n","N","no","No","NO","off","Off","OFF"];var ur=/^[-+]?[0-9_]+(?::[0-9_]+)+(?:\.[0-9_]*)?$/;function fr(t,e){var r,i,a,n,o,s,l;if(e===null)return{};r={};i=Object.keys(e);for(a=0,n=i.length;a<n;a+=1){o=i[a];s=String(e[o]);if(o.slice(0,2)==="!!"){o="tag:yaml.org,2002:"+o.slice(2)}l=t.compiledTypeMap["fallback"][o];if(l&&Re.call(l.styleAliases,s)){s=l.styleAliases[s]}r[o]=s}return r}(0,i.K2)(fr,"compileStyleMap");function pr(t){var e,r,i;e=t.toString(16).toUpperCase();if(t<=255){r="x";i=2}else if(t<=65535){r="u";i=4}else if(t<=4294967295){r="U";i=8}else{throw new x("code point within a string may not be greater than 0xFFFFFFFF")}return"\\"+r+m.repeat("0",i-e.length)+e}(0,i.K2)(pr,"encodeHex");var gr=1;var mr=2;function yr(t){this.schema=t["schema"]||Ot;this.indent=Math.max(1,t["indent"]||2);this.noArrayIndent=t["noArrayIndent"]||false;this.skipInvalid=t["skipInvalid"]||false;this.flowLevel=m.isNothing(t["flowLevel"])?-1:t["flowLevel"];this.styleMap=fr(this.schema,t["styles"]||null);this.sortKeys=t["sortKeys"]||false;this.lineWidth=t["lineWidth"]||80;this.noRefs=t["noRefs"]||false;this.noCompatMode=t["noCompatMode"]||false;this.condenseFlow=t["condenseFlow"]||false;this.quotingType=t["quotingType"]==='"'?mr:gr;this.forceQuotes=t["forceQuotes"]||false;this.replacer=typeof t["replacer"]==="function"?t["replacer"]:null;this.implicitTypes=this.schema.compiledImplicit;this.explicitTypes=this.schema.compiledExplicit;this.tag=null;this.result="";this.duplicates=[];this.usedDuplicates=null}(0,i.K2)(yr,"State");function br(t,e){var r=m.repeat(" ",e),i=0,a=-1,n="",o,s=t.length;while(i<s){a=t.indexOf("\n",i);if(a===-1){o=t.slice(i);i=s}else{o=t.slice(i,a+1);i=a+1}if(o.length&&o!=="\n")n+=r;n+=o}return n}(0,i.K2)(br,"indentString");function xr(t,e){return"\n"+m.repeat(" ",t.indent*e)}(0,i.K2)(xr,"generateNextLine");function Cr(t,e){var r,i,a;for(r=0,i=t.implicitTypes.length;r<i;r+=1){a=t.implicitTypes[r];if(a.resolve(e)){return true}}return false}(0,i.K2)(Cr,"testImplicitResolving");function vr(t){return t===We||t===ze}(0,i.K2)(vr,"isWhitespace");function kr(t){return 32<=t&&t<=126||161<=t&&t<=55295&&t!==8232&&t!==8233||57344<=t&&t<=65533&&t!==Pe||65536<=t&&t<=1114111}(0,i.K2)(kr,"isPrintable");function wr(t){return kr(t)&&t!==Pe&&t!==Ne&&t!==qe}(0,i.K2)(wr,"isNsCharOrWhitespace");function Sr(t,e,r){var i=wr(t);var a=i&&!vr(t);return(r?i:i&&t!==Ze&&t!==ar&&t!==nr&&t!==sr&&t!==cr)&&t!==Ye&&!(e===Qe&&!a)||wr(e)&&!vr(e)&&t===Ye||e===Qe&&a}(0,i.K2)(Sr,"isPlainSafe");function Ar(t){return kr(t)&&t!==Pe&&!vr(t)&&t!==Je&&t!==rr&&t!==Qe&&t!==Ze&&t!==ar&&t!==nr&&t!==sr&&t!==cr&&t!==Ye&&t!==Ge&&t!==Xe&&t!==je&&t!==lr&&t!==tr&&t!==er&&t!==Ve&&t!==He&&t!==Ue&&t!==ir&&t!==or}(0,i.K2)(Ar,"isPlainSafeFirst");function Tr(t){return!vr(t)&&t!==Qe}(0,i.K2)(Tr,"isPlainSafeLast");function Br(t,e){var r=t.charCodeAt(e),i;if(r>=55296&&r<=56319&&e+1<t.length){i=t.charCodeAt(e+1);if(i>=56320&&i<=57343){return(r-55296)*1024+i-56320+65536}}return r}(0,i.K2)(Br,"codePointAt");function Lr(t){var e=/^\n* /;return e.test(t)}(0,i.K2)(Lr,"needIndentIndicator");var Mr=1;var _r=2;var Fr=3;var $r=4;var Er=5;function Or(t,e,r,i,a,n,o,s){var l;var c=0;var h=null;var d=false;var u=false;var f=i!==-1;var p=-1;var g=Ar(Br(t,0))&&Tr(Br(t,t.length-1));if(e||o){for(l=0;l<t.length;c>=65536?l+=2:l++){c=Br(t,l);if(!kr(c)){return Er}g=g&&Sr(c,h,s);h=c}}else{for(l=0;l<t.length;c>=65536?l+=2:l++){c=Br(t,l);if(c===qe){d=true;if(f){u=u||l-p-1>i&&t[p+1]!==" ";p=l}}else if(!kr(c)){return Er}g=g&&Sr(c,h,s);h=c}u=u||f&&(l-p-1>i&&t[p+1]!==" ")}if(!d&&!u){if(g&&!o&&!a(t)){return Mr}return n===mr?Er:_r}if(r>9&&Lr(t)){return Er}if(!o){return u?$r:Fr}return n===mr?Er:_r}(0,i.K2)(Or,"chooseScalarStyle");function Dr(t,e,r,a,n){t.dump=function(){if(e.length===0){return t.quotingType===mr?'""':"''"}if(!t.noCompatMode){if(dr.indexOf(e)!==-1||ur.test(e)){return t.quotingType===mr?'"'+e+'"':"'"+e+"'"}}var o=t.indent*Math.max(1,r);var s=t.lineWidth===-1?-1:Math.max(Math.min(t.lineWidth,40),t.lineWidth-o);var l=a||t.flowLevel>-1&&r>=t.flowLevel;function c(e){return Cr(t,e)}(0,i.K2)(c,"testAmbiguity");switch(Or(e,l,t.indent,s,c,t.quotingType,t.forceQuotes&&!a,n)){case Mr:return e;case _r:return"'"+e.replace(/'/g,"''")+"'";case Fr:return"|"+Ir(e,t.indent)+Kr(br(e,o));case $r:return">"+Ir(e,t.indent)+Kr(br(Rr(e,s),o));case Er:return'"'+zr(e)+'"';default:throw new x("impossible error: invalid scalar style")}}()}(0,i.K2)(Dr,"writeScalar");function Ir(t,e){var r=Lr(t)?String(e):"";var i=t[t.length-1]==="\n";var a=i&&(t[t.length-2]==="\n"||t==="\n");var n=a?"+":i?"":"-";return r+n+"\n"}(0,i.K2)(Ir,"blockHeader");function Kr(t){return t[t.length-1]==="\n"?t.slice(0,-1):t}(0,i.K2)(Kr,"dropEndingNewline");function Rr(t,e){var r=/(\n+)([^\n]*)/g;var i=function(){var i=t.indexOf("\n");i=i!==-1?i:t.length;r.lastIndex=i;return Pr(t.slice(0,i),e)}();var a=t[0]==="\n"||t[0]===" ";var n;var o;while(o=r.exec(t)){var s=o[1],l=o[2];n=l[0]===" ";i+=s+(!a&&!n&&l!==""?"\n":"")+Pr(l,e);a=n}return i}(0,i.K2)(Rr,"foldString");function Pr(t,e){if(t===""||t[0]===" ")return t;var r=/ [^ ]/g;var i;var a=0,n,o=0,s=0;var l="";while(i=r.exec(t)){s=i.index;if(s-a>e){n=o>a?o:s;l+="\n"+t.slice(a,n);a=n+1}o=s}l+="\n";if(t.length-a>e&&o>a){l+=t.slice(a,o)+"\n"+t.slice(o+1)}else{l+=t.slice(a)}return l.slice(1)}(0,i.K2)(Pr,"foldLine");function zr(t){var e="";var r=0;var i;for(var a=0;a<t.length;r>=65536?a+=2:a++){r=Br(t,a);i=hr[r];if(!i&&kr(r)){e+=t[a];if(r>=65536)e+=t[a+1]}else{e+=i||pr(r)}}return e}(0,i.K2)(zr,"escapeString");function qr(t,e,r){var i="",a=t.tag,n,o,s;for(n=0,o=r.length;n<o;n+=1){s=r[n];if(t.replacer){s=t.replacer.call(r,String(n),s)}if(Yr(t,e,s,false,false)||typeof s==="undefined"&&Yr(t,e,null,false,false)){if(i!=="")i+=","+(!t.condenseFlow?" ":"");i+=t.dump}}t.tag=a;t.dump="["+i+"]"}(0,i.K2)(qr,"writeFlowSequence");function Nr(t,e,r,i){var a="",n=t.tag,o,s,l;for(o=0,s=r.length;o<s;o+=1){l=r[o];if(t.replacer){l=t.replacer.call(r,String(o),l)}if(Yr(t,e+1,l,true,true,false,true)||typeof l==="undefined"&&Yr(t,e+1,null,true,true,false,true)){if(!i||a!==""){a+=xr(t,e)}if(t.dump&&qe===t.dump.charCodeAt(0)){a+="-"}else{a+="- "}a+=t.dump}}t.tag=n;t.dump=a||"[]"}(0,i.K2)(Nr,"writeBlockSequence");function Wr(t,e,r){var i="",a=t.tag,n=Object.keys(r),o,s,l,c,h;for(o=0,s=n.length;o<s;o+=1){h="";if(i!=="")h+=", ";if(t.condenseFlow)h+='"';l=n[o];c=r[l];if(t.replacer){c=t.replacer.call(r,l,c)}if(!Yr(t,e,l,false,false)){continue}if(t.dump.length>1024)h+="? ";h+=t.dump+(t.condenseFlow?'"':"")+":"+(t.condenseFlow?"":" ");if(!Yr(t,e,c,false,false)){continue}h+=t.dump;i+=h}t.tag=a;t.dump="{"+i+"}"}(0,i.K2)(Wr,"writeFlowMapping");function jr(t,e,r,i){var a="",n=t.tag,o=Object.keys(r),s,l,c,h,d,u;if(t.sortKeys===true){o.sort()}else if(typeof t.sortKeys==="function"){o.sort(t.sortKeys)}else if(t.sortKeys){throw new x("sortKeys must be a boolean or a function")}for(s=0,l=o.length;s<l;s+=1){u="";if(!i||a!==""){u+=xr(t,e)}c=o[s];h=r[c];if(t.replacer){h=t.replacer.call(r,c,h)}if(!Yr(t,e+1,c,true,true,true)){continue}d=t.tag!==null&&t.tag!=="?"||t.dump&&t.dump.length>1024;if(d){if(t.dump&&qe===t.dump.charCodeAt(0)){u+="?"}else{u+="? "}}u+=t.dump;if(d){u+=xr(t,e)}if(!Yr(t,e+1,h,true,d)){continue}if(t.dump&&qe===t.dump.charCodeAt(0)){u+=":"}else{u+=": "}u+=t.dump;a+=u}t.tag=n;t.dump=a||"{}"}(0,i.K2)(jr,"writeBlockMapping");function Hr(t,e,r){var i,a,n,o,s,l;a=r?t.explicitTypes:t.implicitTypes;for(n=0,o=a.length;n<o;n+=1){s=a[n];if((s.instanceOf||s.predicate)&&(!s.instanceOf||typeof e==="object"&&e instanceof s.instanceOf)&&(!s.predicate||s.predicate(e))){if(r){if(s.multi&&s.representName){t.tag=s.representName(e)}else{t.tag=s.tag}}else{t.tag="?"}if(s.represent){l=t.styleMap[s.tag]||s.defaultStyle;if(Ke.call(s.represent)==="[object Function]"){i=s.represent(e,l)}else if(Re.call(s.represent,l)){i=s.represent[l](e,l)}else{throw new x("!<"+s.tag+'> tag resolver accepts not "'+l+'" style')}t.dump=i}return true}}return false}(0,i.K2)(Hr,"detectType");function Yr(t,e,r,i,a,n,o){t.tag=null;t.dump=r;if(!Hr(t,r,false)){Hr(t,r,true)}var s=Ke.call(t.dump);var l=i;var c;if(i){i=t.flowLevel<0||t.flowLevel>e}var h=s==="[object Object]"||s==="[object Array]",d,u;if(h){d=t.duplicates.indexOf(r);u=d!==-1}if(t.tag!==null&&t.tag!=="?"||u||t.indent!==2&&e>0){a=false}if(u&&t.usedDuplicates[d]){t.dump="*ref_"+d}else{if(h&&u&&!t.usedDuplicates[d]){t.usedDuplicates[d]=true}if(s==="[object Object]"){if(i&&Object.keys(t.dump).length!==0){jr(t,e,t.dump,a);if(u){t.dump="&ref_"+d+t.dump}}else{Wr(t,e,t.dump);if(u){t.dump="&ref_"+d+" "+t.dump}}}else if(s==="[object Array]"){if(i&&t.dump.length!==0){if(t.noArrayIndent&&!o&&e>0){Nr(t,e-1,t.dump,a)}else{Nr(t,e,t.dump,a)}if(u){t.dump="&ref_"+d+t.dump}}else{qr(t,e,t.dump);if(u){t.dump="&ref_"+d+" "+t.dump}}}else if(s==="[object String]"){if(t.tag!=="?"){Dr(t,t.dump,e,n,l)}}else if(s==="[object Undefined]"){return false}else{if(t.skipInvalid)return false;throw new x("unacceptable kind of an object to dump "+s)}if(t.tag!==null&&t.tag!=="?"){c=encodeURI(t.tag[0]==="!"?t.tag.slice(1):t.tag).replace(/!/g,"%21");if(t.tag[0]==="!"){c="!"+c}else if(c.slice(0,18)==="tag:yaml.org,2002:"){c="!!"+c.slice(18)}else{c="!<"+c+">"}t.dump=c+" "+t.dump}}return true}(0,i.K2)(Yr,"writeNode");function Ur(t,e){var r=[],i=[],a,n;Gr(t,r,i);for(a=0,n=i.length;a<n;a+=1){e.duplicates.push(r[i[a]])}e.usedDuplicates=new Array(n)}(0,i.K2)(Ur,"getDuplicateReferences");function Gr(t,e,r){var i,a,n;if(t!==null&&typeof t==="object"){a=e.indexOf(t);if(a!==-1){if(r.indexOf(a)===-1){r.push(a)}}else{e.push(t);if(Array.isArray(t)){for(a=0,n=t.length;a<n;a+=1){Gr(t[a],e,r)}}else{i=Object.keys(t);for(a=0,n=i.length;a<n;a+=1){Gr(t[i[a]],e,r)}}}}}(0,i.K2)(Gr,"inspectNode");function Vr(t,e){e=e||{};var r=new yr(e);if(!r.noRefs)Ur(t,r);var i=t;if(r.replacer){i=r.replacer.call({"":i},"",i)}if(Yr(r,0,i,true,true))return r.dump+"\n";return""}(0,i.K2)(Vr,"dump$1");var Xr=Vr;var Zr={dump:Xr};function Jr(t,e){return function(){throw new Error("Function yaml."+t+" is removed in js-yaml 4. Use yaml."+e+" instead, which is now safe by default.")}}(0,i.K2)(Jr,"renamed");var Qr=nt;var ti=Ie.load;var ei=Ie.loadAll;var ri=Zr.dump;var ii=Jr("safeLoad","load");var ai=Jr("safeLoadAll","loadAll");var ni=Jr("safeDump","dump")},93113:(t,e,r)=>{"use strict";r.d(e,{D:()=>n});var i=r(75905);var a=r(24982);var n=(0,i.K2)((t=>{const{securityLevel:e}=(0,i.D7)();let r=(0,a.Ltv)("body");if(e==="sandbox"){const e=(0,a.Ltv)(`#i${t}`);const i=e.node()?.contentDocument??document;r=(0,a.Ltv)(i.body)}const n=r.select(`#${t}`);return n}),"selectSvgElement")},76261:(t,e,r)=>{"use strict";r.d(e,{GZ:()=>A,W6:()=>v,hE:()=>S});var i=r(96049);var a=r(75905);var n=r(24982);var o=r(14507);var s=r.n(o);var l=r(60513);function c(t,{markdownAutoWrap:e}){const r=t.replace(/<br\/>/g,"\n");const i=r.replace(/\n{2,}/g,"\n");const a=(0,l.T)(i);if(e===false){return a.replace(/ /g,"&nbsp;")}return a}(0,a.K2)(c,"preprocessMarkdown");function h(t,e={}){const r=c(t,e);const i=o.marked.lexer(r);const n=[[]];let s=0;function l(t,e="normal"){if(t.type==="text"){const r=t.text.split("\n");r.forEach(((t,r)=>{if(r!==0){s++;n.push([])}t.split(" ").forEach((t=>{t=t.replace(/&#39;/g,`'`);if(t){n[s].push({content:t,type:e})}}))}))}else if(t.type==="strong"||t.type==="em"){t.tokens.forEach((e=>{l(e,t.type)}))}else if(t.type==="html"){n[s].push({content:t.text,type:"normal"})}}(0,a.K2)(l,"processNode");i.forEach((t=>{if(t.type==="paragraph"){t.tokens?.forEach((t=>{l(t)}))}else if(t.type==="html"){n[s].push({content:t.text,type:"normal"})}}));return n}(0,a.K2)(h,"markdownToLines");function d(t,{markdownAutoWrap:e}={}){const r=o.marked.lexer(t);function i(t){if(t.type==="text"){if(e===false){return t.text.replace(/\n */g,"<br/>").replace(/ /g,"&nbsp;")}return t.text.replace(/\n */g,"<br/>")}else if(t.type==="strong"){return`<strong>${t.tokens?.map(i).join("")}</strong>`}else if(t.type==="em"){return`<em>${t.tokens?.map(i).join("")}</em>`}else if(t.type==="paragraph"){return`<p>${t.tokens?.map(i).join("")}</p>`}else if(t.type==="space"){return""}else if(t.type==="html"){return`${t.text}`}else if(t.type==="escape"){return t.text}return`Unsupported markdown: ${t.type}`}(0,a.K2)(i,"output");return r.map(i).join("")}(0,a.K2)(d,"markdownToHTML");function u(t){if(Intl.Segmenter){return[...(new Intl.Segmenter).segment(t)].map((t=>t.segment))}return[...t]}(0,a.K2)(u,"splitTextToChars");function f(t,e){const r=u(e.content);return p(t,[],r,e.type)}(0,a.K2)(f,"splitWordToFitWidth");function p(t,e,r,i){if(r.length===0){return[{content:e.join(""),type:i},{content:"",type:i}]}const[a,...n]=r;const o=[...e,a];if(t([{content:o.join(""),type:i}])){return p(t,o,n,i)}if(e.length===0&&a){e.push(a);r.shift()}return[{content:e.join(""),type:i},{content:r.join(""),type:i}]}(0,a.K2)(p,"splitWordToFitWidthRecursion");function g(t,e){if(t.some((({content:t})=>t.includes("\n")))){throw new Error("splitLineToFitWidth does not support newlines in the line")}return m(t,e)}(0,a.K2)(g,"splitLineToFitWidth");function m(t,e,r=[],i=[]){if(t.length===0){if(i.length>0){r.push(i)}return r.length>0?r:[]}let a="";if(t[0].content===" "){a=" ";t.shift()}const n=t.shift()??{content:" ",type:"normal"};const o=[...i];if(a!==""){o.push({content:a,type:"normal"})}o.push(n);if(e(o)){return m(t,e,r,o)}if(i.length>0){r.push(i);t.unshift(n)}else if(n.content){const[i,a]=f(e,n);r.push([i]);if(a.content){t.unshift(a)}}return m(t,e,r)}(0,a.K2)(m,"splitLineToFitWidthRecursion");function y(t,e){if(e){t.attr("style",e)}}(0,a.K2)(y,"applyStyle");async function b(t,e,r,i,n=false){const o=t.append("foreignObject");o.attr("width",`${10*r}px`);o.attr("height",`${10*r}px`);const s=o.append("xhtml:div");let l=e.label;if(e.label&&(0,a.Wi)(e.label)){l=await(0,a.VJ)(e.label.replace(a.Y2.lineBreakRegex,"\n"),(0,a.D7)())}const c=e.isNode?"nodeLabel":"edgeLabel";const h=s.append("span");h.html(l);y(h,e.labelStyle);h.attr("class",`${c} ${i}`);y(s,e.labelStyle);s.style("display","table-cell");s.style("white-space","nowrap");s.style("line-height","1.5");s.style("max-width",r+"px");s.style("text-align","center");s.attr("xmlns","http://www.w3.org/1999/xhtml");if(n){s.attr("class","labelBkg")}let d=s.node().getBoundingClientRect();if(d.width===r){s.style("display","table");s.style("white-space","break-spaces");s.style("width",r+"px");d=s.node().getBoundingClientRect()}return o.node()}(0,a.K2)(b,"addHtmlSpan");function x(t,e,r){return t.append("tspan").attr("class","text-outer-tspan").attr("x",0).attr("y",e*r-.1+"em").attr("dy",r+"em")}(0,a.K2)(x,"createTspan");function C(t,e,r){const i=t.append("text");const a=x(i,1,e);w(a,r);const n=a.node().getComputedTextLength();i.remove();return n}(0,a.K2)(C,"computeWidthOfText");function v(t,e,r){const i=t.append("text");const a=x(i,1,e);w(a,[{content:r,type:"normal"}]);const n=a.node()?.getBoundingClientRect();if(n){i.remove()}return n}(0,a.K2)(v,"computeDimensionOfText");function k(t,e,r,i=false){const n=1.1;const o=e.append("g");const s=o.insert("rect").attr("class","background").attr("style","stroke: none");const l=o.append("text").attr("y","-10.1");let c=0;for(const h of r){const e=(0,a.K2)((e=>C(o,n,e)<=t),"checkWidth");const r=e(h)?[h]:g(h,e);for(const t of r){const e=x(l,c,n);w(e,t);c++}}if(i){const t=l.node().getBBox();const e=2;s.attr("x",t.x-e).attr("y",t.y-e).attr("width",t.width+2*e).attr("height",t.height+2*e);return o.node()}else{return l.node()}}(0,a.K2)(k,"createFormattedText");function w(t,e){t.text("");e.forEach(((e,r)=>{const i=t.append("tspan").attr("font-style",e.type==="em"?"italic":"normal").attr("class","text-inner-tspan").attr("font-weight",e.type==="strong"?"bold":"normal");if(r===0){i.text(e.content)}else{i.text(" "+e.content)}}))}(0,a.K2)(w,"updateTextContentAndStyles");function S(t){return t.replace(/fa[bklrs]?:fa-[\w-]+/g,(t=>`<i class='${t.replace(":"," ")}'></i>`))}(0,a.K2)(S,"replaceIconSubstring");var A=(0,a.K2)((async(t,e="",{style:r="",isTitle:o=false,classes:s="",useHtmlLabels:l=true,isNode:c=true,width:u=200,addSvgBackground:f=false}={},p)=>{a.Rm.debug("XYZ createText",e,r,o,s,l,c,"addSvgBackground: ",f);if(l){const n=d(e,p);const o=S((0,i.Sm)(n));const l=e.replace(/\\\\/g,"\\");const h={isNode:c,label:(0,a.Wi)(e)?l:o,labelStyle:r.replace("fill:","color:")};const g=await b(t,h,u,s,f);return g}else{const i=e.replace(/<br\s*\/?>/g,"<br/>");const a=h(i.replace("<br>","<br/>"),p);const o=k(u,t,a,e?f:false);if(c){if(/stroke:/.exec(r)){r=r.replace("stroke:","lineColor:")}const t=r.replace(/stroke:[^;]+;?/g,"").replace(/stroke-width:[^;]+;?/g,"").replace(/fill:[^;]+;?/g,"").replace(/color:/g,"fill:");(0,n.Ltv)(o).attr("style",t)}else{const t=r.replace(/stroke:[^;]+;?/g,"").replace(/stroke-width:[^;]+;?/g,"").replace(/fill:[^;]+;?/g,"").replace(/background:/g,"fill:");(0,n.Ltv)(o).select("rect").attr("style",t.replace(/background:/g,"fill:"));const e=r.replace(/stroke:[^;]+;?/g,"").replace(/stroke-width:[^;]+;?/g,"").replace(/fill:[^;]+;?/g,"").replace(/color:/g,"fill:");(0,n.Ltv)(o).select("text").attr("style",e)}return o}}),"createText")},68232:(t,e,r)=>{"use strict";r.d(e,{WY:()=>I,pC:()=>O,Gc:()=>F});var i=r(75905);const a=/^[a-z0-9]+(-[a-z0-9]+)*$/;const n=(t,e,r,i="")=>{const a=t.split(":");if(t.slice(0,1)==="@"){if(a.length<2||a.length>3){return null}i=a.shift().slice(1)}if(a.length>3||!a.length){return null}if(a.length>1){const t=a.pop();const r=a.pop();const n={provider:a.length>0?a[0]:i,prefix:r,name:t};return e&&!o(n)?null:n}const n=a[0];const s=n.split("-");if(s.length>1){const t={provider:i,prefix:s.shift(),name:s.join("-")};return e&&!o(t)?null:t}if(r&&i===""){const t={provider:i,prefix:"",name:n};return e&&!o(t,r)?null:t}return null};const o=(t,e)=>{if(!t){return false}return!!((e&&t.prefix===""||!!t.prefix)&&!!t.name)};const s=Object.freeze({left:0,top:0,width:16,height:16});const l=Object.freeze({rotate:0,vFlip:false,hFlip:false});const c=Object.freeze({...s,...l});const h=Object.freeze({...c,body:"",hidden:false});function d(t,e){const r={};if(!t.hFlip!==!e.hFlip){r.hFlip=true}if(!t.vFlip!==!e.vFlip){r.vFlip=true}const i=((t.rotate||0)+(e.rotate||0))%4;if(i){r.rotate=i}return r}function u(t,e){const r=d(t,e);for(const i in h){if(i in l){if(i in t&&!(i in r)){r[i]=l[i]}}else if(i in e){r[i]=e[i]}else if(i in t){r[i]=t[i]}}return r}function f(t,e){const r=t.icons;const i=t.aliases||Object.create(null);const a=Object.create(null);function n(t){if(r[t]){return a[t]=[]}if(!(t in a)){a[t]=null;const e=i[t]&&i[t].parent;const r=e&&n(e);if(r){a[t]=[e].concat(r)}}return a[t]}(e||Object.keys(r).concat(Object.keys(i))).forEach(n);return a}function p(t,e,r){const i=t.icons;const a=t.aliases||Object.create(null);let n={};function o(t){n=u(i[t]||a[t],n)}o(e);r.forEach(o);return u(t,n)}function g(t,e){if(t.icons[e]){return p(t,e,[])}const r=f(t,[e])[e];return r?p(t,e,r):null}const m=Object.freeze({width:null,height:null});const y=Object.freeze({...m,...l});const b=/(-?[0-9.]*[0-9]+[0-9.]*)/g;const x=/^-?[0-9.]*[0-9]+[0-9.]*$/g;function C(t,e,r){if(e===1){return t}r=r||100;if(typeof t==="number"){return Math.ceil(t*e*r)/r}if(typeof t!=="string"){return t}const i=t.split(b);if(i===null||!i.length){return t}const a=[];let n=i.shift();let o=x.test(n);while(true){if(o){const t=parseFloat(n);if(isNaN(t)){a.push(n)}else{a.push(Math.ceil(t*e*r)/r)}}else{a.push(n)}n=i.shift();if(n===void 0){return a.join("")}o=!o}}function v(t,e="defs"){let r="";const i=t.indexOf("<"+e);while(i>=0){const a=t.indexOf(">",i);const n=t.indexOf("</"+e);if(a===-1||n===-1){break}const o=t.indexOf(">",n);if(o===-1){break}r+=t.slice(a+1,n).trim();t=t.slice(0,i).trim()+t.slice(o+1)}return{defs:r,content:t}}function k(t,e){return t?"<defs>"+t+"</defs>"+e:e}function w(t,e,r){const i=v(t);return k(i.defs,e+i.content+r)}const S=t=>t==="unset"||t==="undefined"||t==="none";function A(t,e){const r={...c,...t};const i={...y,...e};const a={left:r.left,top:r.top,width:r.width,height:r.height};let n=r.body;[r,i].forEach((t=>{const e=[];const r=t.hFlip;const i=t.vFlip;let o=t.rotate;if(r){if(i){o+=2}else{e.push("translate("+(a.width+a.left).toString()+" "+(0-a.top).toString()+")");e.push("scale(-1 1)");a.top=a.left=0}}else if(i){e.push("translate("+(0-a.left).toString()+" "+(a.height+a.top).toString()+")");e.push("scale(1 -1)");a.top=a.left=0}let s;if(o<0){o-=Math.floor(o/4)*4}o=o%4;switch(o){case 1:s=a.height/2+a.top;e.unshift("rotate(90 "+s.toString()+" "+s.toString()+")");break;case 2:e.unshift("rotate(180 "+(a.width/2+a.left).toString()+" "+(a.height/2+a.top).toString()+")");break;case 3:s=a.width/2+a.left;e.unshift("rotate(-90 "+s.toString()+" "+s.toString()+")");break}if(o%2===1){if(a.left!==a.top){s=a.left;a.left=a.top;a.top=s}if(a.width!==a.height){s=a.width;a.width=a.height;a.height=s}}if(e.length){n=w(n,'<g transform="'+e.join(" ")+'">',"</g>")}}));const o=i.width;const s=i.height;const l=a.width;const h=a.height;let d;let u;if(o===null){u=s===null?"1em":s==="auto"?h:s;d=C(u,l/h)}else{d=o==="auto"?l:o;u=s===null?C(d,h/l):s==="auto"?h:s}const f={};const p=(t,e)=>{if(!S(e)){f[t]=e.toString()}};p("width",d);p("height",u);const g=[a.left,a.top,l,h];f.viewBox=g.join(" ");return{attributes:f,viewBox:g,body:n}}function T(t,e){let r=t.indexOf("xlink:")===-1?"":' xmlns:xlink="http://www.w3.org/1999/xlink"';for(const i in e){r+=" "+i+'="'+e[i]+'"'}return'<svg xmlns="http://www.w3.org/2000/svg"'+r+">"+t+"</svg>"}const B=/\sid="(\S+)"/g;const L="IconifyId"+Date.now().toString(16)+(Math.random()*16777216|0).toString(16);let M=0;function _(t,e=L){const r=[];let i;while(i=B.exec(t)){r.push(i[1])}if(!r.length){return t}const a="suffix"+(Math.random()*16777216|Date.now()).toString(16);r.forEach((r=>{const i=typeof e==="function"?e(r):e+(M++).toString();const n=r.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");t=t.replace(new RegExp('([#;"])('+n+')([")]|\\.[a-z])',"g"),"$1"+i+a+"$3")}));t=t.replace(new RegExp(a,"g"),"");return t}var F={body:'<g><rect width="80" height="80" style="fill: #087ebf; stroke-width: 0px;"/><text transform="translate(21.16 64.67)" style="fill: #fff; font-family: ArialMT, Arial; font-size: 67.75px;"><tspan x="0" y="0">?</tspan></text></g>',height:80,width:80};var $=new Map;var E=new Map;var O=(0,i.K2)((t=>{for(const e of t){if(!e.name){throw new Error('Invalid icon loader. Must have a "name" property with non-empty string value.')}i.Rm.debug("Registering icon pack:",e.name);if("loader"in e){E.set(e.name,e.loader)}else if("icons"in e){$.set(e.name,e.icons)}else{i.Rm.error("Invalid icon loader:",e);throw new Error('Invalid icon loader. Must have either "icons" or "loader" property.')}}}),"registerIconPacks");var D=(0,i.K2)((async(t,e)=>{const r=n(t,true,e!==void 0);if(!r){throw new Error(`Invalid icon name: ${t}`)}const a=r.prefix||e;if(!a){throw new Error(`Icon name must contain a prefix: ${t}`)}let o=$.get(a);if(!o){const t=E.get(a);if(!t){throw new Error(`Icon set not found: ${r.prefix}`)}try{const e=await t();o={...e,prefix:a};$.set(a,o)}catch(l){i.Rm.error(l);throw new Error(`Failed to load icon set: ${r.prefix}`)}}const s=g(o,r.name);if(!s){throw new Error(`Icon not found: ${t}`)}return s}),"getRegisteredIconData");var I=(0,i.K2)((async(t,e)=>{let r;try{r=await D(t,e?.fallbackPrefix)}catch(o){i.Rm.error(o);r=F}const a=A(r,e);const n=T(_(a.body),a.attributes);return n}),"getIconSVG")},20778:(t,e,r)=>{"use strict";r.d(e,{DA:()=>k,IU:()=>P,KX:()=>B,U:()=>R,U7:()=>Ee,U_:()=>De,Zk:()=>h,aP:()=>_e,gh:()=>Oe,lC:()=>u,on:()=>$e});var i=r(57590);var a=r(68232);var n=r(76261);var o=r(96049);var s=r(75905);var l=r(24982);var c=r(52274);var h=(0,s.K2)((async(t,e,r)=>{let i;const a=e.useHtmlLabels||(0,s._3)((0,s.D7)()?.htmlLabels);if(!r){i="node default"}else{i=r}const c=t.insert("g").attr("class",i).attr("id",e.domId||e.id);const h=c.insert("g").attr("class","label").attr("style",(0,o.KL)(e.labelStyle));let d;if(e.label===void 0){d=""}else{d=typeof e.label==="string"?e.label:e.label[0]}const u=await(0,n.GZ)(h,(0,s.jZ)((0,o.Sm)(d),(0,s.D7)()),{useHtmlLabels:a,width:e.width||(0,s.D7)().flowchart?.wrappingWidth,cssClasses:"markdown-node-label",style:e.labelStyle,addSvgBackground:!!e.icon||!!e.img});let f=u.getBBox();const p=(e?.padding??0)/2;if(a){const t=u.children[0];const e=(0,l.Ltv)(u);const r=t.getElementsByTagName("img");if(r){const t=d.replace(/<img[^>]*>/g,"").trim()==="";await Promise.all([...r].map((e=>new Promise((r=>{function i(){e.style.display="flex";e.style.flexDirection="column";if(t){const t=(0,s.D7)().fontSize?(0,s.D7)().fontSize:window.getComputedStyle(document.body).fontSize;const r=5;const[i=s.UI.fontSize]=(0,o.I5)(t);const a=i*r+"px";e.style.minWidth=a;e.style.maxWidth=a}else{e.style.width="100%"}r(e)}(0,s.K2)(i,"setupImage");setTimeout((()=>{if(e.complete){i()}}));e.addEventListener("error",i);e.addEventListener("load",i)})))))}f=t.getBoundingClientRect();e.attr("width",f.width);e.attr("height",f.height)}if(a){h.attr("transform","translate("+-f.width/2+", "+-f.height/2+")")}else{h.attr("transform","translate(0, "+-f.height/2+")")}if(e.centerLabel){h.attr("transform","translate("+-f.width/2+", "+-f.height/2+")")}h.insert("rect",":first-child");return{shapeSvg:c,bbox:f,halfPadding:p,label:h}}),"labelHelper");var d=(0,s.K2)((async(t,e,r)=>{const i=r.useHtmlLabels||(0,s._3)((0,s.D7)()?.flowchart?.htmlLabels);const a=t.insert("g").attr("class","label").attr("style",r.labelStyle||"");const c=await(0,n.GZ)(a,(0,s.jZ)((0,o.Sm)(e),(0,s.D7)()),{useHtmlLabels:i,width:r.width||(0,s.D7)()?.flowchart?.wrappingWidth,style:r.labelStyle,addSvgBackground:!!r.icon||!!r.img});let h=c.getBBox();const d=r.padding/2;if((0,s._3)((0,s.D7)()?.flowchart?.htmlLabels)){const t=c.children[0];const e=(0,l.Ltv)(c);h=t.getBoundingClientRect();e.attr("width",h.width);e.attr("height",h.height)}if(i){a.attr("transform","translate("+-h.width/2+", "+-h.height/2+")")}else{a.attr("transform","translate(0, "+-h.height/2+")")}if(r.centerLabel){a.attr("transform","translate("+-h.width/2+", "+-h.height/2+")")}a.insert("rect",":first-child");return{shapeSvg:t,bbox:h,halfPadding:d,label:a}}),"insertLabel");var u=(0,s.K2)(((t,e)=>{const r=e.node().getBBox();t.width=r.width;t.height=r.height}),"updateNodeBounds");var f=(0,s.K2)(((t,e)=>(t.look==="handDrawn"?"rough-node":"node")+" "+t.cssClasses+" "+(e||"")),"getNodeClasses");function p(t){const e=t.map(((t,e)=>`${e===0?"M":"L"}${t.x},${t.y}`));e.push("Z");return e.join(" ")}(0,s.K2)(p,"createPathFromPoints");function g(t,e,r,i,a,n){const o=[];const s=50;const l=r-t;const c=i-e;const h=l/n;const d=2*Math.PI/h;const u=e+c/2;for(let f=0;f<=s;f++){const e=f/s;const r=t+e*l;const i=u+a*Math.sin(d*(r-t));o.push({x:r,y:i})}return o}(0,s.K2)(g,"generateFullSineWavePoints");function m(t,e,r,i,a,n){const o=[];const s=a*Math.PI/180;const l=n*Math.PI/180;const c=l-s;const h=c/(i-1);for(let d=0;d<i;d++){const i=s+d*h;const a=t+r*Math.cos(i);const n=e+r*Math.sin(i);o.push({x:-a,y:-n})}return o}(0,s.K2)(m,"generateCirclePoints");var y=(0,s.K2)(((t,e)=>{var r=t.x;var i=t.y;var a=e.x-r;var n=e.y-i;var o=t.width/2;var s=t.height/2;var l,c;if(Math.abs(n)*o>Math.abs(a)*s){if(n<0){s=-s}l=n===0?0:s*a/n;c=s}else{if(a<0){o=-o}l=o;c=a===0?0:o*n/a}return{x:r+l,y:i+c}}),"intersectRect");var b=y;function x(t,e){if(e){t.attr("style",e)}}(0,s.K2)(x,"applyStyle");async function C(t){const e=(0,l.Ltv)(document.createElementNS("http://www.w3.org/2000/svg","foreignObject"));const r=e.append("xhtml:div");let i=t.label;if(t.label&&(0,s.Wi)(t.label)){i=await(0,s.VJ)(t.label.replace(s.Y2.lineBreakRegex,"\n"),(0,s.D7)())}const a=t.isNode?"nodeLabel":"edgeLabel";r.html('<span class="'+a+'" '+(t.labelStyle?'style="'+t.labelStyle+'"':"")+">"+i+"</span>");x(r,t.labelStyle);r.style("display","inline-block");r.style("padding-right","1px");r.style("white-space","nowrap");r.attr("xmlns","http://www.w3.org/1999/xhtml");return e.node()}(0,s.K2)(C,"addHtmlLabel");var v=(0,s.K2)((async(t,e,r,i)=>{let a=t||"";if(typeof a==="object"){a=a[0]}if((0,s._3)((0,s.D7)().flowchart.htmlLabels)){a=a.replace(/\\n|\n/g,"<br />");s.Rm.info("vertexText"+a);const t={isNode:i,label:(0,o.Sm)(a).replace(/fa[blrs]?:fa-[\w-]+/g,(t=>`<i class='${t.replace(":"," ")}'></i>`)),labelStyle:e?e.replace("fill:","color:"):e};let r=await C(t);return r}else{const t=document.createElementNS("http://www.w3.org/2000/svg","text");t.setAttribute("style",e.replace("color:","fill:"));let i=[];if(typeof a==="string"){i=a.split(/\\n|\n|<br\s*\/?>/gi)}else if(Array.isArray(a)){i=a}else{i=[]}for(const e of i){const i=document.createElementNS("http://www.w3.org/2000/svg","tspan");i.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve");i.setAttribute("dy","1em");i.setAttribute("x","0");if(r){i.setAttribute("class","title-row")}else{i.setAttribute("class","row")}i.textContent=e.trim();t.appendChild(i)}return t}}),"createLabel");var k=v;var w=(0,s.K2)(((t,e,r,i,a)=>["M",t+a,e,"H",t+r-a,"A",a,a,0,0,1,t+r,e+a,"V",e+i-a,"A",a,a,0,0,1,t+r-a,e+i,"H",t+a,"A",a,a,0,0,1,t,e+i-a,"V",e+a,"A",a,a,0,0,1,t+a,e,"Z"].join(" ")),"createRoundedRectPathD");var S=(0,s.K2)((t=>{const{handDrawnSeed:e}=(0,s.D7)();return{fill:t,hachureAngle:120,hachureGap:4,fillWeight:2,roughness:.7,stroke:t,seed:e}}),"solidStateFill");var A=(0,s.K2)((t=>{const e=T([...t.cssCompiledStyles||[],...t.cssStyles||[]]);return{stylesMap:e,stylesArray:[...e]}}),"compileStyles");var T=(0,s.K2)((t=>{const e=new Map;t.forEach((t=>{const[r,i]=t.split(":");e.set(r.trim(),i?.trim())}));return e}),"styles2Map");var B=(0,s.K2)((t=>t==="color"||t==="font-size"||t==="font-family"||t==="font-weight"||t==="font-style"||t==="text-decoration"||t==="text-align"||t==="text-transform"||t==="line-height"||t==="letter-spacing"||t==="word-spacing"||t==="text-shadow"||t==="text-overflow"||t==="white-space"||t==="word-wrap"||t==="word-break"||t==="overflow-wrap"||t==="hyphens"),"isLabelStyle");var L=(0,s.K2)((t=>{const{stylesArray:e}=A(t);const r=[];const i=[];const a=[];const n=[];e.forEach((t=>{const e=t[0];if(B(e)){r.push(t.join(":")+" !important")}else{i.push(t.join(":")+" !important");if(e.includes("stroke")){a.push(t.join(":")+" !important")}if(e==="fill"){n.push(t.join(":")+" !important")}}}));return{labelStyles:r.join(";"),nodeStyles:i.join(";"),stylesArray:e,borderStyles:a,backgroundStyles:n}}),"styles2String");var M=(0,s.K2)(((t,e)=>{const{themeVariables:r,handDrawnSeed:i}=(0,s.D7)();const{nodeBorder:a,mainBkg:n}=r;const{stylesMap:o}=A(t);const l=Object.assign({roughness:.7,fill:o.get("fill")||n,fillStyle:"hachure",fillWeight:4,hachureGap:5.2,stroke:o.get("stroke")||a,seed:i,strokeWidth:o.get("stroke-width")?.replace("px","")||1.3,fillLineDash:[0,0]},e);return l}),"userNodeOverrides");var _=(0,s.K2)((async(t,e)=>{s.Rm.info("Creating subgraph rect for ",e.id,e);const r=(0,s.D7)();const{themeVariables:a,handDrawnSeed:o}=r;const{clusterBkg:h,clusterBorder:d}=a;const{labelStyles:u,nodeStyles:f,borderStyles:p,backgroundStyles:g}=L(e);const m=t.insert("g").attr("class","cluster "+e.cssClasses).attr("id",e.id).attr("data-look",e.look);const y=(0,s._3)(r.flowchart.htmlLabels);const x=m.insert("g").attr("class","cluster-label ");const C=await(0,n.GZ)(x,e.label,{style:e.labelStyle,useHtmlLabels:y,isNode:true});let v=C.getBBox();if((0,s._3)(r.flowchart.htmlLabels)){const t=C.children[0];const e=(0,l.Ltv)(C);v=t.getBoundingClientRect();e.attr("width",v.width);e.attr("height",v.height)}const k=e.width<=v.width+e.padding?v.width+e.padding:e.width;if(e.width<=v.width+e.padding){e.diff=(k-e.width)/2-e.padding}else{e.diff=-e.padding}const S=e.height;const A=e.x-k/2;const T=e.y-S/2;s.Rm.trace("Data ",e,JSON.stringify(e));let B;if(e.look==="handDrawn"){const t=c.A.svg(m);const r=M(e,{roughness:.7,fill:h,stroke:d,fillWeight:3,seed:o});const i=t.path(w(A,T,k,S,0),r);B=m.insert((()=>{s.Rm.debug("Rough node insert CXC",i);return i}),":first-child");B.select("path:nth-child(2)").attr("style",p.join(";"));B.select("path").attr("style",g.join(";").replace("fill","stroke"))}else{B=m.insert("rect",":first-child");B.attr("style",f).attr("rx",e.rx).attr("ry",e.ry).attr("x",A).attr("y",T).attr("width",k).attr("height",S)}const{subGraphTitleTopMargin:_}=(0,i.O)(r);x.attr("transform",`translate(${e.x-v.width/2}, ${e.y-e.height/2+_})`);if(u){const t=x.select("span");if(t){t.attr("style",u)}}const F=B.node().getBBox();e.offsetX=0;e.width=F.width;e.height=F.height;e.offsetY=v.height-e.padding/2;e.intersect=function(t){return b(e,t)};return{cluster:m,labelBBox:v}}),"rect");var F=(0,s.K2)(((t,e)=>{const r=t.insert("g").attr("class","note-cluster").attr("id",e.id);const i=r.insert("rect",":first-child");const a=0*e.padding;const n=a/2;i.attr("rx",e.rx).attr("ry",e.ry).attr("x",e.x-e.width/2-n).attr("y",e.y-e.height/2-n).attr("width",e.width+a).attr("height",e.height+a).attr("fill","none");const o=i.node().getBBox();e.width=o.width;e.height=o.height;e.intersect=function(t){return b(e,t)};return{cluster:r,labelBBox:{width:0,height:0}}}),"noteGroup");var $=(0,s.K2)((async(t,e)=>{const r=(0,s.D7)();const{themeVariables:i,handDrawnSeed:a}=r;const{altBackground:n,compositeBackground:o,compositeTitleBackground:h,nodeBorder:d}=i;const u=t.insert("g").attr("class",e.cssClasses).attr("id",e.id).attr("data-id",e.id).attr("data-look",e.look);const f=u.insert("g",":first-child");const p=u.insert("g").attr("class","cluster-label");let g=u.append("rect");const m=p.node().appendChild(await k(e.label,e.labelStyle,void 0,true));let y=m.getBBox();if((0,s._3)(r.flowchart.htmlLabels)){const t=m.children[0];const e=(0,l.Ltv)(m);y=t.getBoundingClientRect();e.attr("width",y.width);e.attr("height",y.height)}const x=0*e.padding;const C=x/2;const v=(e.width<=y.width+e.padding?y.width+e.padding:e.width)+x;if(e.width<=y.width+e.padding){e.diff=(v-e.width)/2-e.padding}else{e.diff=-e.padding}const S=e.height+x;const A=e.height+x-y.height-6;const T=e.x-v/2;const B=e.y-S/2;e.width=v;const L=e.y-e.height/2-C+y.height+2;let M;if(e.look==="handDrawn"){const t=e.cssClasses.includes("statediagram-cluster-alt");const r=c.A.svg(u);const i=e.rx||e.ry?r.path(w(T,B,v,S,10),{roughness:.7,fill:h,fillStyle:"solid",stroke:d,seed:a}):r.rectangle(T,B,v,S,{seed:a});M=u.insert((()=>i),":first-child");const s=r.rectangle(T,L,v,A,{fill:t?n:o,fillStyle:t?"hachure":"solid",stroke:d,seed:a});M=u.insert((()=>i),":first-child");g=u.insert((()=>s))}else{M=f.insert("rect",":first-child");const t="outer";M.attr("class",t).attr("x",T).attr("y",B).attr("width",v).attr("height",S).attr("data-look",e.look);g.attr("class","inner").attr("x",T).attr("y",L).attr("width",v).attr("height",A)}p.attr("transform",`translate(${e.x-y.width/2}, ${B+1-((0,s._3)(r.flowchart.htmlLabels)?0:3)})`);const _=M.node().getBBox();e.height=_.height;e.offsetX=0;e.offsetY=y.height-e.padding/2;e.labelBBox=y;e.intersect=function(t){return b(e,t)};return{cluster:u,labelBBox:y}}),"roundedWithTitle");var E=(0,s.K2)((async(t,e)=>{s.Rm.info("Creating subgraph rect for ",e.id,e);const r=(0,s.D7)();const{themeVariables:a,handDrawnSeed:o}=r;const{clusterBkg:h,clusterBorder:d}=a;const{labelStyles:u,nodeStyles:f,borderStyles:p,backgroundStyles:g}=L(e);const m=t.insert("g").attr("class","cluster "+e.cssClasses).attr("id",e.id).attr("data-look",e.look);const y=(0,s._3)(r.flowchart.htmlLabels);const x=m.insert("g").attr("class","cluster-label ");const C=await(0,n.GZ)(x,e.label,{style:e.labelStyle,useHtmlLabels:y,isNode:true,width:e.width});let v=C.getBBox();if((0,s._3)(r.flowchart.htmlLabels)){const t=C.children[0];const e=(0,l.Ltv)(C);v=t.getBoundingClientRect();e.attr("width",v.width);e.attr("height",v.height)}const k=e.width<=v.width+e.padding?v.width+e.padding:e.width;if(e.width<=v.width+e.padding){e.diff=(k-e.width)/2-e.padding}else{e.diff=-e.padding}const S=e.height;const A=e.x-k/2;const T=e.y-S/2;s.Rm.trace("Data ",e,JSON.stringify(e));let B;if(e.look==="handDrawn"){const t=c.A.svg(m);const r=M(e,{roughness:.7,fill:h,stroke:d,fillWeight:4,seed:o});const i=t.path(w(A,T,k,S,e.rx),r);B=m.insert((()=>{s.Rm.debug("Rough node insert CXC",i);return i}),":first-child");B.select("path:nth-child(2)").attr("style",p.join(";"));B.select("path").attr("style",g.join(";").replace("fill","stroke"))}else{B=m.insert("rect",":first-child");B.attr("style",f).attr("rx",e.rx).attr("ry",e.ry).attr("x",A).attr("y",T).attr("width",k).attr("height",S)}const{subGraphTitleTopMargin:_}=(0,i.O)(r);x.attr("transform",`translate(${e.x-v.width/2}, ${e.y-e.height/2+_})`);if(u){const t=x.select("span");if(t){t.attr("style",u)}}const F=B.node().getBBox();e.offsetX=0;e.width=F.width;e.height=F.height;e.offsetY=v.height-e.padding/2;e.intersect=function(t){return b(e,t)};return{cluster:m,labelBBox:v}}),"kanbanSection");var O=(0,s.K2)(((t,e)=>{const r=(0,s.D7)();const{themeVariables:i,handDrawnSeed:a}=r;const{nodeBorder:n}=i;const o=t.insert("g").attr("class",e.cssClasses).attr("id",e.id).attr("data-look",e.look);const l=o.insert("g",":first-child");const h=0*e.padding;const d=e.width+h;e.diff=-e.padding;const u=e.height+h;const f=e.x-d/2;const p=e.y-u/2;e.width=d;let g;if(e.look==="handDrawn"){const t=c.A.svg(o);const e=t.rectangle(f,p,d,u,{fill:"lightgrey",roughness:.5,strokeLineDash:[5],stroke:n,seed:a});g=o.insert((()=>e),":first-child")}else{g=l.insert("rect",":first-child");const t="divider";g.attr("class",t).attr("x",f).attr("y",p).attr("width",d).attr("height",u).attr("data-look",e.look)}const m=g.node().getBBox();e.height=m.height;e.offsetX=0;e.offsetY=0;e.intersect=function(t){return b(e,t)};return{cluster:o,labelBBox:{}}}),"divider");var D=_;var I={rect:_,squareRect:D,roundedWithTitle:$,noteGroup:F,divider:O,kanbanSection:E};var K=new Map;var R=(0,s.K2)((async(t,e)=>{const r=e.shape||"rect";const i=await I[r](t,e);K.set(e.id,i);return i}),"insertCluster");var P=(0,s.K2)((()=>{K=new Map}),"clear");function z(t,e){return t.intersect(e)}(0,s.K2)(z,"intersectNode");var q=z;function N(t,e,r,i){var a=t.x;var n=t.y;var o=a-i.x;var s=n-i.y;var l=Math.sqrt(e*e*s*s+r*r*o*o);var c=Math.abs(e*r*o/l);if(i.x<a){c=-c}var h=Math.abs(e*r*s/l);if(i.y<n){h=-h}return{x:a+c,y:n+h}}(0,s.K2)(N,"intersectEllipse");var W=N;function j(t,e,r){return W(t,e,e,r)}(0,s.K2)(j,"intersectCircle");var H=j;function Y(t,e,r,i){var a,n,o,s,l,c;var h,d,u,f;var p,g,m;var y,b;a=e.y-t.y;o=t.x-e.x;l=e.x*t.y-t.x*e.y;u=a*r.x+o*r.y+l;f=a*i.x+o*i.y+l;if(u!==0&&f!==0&&U(u,f)){return}n=i.y-r.y;s=r.x-i.x;c=i.x*r.y-r.x*i.y;h=n*t.x+s*t.y+c;d=n*e.x+s*e.y+c;if(h!==0&&d!==0&&U(h,d)){return}p=a*s-n*o;if(p===0){return}g=Math.abs(p/2);m=o*c-s*l;y=m<0?(m-g)/p:(m+g)/p;m=n*l-a*c;b=m<0?(m-g)/p:(m+g)/p;return{x:y,y:b}}(0,s.K2)(Y,"intersectLine");function U(t,e){return t*e>0}(0,s.K2)(U,"sameSign");var G=Y;function V(t,e,r){let i=t.x;let a=t.y;let n=[];let o=Number.POSITIVE_INFINITY;let s=Number.POSITIVE_INFINITY;if(typeof e.forEach==="function"){e.forEach((function(t){o=Math.min(o,t.x);s=Math.min(s,t.y)}))}else{o=Math.min(o,e.x);s=Math.min(s,e.y)}let l=i-t.width/2-o;let c=a-t.height/2-s;for(let h=0;h<e.length;h++){let i=e[h];let a=e[h<e.length-1?h+1:0];let o=G(t,r,{x:l+i.x,y:c+i.y},{x:l+a.x,y:c+a.y});if(o){n.push(o)}}if(!n.length){return t}if(n.length>1){n.sort((function(t,e){let i=t.x-r.x;let a=t.y-r.y;let n=Math.sqrt(i*i+a*a);let o=e.x-r.x;let s=e.y-r.y;let l=Math.sqrt(o*o+s*s);return n<l?-1:n===l?0:1}))}return n[0]}(0,s.K2)(V,"intersectPolygon");var X=V;var Z={node:q,circle:H,ellipse:W,polygon:X,rect:b};function J(t,e){const{labelStyles:r}=L(e);e.labelStyle=r;const i=f(e);let a=i;if(!i){a="anchor"}const n=t.insert("g").attr("class",a).attr("id",e.domId||e.id);const l=1;const{cssStyles:h}=e;const d=c.A.svg(n);const p=M(e,{fill:"black",stroke:"none",fillStyle:"solid"});if(e.look!=="handDrawn"){p.roughness=0}const g=d.circle(0,0,l*2,p);const m=n.insert((()=>g),":first-child");m.attr("class","anchor").attr("style",(0,o.KL)(h));u(e,m);e.intersect=function(t){s.Rm.info("Circle intersect",e,l,t);return Z.circle(e,l,t)};return n}(0,s.K2)(J,"anchor");function Q(t,e,r,i,a,n,o){const s=20;const l=(t+r)/2;const c=(e+i)/2;const h=Math.atan2(i-e,r-t);const d=(r-t)/2;const u=(i-e)/2;const f=d/a;const p=u/n;const g=Math.sqrt(f**2+p**2);if(g>1){throw new Error("The given radii are too small to create an arc between the points.")}const m=Math.sqrt(1-g**2);const y=l+m*n*Math.sin(h)*(o?-1:1);const b=c-m*a*Math.cos(h)*(o?-1:1);const x=Math.atan2((e-b)/n,(t-y)/a);const C=Math.atan2((i-b)/n,(r-y)/a);let v=C-x;if(o&&v<0){v+=2*Math.PI}if(!o&&v>0){v-=2*Math.PI}const k=[];for(let w=0;w<s;w++){const t=w/(s-1);const e=x+t*v;const r=y+a*Math.cos(e);const i=b+n*Math.sin(e);k.push({x:r,y:i})}return k}(0,s.K2)(Q,"generateArcPoints");async function tt(t,e){const{labelStyles:r,nodeStyles:i}=L(e);e.labelStyle=r;const{shapeSvg:a,bbox:n}=await h(t,e,f(e));const o=n.width+e.padding+20;const s=n.height+e.padding;const l=s/2;const d=l/(2.5+s/50);const{cssStyles:g}=e;const m=[{x:o/2,y:-s/2},{x:-o/2,y:-s/2},...Q(-o/2,-s/2,-o/2,s/2,d,l,false),{x:o/2,y:s/2},...Q(o/2,s/2,o/2,-s/2,d,l,true)];const y=c.A.svg(a);const b=M(e,{});if(e.look!=="handDrawn"){b.roughness=0;b.fillStyle="solid"}const x=p(m);const C=y.path(x,b);const v=a.insert((()=>C),":first-child");v.attr("class","basic label-container");if(g&&e.look!=="handDrawn"){v.selectAll("path").attr("style",g)}if(i&&e.look!=="handDrawn"){v.selectAll("path").attr("style",i)}v.attr("transform",`translate(${d/2}, 0)`);u(e,v);e.intersect=function(t){const r=Z.polygon(e,m,t);return r};return a}(0,s.K2)(tt,"bowTieRect");function et(t,e,r,i){return t.insert("polygon",":first-child").attr("points",i.map((function(t){return t.x+","+t.y})).join(" ")).attr("class","label-container").attr("transform","translate("+-e/2+","+r/2+")")}(0,s.K2)(et,"insertPolygonShape");async function rt(t,e){const{labelStyles:r,nodeStyles:i}=L(e);e.labelStyle=r;const{shapeSvg:a,bbox:n}=await h(t,e,f(e));const o=n.height+e.padding;const s=12;const l=n.width+e.padding+s;const d=0;const g=l;const m=-o;const y=0;const b=[{x:d+s,y:m},{x:g,y:m},{x:g,y},{x:d,y},{x:d,y:m+s},{x:d+s,y:m}];let x;const{cssStyles:C}=e;if(e.look==="handDrawn"){const t=c.A.svg(a);const r=M(e,{});const i=p(b);const n=t.path(i,r);x=a.insert((()=>n),":first-child").attr("transform",`translate(${-l/2}, ${o/2})`);if(C){x.attr("style",C)}}else{x=et(a,l,o,b)}if(i){x.attr("style",i)}u(e,x);e.intersect=function(t){return Z.polygon(e,b,t)};return a}(0,s.K2)(rt,"card");function it(t,e){const{nodeStyles:r}=L(e);e.label="";const i=t.insert("g").attr("class",f(e)).attr("id",e.domId??e.id);const{cssStyles:a}=e;const n=Math.max(28,e.width??0);const o=[{x:0,y:n/2},{x:n/2,y:0},{x:0,y:-n/2},{x:-n/2,y:0}];const s=c.A.svg(i);const l=M(e,{});if(e.look!=="handDrawn"){l.roughness=0;l.fillStyle="solid"}const h=p(o);const d=s.path(h,l);const u=i.insert((()=>d),":first-child");if(a&&e.look!=="handDrawn"){u.selectAll("path").attr("style",a)}if(r&&e.look!=="handDrawn"){u.selectAll("path").attr("style",r)}e.width=28;e.height=28;e.intersect=function(t){return Z.polygon(e,o,t)};return i}(0,s.K2)(it,"choice");async function at(t,e){const{labelStyles:r,nodeStyles:i}=L(e);e.labelStyle=r;const{shapeSvg:a,bbox:n,halfPadding:l}=await h(t,e,f(e));const d=n.width/2+l;let p;const{cssStyles:g}=e;if(e.look==="handDrawn"){const t=c.A.svg(a);const r=M(e,{});const i=t.circle(0,0,d*2,r);p=a.insert((()=>i),":first-child");p.attr("class","basic label-container").attr("style",(0,o.KL)(g))}else{p=a.insert("circle",":first-child").attr("class","basic label-container").attr("style",i).attr("r",d).attr("cx",0).attr("cy",0)}u(e,p);e.intersect=function(t){s.Rm.info("Circle intersect",e,d,t);return Z.circle(e,d,t)};return a}(0,s.K2)(at,"circle");function nt(t){const e=Math.cos(Math.PI/4);const r=Math.sin(Math.PI/4);const i=t*2;const a={x:i/2*e,y:i/2*r};const n={x:-(i/2)*e,y:i/2*r};const o={x:-(i/2)*e,y:-(i/2)*r};const s={x:i/2*e,y:-(i/2)*r};return`M ${n.x},${n.y} L ${s.x},${s.y}\n                   M ${a.x},${a.y} L ${o.x},${o.y}`}(0,s.K2)(nt,"createLine");function ot(t,e){const{labelStyles:r,nodeStyles:i}=L(e);e.labelStyle=r;e.label="";const a=t.insert("g").attr("class",f(e)).attr("id",e.domId??e.id);const n=Math.max(30,e?.width??0);const{cssStyles:o}=e;const l=c.A.svg(a);const h=M(e,{});if(e.look!=="handDrawn"){h.roughness=0;h.fillStyle="solid"}const d=l.circle(0,0,n*2,h);const p=nt(n);const g=l.path(p,h);const m=a.insert((()=>d),":first-child");m.insert((()=>g));if(o&&e.look!=="handDrawn"){m.selectAll("path").attr("style",o)}if(i&&e.look!=="handDrawn"){m.selectAll("path").attr("style",i)}u(e,m);e.intersect=function(t){s.Rm.info("crossedCircle intersect",e,{radius:n,point:t});const r=Z.circle(e,n,t);return r};return a}(0,s.K2)(ot,"crossedCircle");function st(t,e,r,i=100,a=0,n=180){const o=[];const s=a*Math.PI/180;const l=n*Math.PI/180;const c=l-s;const h=c/(i-1);for(let d=0;d<i;d++){const i=s+d*h;const a=t+r*Math.cos(i);const n=e+r*Math.sin(i);o.push({x:-a,y:-n})}return o}(0,s.K2)(st,"generateCirclePoints");async function lt(t,e){const{labelStyles:r,nodeStyles:i}=L(e);e.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await h(t,e,f(e));const s=n.width+(e.padding??0);const l=n.height+(e.padding??0);const d=Math.max(5,l*.1);const{cssStyles:g}=e;const m=[...st(s/2,-l/2,d,30,-90,0),{x:-s/2-d,y:d},...st(s/2+d*2,-d,d,20,-180,-270),...st(s/2+d*2,d,d,20,-90,-180),{x:-s/2-d,y:-l/2},...st(s/2,l/2,d,20,0,90)];const y=[{x:s/2,y:-l/2-d},{x:-s/2,y:-l/2-d},...st(s/2,-l/2,d,20,-90,0),{x:-s/2-d,y:-d},...st(s/2+s*.1,-d,d,20,-180,-270),...st(s/2+s*.1,d,d,20,-90,-180),{x:-s/2-d,y:l/2},...st(s/2,l/2,d,20,0,90),{x:-s/2,y:l/2+d},{x:s/2,y:l/2+d}];const b=c.A.svg(a);const x=M(e,{fill:"none"});if(e.look!=="handDrawn"){x.roughness=0;x.fillStyle="solid"}const C=p(m);const v=C.replace("Z","");const k=b.path(v,x);const w=p(y);const S=b.path(w,{...x});const A=a.insert("g",":first-child");A.insert((()=>S),":first-child").attr("stroke-opacity",0);A.insert((()=>k),":first-child");A.attr("class","text");if(g&&e.look!=="handDrawn"){A.selectAll("path").attr("style",g)}if(i&&e.look!=="handDrawn"){A.selectAll("path").attr("style",i)}A.attr("transform",`translate(${d}, 0)`);o.attr("transform",`translate(${-s/2+d-(n.x-(n.left??0))},${-l/2+(e.padding??0)/2-(n.y-(n.top??0))})`);u(e,A);e.intersect=function(t){const r=Z.polygon(e,y,t);return r};return a}(0,s.K2)(lt,"curlyBraceLeft");function ct(t,e,r,i=100,a=0,n=180){const o=[];const s=a*Math.PI/180;const l=n*Math.PI/180;const c=l-s;const h=c/(i-1);for(let d=0;d<i;d++){const i=s+d*h;const a=t+r*Math.cos(i);const n=e+r*Math.sin(i);o.push({x:a,y:n})}return o}(0,s.K2)(ct,"generateCirclePoints");async function ht(t,e){const{labelStyles:r,nodeStyles:i}=L(e);e.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await h(t,e,f(e));const s=n.width+(e.padding??0);const l=n.height+(e.padding??0);const d=Math.max(5,l*.1);const{cssStyles:g}=e;const m=[...ct(s/2,-l/2,d,20,-90,0),{x:s/2+d,y:-d},...ct(s/2+d*2,-d,d,20,-180,-270),...ct(s/2+d*2,d,d,20,-90,-180),{x:s/2+d,y:l/2},...ct(s/2,l/2,d,20,0,90)];const y=[{x:-s/2,y:-l/2-d},{x:s/2,y:-l/2-d},...ct(s/2,-l/2,d,20,-90,0),{x:s/2+d,y:-d},...ct(s/2+d*2,-d,d,20,-180,-270),...ct(s/2+d*2,d,d,20,-90,-180),{x:s/2+d,y:l/2},...ct(s/2,l/2,d,20,0,90),{x:s/2,y:l/2+d},{x:-s/2,y:l/2+d}];const b=c.A.svg(a);const x=M(e,{fill:"none"});if(e.look!=="handDrawn"){x.roughness=0;x.fillStyle="solid"}const C=p(m);const v=C.replace("Z","");const k=b.path(v,x);const w=p(y);const S=b.path(w,{...x});const A=a.insert("g",":first-child");A.insert((()=>S),":first-child").attr("stroke-opacity",0);A.insert((()=>k),":first-child");A.attr("class","text");if(g&&e.look!=="handDrawn"){A.selectAll("path").attr("style",g)}if(i&&e.look!=="handDrawn"){A.selectAll("path").attr("style",i)}A.attr("transform",`translate(${-d}, 0)`);o.attr("transform",`translate(${-s/2+(e.padding??0)/2-(n.x-(n.left??0))},${-l/2+(e.padding??0)/2-(n.y-(n.top??0))})`);u(e,A);e.intersect=function(t){const r=Z.polygon(e,y,t);return r};return a}(0,s.K2)(ht,"curlyBraceRight");function dt(t,e,r,i=100,a=0,n=180){const o=[];const s=a*Math.PI/180;const l=n*Math.PI/180;const c=l-s;const h=c/(i-1);for(let d=0;d<i;d++){const i=s+d*h;const a=t+r*Math.cos(i);const n=e+r*Math.sin(i);o.push({x:-a,y:-n})}return o}(0,s.K2)(dt,"generateCirclePoints");async function ut(t,e){const{labelStyles:r,nodeStyles:i}=L(e);e.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await h(t,e,f(e));const s=n.width+(e.padding??0);const l=n.height+(e.padding??0);const d=Math.max(5,l*.1);const{cssStyles:g}=e;const m=[...dt(s/2,-l/2,d,30,-90,0),{x:-s/2-d,y:d},...dt(s/2+d*2,-d,d,20,-180,-270),...dt(s/2+d*2,d,d,20,-90,-180),{x:-s/2-d,y:-l/2},...dt(s/2,l/2,d,20,0,90)];const y=[...dt(-s/2+d+d/2,-l/2,d,20,-90,-180),{x:s/2-d/2,y:d},...dt(-s/2-d/2,-d,d,20,0,90),...dt(-s/2-d/2,d,d,20,-90,0),{x:s/2-d/2,y:-d},...dt(-s/2+d+d/2,l/2,d,30,-180,-270)];const b=[{x:s/2,y:-l/2-d},{x:-s/2,y:-l/2-d},...dt(s/2,-l/2,d,20,-90,0),{x:-s/2-d,y:-d},...dt(s/2+d*2,-d,d,20,-180,-270),...dt(s/2+d*2,d,d,20,-90,-180),{x:-s/2-d,y:l/2},...dt(s/2,l/2,d,20,0,90),{x:-s/2,y:l/2+d},{x:s/2-d-d/2,y:l/2+d},...dt(-s/2+d+d/2,-l/2,d,20,-90,-180),{x:s/2-d/2,y:d},...dt(-s/2-d/2,-d,d,20,0,90),...dt(-s/2-d/2,d,d,20,-90,0),{x:s/2-d/2,y:-d},...dt(-s/2+d+d/2,l/2,d,30,-180,-270)];const x=c.A.svg(a);const C=M(e,{fill:"none"});if(e.look!=="handDrawn"){C.roughness=0;C.fillStyle="solid"}const v=p(m);const k=v.replace("Z","");const w=x.path(k,C);const S=p(y);const A=S.replace("Z","");const T=x.path(A,C);const B=p(b);const _=x.path(B,{...C});const F=a.insert("g",":first-child");F.insert((()=>_),":first-child").attr("stroke-opacity",0);F.insert((()=>w),":first-child");F.insert((()=>T),":first-child");F.attr("class","text");if(g&&e.look!=="handDrawn"){F.selectAll("path").attr("style",g)}if(i&&e.look!=="handDrawn"){F.selectAll("path").attr("style",i)}F.attr("transform",`translate(${d-d/4}, 0)`);o.attr("transform",`translate(${-s/2+(e.padding??0)/2-(n.x-(n.left??0))},${-l/2+(e.padding??0)/2-(n.y-(n.top??0))})`);u(e,F);e.intersect=function(t){const r=Z.polygon(e,b,t);return r};return a}(0,s.K2)(ut,"curlyBraces");async function ft(t,e){const{labelStyles:r,nodeStyles:i}=L(e);e.labelStyle=r;const{shapeSvg:a,bbox:n}=await h(t,e,f(e));const o=80,s=20;const l=Math.max(o,(n.width+(e.padding??0)*2)*1.25,e?.width??0);const d=Math.max(s,n.height+(e.padding??0)*2,e?.height??0);const g=d/2;const{cssStyles:y}=e;const b=c.A.svg(a);const x=M(e,{});if(e.look!=="handDrawn"){x.roughness=0;x.fillStyle="solid"}const C=l,v=d;const k=C-g;const w=v/4;const S=[{x:k,y:0},{x:w,y:0},{x:0,y:v/2},{x:w,y:v},{x:k,y:v},...m(-k,-v/2,g,50,270,90)];const A=p(S);const T=b.path(A,x);const B=a.insert((()=>T),":first-child");B.attr("class","basic label-container");if(y&&e.look!=="handDrawn"){B.selectChildren("path").attr("style",y)}if(i&&e.look!=="handDrawn"){B.selectChildren("path").attr("style",i)}B.attr("transform",`translate(${-l/2}, ${-d/2})`);u(e,B);e.intersect=function(t){const r=Z.polygon(e,S,t);return r};return a}(0,s.K2)(ft,"curvedTrapezoid");var pt=(0,s.K2)(((t,e,r,i,a,n)=>[`M${t},${e+n}`,`a${a},${n} 0,0,0 ${r},0`,`a${a},${n} 0,0,0 ${-r},0`,`l0,${i}`,`a${a},${n} 0,0,0 ${r},0`,`l0,${-i}`].join(" ")),"createCylinderPathD");var gt=(0,s.K2)(((t,e,r,i,a,n)=>[`M${t},${e+n}`,`M${t+r},${e+n}`,`a${a},${n} 0,0,0 ${-r},0`,`l0,${i}`,`a${a},${n} 0,0,0 ${r},0`,`l0,${-i}`].join(" ")),"createOuterCylinderPathD");var mt=(0,s.K2)(((t,e,r,i,a,n)=>[`M${t-r/2},${-i/2}`,`a${a},${n} 0,0,0 ${r},0`].join(" ")),"createInnerCylinderPathD");async function yt(t,e){const{labelStyles:r,nodeStyles:i}=L(e);e.labelStyle=r;const{shapeSvg:a,bbox:n,label:s}=await h(t,e,f(e));const l=Math.max(n.width+e.padding,e.width??0);const d=l/2;const p=d/(2.5+l/50);const g=Math.max(n.height+p+e.padding,e.height??0);let m;const{cssStyles:y}=e;if(e.look==="handDrawn"){const t=c.A.svg(a);const r=gt(0,0,l,g,d,p);const i=mt(0,p,l,g,d,p);const n=t.path(r,M(e,{}));const o=t.path(i,M(e,{fill:"none"}));m=a.insert((()=>o),":first-child");m=a.insert((()=>n),":first-child");m.attr("class","basic label-container");if(y){m.attr("style",y)}}else{const t=pt(0,0,l,g,d,p);m=a.insert("path",":first-child").attr("d",t).attr("class","basic label-container").attr("style",(0,o.KL)(y)).attr("style",i)}m.attr("label-offset-y",p);m.attr("transform",`translate(${-l/2}, ${-(g/2+p)})`);u(e,m);s.attr("transform",`translate(${-(n.width/2)-(n.x-(n.left??0))}, ${-(n.height/2)+(e.padding??0)/1.5-(n.y-(n.top??0))})`);e.intersect=function(t){const r=Z.rect(e,t);const i=r.x-(e.x??0);if(d!=0&&(Math.abs(i)<(e.width??0)/2||Math.abs(i)==(e.width??0)/2&&Math.abs(r.y-(e.y??0))>(e.height??0)/2-p)){let a=p*p*(1-i*i/(d*d));if(a>0){a=Math.sqrt(a)}a=p-a;if(t.y-(e.y??0)>0){a=-a}r.y+=a}return r};return a}(0,s.K2)(yt,"cylinder");async function bt(t,e){const{labelStyles:r,nodeStyles:i}=L(e);e.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await h(t,e,f(e));const s=n.width+e.padding;const l=n.height+e.padding;const d=l*.2;const p=-s/2;const g=-l/2-d/2;const{cssStyles:m}=e;const y=c.A.svg(a);const b=M(e,{});if(e.look!=="handDrawn"){b.roughness=0;b.fillStyle="solid"}const x=[{x:p,y:g+d},{x:-p,y:g+d},{x:-p,y:-g},{x:p,y:-g},{x:p,y:g},{x:-p,y:g},{x:-p,y:g+d}];const C=y.polygon(x.map((t=>[t.x,t.y])),b);const v=a.insert((()=>C),":first-child");v.attr("class","basic label-container");if(m&&e.look!=="handDrawn"){v.selectAll("path").attr("style",m)}if(i&&e.look!=="handDrawn"){v.selectAll("path").attr("style",i)}o.attr("transform",`translate(${p+(e.padding??0)/2-(n.x-(n.left??0))}, ${g+d+(e.padding??0)/2-(n.y-(n.top??0))})`);u(e,v);e.intersect=function(t){const r=Z.rect(e,t);return r};return a}(0,s.K2)(bt,"dividedRectangle");async function xt(t,e){const{labelStyles:r,nodeStyles:i}=L(e);e.labelStyle=r;const{shapeSvg:a,bbox:n,halfPadding:l}=await h(t,e,f(e));const d=5;const p=n.width/2+l+d;const g=n.width/2+l;let m;const{cssStyles:y}=e;if(e.look==="handDrawn"){const t=c.A.svg(a);const r=M(e,{roughness:.2,strokeWidth:2.5});const i=M(e,{roughness:.2,strokeWidth:1.5});const n=t.circle(0,0,p*2,r);const s=t.circle(0,0,g*2,i);m=a.insert("g",":first-child");m.attr("class",(0,o.KL)(e.cssClasses)).attr("style",(0,o.KL)(y));m.node()?.appendChild(n);m.node()?.appendChild(s)}else{m=a.insert("g",":first-child");const t=m.insert("circle",":first-child");const e=m.insert("circle");m.attr("class","basic label-container").attr("style",i);t.attr("class","outer-circle").attr("style",i).attr("r",p).attr("cx",0).attr("cy",0);e.attr("class","inner-circle").attr("style",i).attr("r",g).attr("cx",0).attr("cy",0)}u(e,m);e.intersect=function(t){s.Rm.info("DoubleCircle intersect",e,p,t);return Z.circle(e,p,t)};return a}(0,s.K2)(xt,"doublecircle");function Ct(t,e,{config:{themeVariables:r}}){const{labelStyles:i,nodeStyles:a}=L(e);e.label="";e.labelStyle=i;const n=t.insert("g").attr("class",f(e)).attr("id",e.domId??e.id);const o=7;const{cssStyles:l}=e;const h=c.A.svg(n);const{nodeBorder:d}=r;const p=M(e,{fillStyle:"solid"});if(e.look!=="handDrawn"){p.roughness=0}const g=h.circle(0,0,o*2,p);const m=n.insert((()=>g),":first-child");m.selectAll("path").attr("style",`fill: ${d} !important;`);if(l&&l.length>0&&e.look!=="handDrawn"){m.selectAll("path").attr("style",l)}if(a&&e.look!=="handDrawn"){m.selectAll("path").attr("style",a)}u(e,m);e.intersect=function(t){s.Rm.info("filledCircle intersect",e,{radius:o,point:t});const r=Z.circle(e,o,t);return r};return n}(0,s.K2)(Ct,"filledCircle");async function vt(t,e){const{labelStyles:r,nodeStyles:i}=L(e);e.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await h(t,e,f(e));const l=n.width+(e.padding??0);const d=l+n.height;const g=l+n.height;const m=[{x:0,y:-d},{x:g,y:-d},{x:g/2,y:0}];const{cssStyles:y}=e;const b=c.A.svg(a);const x=M(e,{});if(e.look!=="handDrawn"){x.roughness=0;x.fillStyle="solid"}const C=p(m);const v=b.path(C,x);const k=a.insert((()=>v),":first-child").attr("transform",`translate(${-d/2}, ${d/2})`);if(y&&e.look!=="handDrawn"){k.selectChildren("path").attr("style",y)}if(i&&e.look!=="handDrawn"){k.selectChildren("path").attr("style",i)}e.width=l;e.height=d;u(e,k);o.attr("transform",`translate(${-n.width/2-(n.x-(n.left??0))}, ${-d/2+(e.padding??0)/2+(n.y-(n.top??0))})`);e.intersect=function(t){s.Rm.info("Triangle intersect",e,m,t);return Z.polygon(e,m,t)};return a}(0,s.K2)(vt,"flippedTriangle");function kt(t,e,{dir:r,config:{state:i,themeVariables:a}}){const{nodeStyles:n}=L(e);e.label="";const o=t.insert("g").attr("class",f(e)).attr("id",e.domId??e.id);const{cssStyles:s}=e;let l=Math.max(70,e?.width??0);let h=Math.max(10,e?.height??0);if(r==="LR"){l=Math.max(10,e?.width??0);h=Math.max(70,e?.height??0)}const d=-1*l/2;const p=-1*h/2;const g=c.A.svg(o);const m=M(e,{stroke:a.lineColor,fill:a.lineColor});if(e.look!=="handDrawn"){m.roughness=0;m.fillStyle="solid"}const y=g.rectangle(d,p,l,h,m);const b=o.insert((()=>y),":first-child");if(s&&e.look!=="handDrawn"){b.selectAll("path").attr("style",s)}if(n&&e.look!=="handDrawn"){b.selectAll("path").attr("style",n)}u(e,b);const x=i?.padding??0;if(e.width&&e.height){e.width+=x/2||0;e.height+=x/2||0}e.intersect=function(t){return Z.rect(e,t)};return o}(0,s.K2)(kt,"forkJoin");async function wt(t,e){const{labelStyles:r,nodeStyles:i}=L(e);e.labelStyle=r;const a=80,n=50;const{shapeSvg:o,bbox:l}=await h(t,e,f(e));const d=Math.max(a,l.width+(e.padding??0)*2,e?.width??0);const g=Math.max(n,l.height+(e.padding??0)*2,e?.height??0);const y=g/2;const{cssStyles:b}=e;const x=c.A.svg(o);const C=M(e,{});if(e.look!=="handDrawn"){C.roughness=0;C.fillStyle="solid"}const v=[{x:-d/2,y:-g/2},{x:d/2-y,y:-g/2},...m(-d/2+y,0,y,50,90,270),{x:d/2-y,y:g/2},{x:-d/2,y:g/2}];const k=p(v);const w=x.path(k,C);const S=o.insert((()=>w),":first-child");S.attr("class","basic label-container");if(b&&e.look!=="handDrawn"){S.selectChildren("path").attr("style",b)}if(i&&e.look!=="handDrawn"){S.selectChildren("path").attr("style",i)}u(e,S);e.intersect=function(t){s.Rm.info("Pill intersect",e,{radius:y,point:t});const r=Z.polygon(e,v,t);return r};return o}(0,s.K2)(wt,"halfRoundedRectangle");var St=(0,s.K2)(((t,e,r,i,a)=>[`M${t+a},${e}`,`L${t+r-a},${e}`,`L${t+r},${e-i/2}`,`L${t+r-a},${e-i}`,`L${t+a},${e-i}`,`L${t},${e-i/2}`,"Z"].join(" ")),"createHexagonPathD");async function At(t,e){const{labelStyles:r,nodeStyles:i}=L(e);e.labelStyle=r;const{shapeSvg:a,bbox:n}=await h(t,e,f(e));const o=4;const s=n.height+e.padding;const l=s/o;const d=n.width+2*l+e.padding;const p=[{x:l,y:0},{x:d-l,y:0},{x:d,y:-s/2},{x:d-l,y:-s},{x:l,y:-s},{x:0,y:-s/2}];let g;const{cssStyles:m}=e;if(e.look==="handDrawn"){const t=c.A.svg(a);const r=M(e,{});const i=St(0,0,d,s,l);const n=t.path(i,r);g=a.insert((()=>n),":first-child").attr("transform",`translate(${-d/2}, ${s/2})`);if(m){g.attr("style",m)}}else{g=et(a,d,s,p)}if(i){g.attr("style",i)}e.width=d;e.height=s;u(e,g);e.intersect=function(t){return Z.polygon(e,p,t)};return a}(0,s.K2)(At,"hexagon");async function Tt(t,e){const{labelStyles:r,nodeStyles:i}=L(e);e.label="";e.labelStyle=r;const{shapeSvg:a}=await h(t,e,f(e));const n=Math.max(30,e?.width??0);const o=Math.max(30,e?.height??0);const{cssStyles:l}=e;const d=c.A.svg(a);const g=M(e,{});if(e.look!=="handDrawn"){g.roughness=0;g.fillStyle="solid"}const m=[{x:0,y:0},{x:n,y:0},{x:0,y:o},{x:n,y:o}];const y=p(m);const b=d.path(y,g);const x=a.insert((()=>b),":first-child");x.attr("class","basic label-container");if(l&&e.look!=="handDrawn"){x.selectChildren("path").attr("style",l)}if(i&&e.look!=="handDrawn"){x.selectChildren("path").attr("style",i)}x.attr("transform",`translate(${-n/2}, ${-o/2})`);u(e,x);e.intersect=function(t){s.Rm.info("Pill intersect",e,{points:m});const r=Z.polygon(e,m,t);return r};return a}(0,s.K2)(Tt,"hourglass");async function Bt(t,e,{config:{themeVariables:r,flowchart:i}}){const{labelStyles:n}=L(e);e.labelStyle=n;const o=e.assetHeight??48;const l=e.assetWidth??48;const d=Math.max(o,l);const f=i?.wrappingWidth;e.width=Math.max(d,f??0);const{shapeSvg:p,bbox:g,label:m}=await h(t,e,"icon-shape default");const y=e.pos==="t";const b=d;const x=d;const{nodeBorder:C}=r;const{stylesMap:v}=A(e);const k=-x/2;const w=-b/2;const S=e.label?8:0;const T=c.A.svg(p);const B=M(e,{stroke:"none",fill:"none"});if(e.look!=="handDrawn"){B.roughness=0;B.fillStyle="solid"}const _=T.rectangle(k,w,x,b,B);const F=Math.max(x,g.width);const $=b+g.height+S;const E=T.rectangle(-F/2,-$/2,F,$,{...B,fill:"transparent",stroke:"none"});const O=p.insert((()=>_),":first-child");const D=p.insert((()=>E));if(e.icon){const t=p.append("g");t.html(`<g>${await(0,a.WY)(e.icon,{height:d,width:d,fallbackPrefix:""})}</g>`);const r=t.node().getBBox();const i=r.width;const n=r.height;const o=r.x;const s=r.y;t.attr("transform",`translate(${-i/2-o},${y?g.height/2+S/2-n/2-s:-g.height/2-S/2-n/2-s})`);t.attr("style",`color: ${v.get("stroke")??C};`)}m.attr("transform",`translate(${-g.width/2-(g.x-(g.left??0))},${y?-$/2:$/2-g.height})`);O.attr("transform",`translate(${0},${y?g.height/2+S/2:-g.height/2-S/2})`);u(e,D);e.intersect=function(t){s.Rm.info("iconSquare intersect",e,t);if(!e.label){return Z.rect(e,t)}const r=e.x??0;const i=e.y??0;const a=e.height??0;let n=[];if(y){n=[{x:r-g.width/2,y:i-a/2},{x:r+g.width/2,y:i-a/2},{x:r+g.width/2,y:i-a/2+g.height+S},{x:r+x/2,y:i-a/2+g.height+S},{x:r+x/2,y:i+a/2},{x:r-x/2,y:i+a/2},{x:r-x/2,y:i-a/2+g.height+S},{x:r-g.width/2,y:i-a/2+g.height+S}]}else{n=[{x:r-x/2,y:i-a/2},{x:r+x/2,y:i-a/2},{x:r+x/2,y:i-a/2+b},{x:r+g.width/2,y:i-a/2+b},{x:r+g.width/2/2,y:i+a/2},{x:r-g.width/2,y:i+a/2},{x:r-g.width/2,y:i-a/2+b},{x:r-x/2,y:i-a/2+b}]}const o=Z.polygon(e,n,t);return o};return p}(0,s.K2)(Bt,"icon");async function Lt(t,e,{config:{themeVariables:r,flowchart:i}}){const{labelStyles:n}=L(e);e.labelStyle=n;const o=e.assetHeight??48;const l=e.assetWidth??48;const d=Math.max(o,l);const f=i?.wrappingWidth;e.width=Math.max(d,f??0);const{shapeSvg:p,bbox:g,label:m}=await h(t,e,"icon-shape default");const y=20;const b=e.label?8:0;const x=e.pos==="t";const{nodeBorder:C,mainBkg:v}=r;const{stylesMap:k}=A(e);const w=c.A.svg(p);const S=M(e,{});if(e.look!=="handDrawn"){S.roughness=0;S.fillStyle="solid"}const T=k.get("fill");S.stroke=T??v;const B=p.append("g");if(e.icon){B.html(`<g>${await(0,a.WY)(e.icon,{height:d,width:d,fallbackPrefix:""})}</g>`)}const _=B.node().getBBox();const F=_.width;const $=_.height;const E=_.x;const O=_.y;const D=Math.max(F,$)*Math.SQRT2+y*2;const I=w.circle(0,0,D,S);const K=Math.max(D,g.width);const R=D+g.height+b;const P=w.rectangle(-K/2,-R/2,K,R,{...S,fill:"transparent",stroke:"none"});const z=p.insert((()=>I),":first-child");const q=p.insert((()=>P));B.attr("transform",`translate(${-F/2-E},${x?g.height/2+b/2-$/2-O:-g.height/2-b/2-$/2-O})`);B.attr("style",`color: ${k.get("stroke")??C};`);m.attr("transform",`translate(${-g.width/2-(g.x-(g.left??0))},${x?-R/2:R/2-g.height})`);z.attr("transform",`translate(${0},${x?g.height/2+b/2:-g.height/2-b/2})`);u(e,q);e.intersect=function(t){s.Rm.info("iconSquare intersect",e,t);const r=Z.rect(e,t);return r};return p}(0,s.K2)(Lt,"iconCircle");async function Mt(t,e,{config:{themeVariables:r,flowchart:i}}){const{labelStyles:n}=L(e);e.labelStyle=n;const o=e.assetHeight??48;const l=e.assetWidth??48;const d=Math.max(o,l);const f=i?.wrappingWidth;e.width=Math.max(d,f??0);const{shapeSvg:p,bbox:g,halfPadding:m,label:y}=await h(t,e,"icon-shape default");const b=e.pos==="t";const x=d+m*2;const C=d+m*2;const{nodeBorder:v,mainBkg:k}=r;const{stylesMap:S}=A(e);const T=-C/2;const B=-x/2;const _=e.label?8:0;const F=c.A.svg(p);const $=M(e,{});if(e.look!=="handDrawn"){$.roughness=0;$.fillStyle="solid"}const E=S.get("fill");$.stroke=E??k;const O=F.path(w(T,B,C,x,5),$);const D=Math.max(C,g.width);const I=x+g.height+_;const K=F.rectangle(-D/2,-I/2,D,I,{...$,fill:"transparent",stroke:"none"});const R=p.insert((()=>O),":first-child").attr("class","icon-shape2");const P=p.insert((()=>K));if(e.icon){const t=p.append("g");t.html(`<g>${await(0,a.WY)(e.icon,{height:d,width:d,fallbackPrefix:""})}</g>`);const r=t.node().getBBox();const i=r.width;const n=r.height;const o=r.x;const s=r.y;t.attr("transform",`translate(${-i/2-o},${b?g.height/2+_/2-n/2-s:-g.height/2-_/2-n/2-s})`);t.attr("style",`color: ${S.get("stroke")??v};`)}y.attr("transform",`translate(${-g.width/2-(g.x-(g.left??0))},${b?-I/2:I/2-g.height})`);R.attr("transform",`translate(${0},${b?g.height/2+_/2:-g.height/2-_/2})`);u(e,P);e.intersect=function(t){s.Rm.info("iconSquare intersect",e,t);if(!e.label){return Z.rect(e,t)}const r=e.x??0;const i=e.y??0;const a=e.height??0;let n=[];if(b){n=[{x:r-g.width/2,y:i-a/2},{x:r+g.width/2,y:i-a/2},{x:r+g.width/2,y:i-a/2+g.height+_},{x:r+C/2,y:i-a/2+g.height+_},{x:r+C/2,y:i+a/2},{x:r-C/2,y:i+a/2},{x:r-C/2,y:i-a/2+g.height+_},{x:r-g.width/2,y:i-a/2+g.height+_}]}else{n=[{x:r-C/2,y:i-a/2},{x:r+C/2,y:i-a/2},{x:r+C/2,y:i-a/2+x},{x:r+g.width/2,y:i-a/2+x},{x:r+g.width/2/2,y:i+a/2},{x:r-g.width/2,y:i+a/2},{x:r-g.width/2,y:i-a/2+x},{x:r-C/2,y:i-a/2+x}]}const o=Z.polygon(e,n,t);return o};return p}(0,s.K2)(Mt,"iconRounded");async function _t(t,e,{config:{themeVariables:r,flowchart:i}}){const{labelStyles:n}=L(e);e.labelStyle=n;const o=e.assetHeight??48;const l=e.assetWidth??48;const d=Math.max(o,l);const f=i?.wrappingWidth;e.width=Math.max(d,f??0);const{shapeSvg:p,bbox:g,halfPadding:m,label:y}=await h(t,e,"icon-shape default");const b=e.pos==="t";const x=d+m*2;const C=d+m*2;const{nodeBorder:v,mainBkg:k}=r;const{stylesMap:S}=A(e);const T=-C/2;const B=-x/2;const _=e.label?8:0;const F=c.A.svg(p);const $=M(e,{});if(e.look!=="handDrawn"){$.roughness=0;$.fillStyle="solid"}const E=S.get("fill");$.stroke=E??k;const O=F.path(w(T,B,C,x,.1),$);const D=Math.max(C,g.width);const I=x+g.height+_;const K=F.rectangle(-D/2,-I/2,D,I,{...$,fill:"transparent",stroke:"none"});const R=p.insert((()=>O),":first-child");const P=p.insert((()=>K));if(e.icon){const t=p.append("g");t.html(`<g>${await(0,a.WY)(e.icon,{height:d,width:d,fallbackPrefix:""})}</g>`);const r=t.node().getBBox();const i=r.width;const n=r.height;const o=r.x;const s=r.y;t.attr("transform",`translate(${-i/2-o},${b?g.height/2+_/2-n/2-s:-g.height/2-_/2-n/2-s})`);t.attr("style",`color: ${S.get("stroke")??v};`)}y.attr("transform",`translate(${-g.width/2-(g.x-(g.left??0))},${b?-I/2:I/2-g.height})`);R.attr("transform",`translate(${0},${b?g.height/2+_/2:-g.height/2-_/2})`);u(e,P);e.intersect=function(t){s.Rm.info("iconSquare intersect",e,t);if(!e.label){return Z.rect(e,t)}const r=e.x??0;const i=e.y??0;const a=e.height??0;let n=[];if(b){n=[{x:r-g.width/2,y:i-a/2},{x:r+g.width/2,y:i-a/2},{x:r+g.width/2,y:i-a/2+g.height+_},{x:r+C/2,y:i-a/2+g.height+_},{x:r+C/2,y:i+a/2},{x:r-C/2,y:i+a/2},{x:r-C/2,y:i-a/2+g.height+_},{x:r-g.width/2,y:i-a/2+g.height+_}]}else{n=[{x:r-C/2,y:i-a/2},{x:r+C/2,y:i-a/2},{x:r+C/2,y:i-a/2+x},{x:r+g.width/2,y:i-a/2+x},{x:r+g.width/2/2,y:i+a/2},{x:r-g.width/2,y:i+a/2},{x:r-g.width/2,y:i-a/2+x},{x:r-C/2,y:i-a/2+x}]}const o=Z.polygon(e,n,t);return o};return p}(0,s.K2)(_t,"iconSquare");async function Ft(t,e,{config:{flowchart:r}}){const i=new Image;i.src=e?.img??"";await i.decode();const a=Number(i.naturalWidth.toString().replace("px",""));const n=Number(i.naturalHeight.toString().replace("px",""));e.imageAspectRatio=a/n;const{labelStyles:o}=L(e);e.labelStyle=o;const l=r?.wrappingWidth;e.defaultWidth=r?.wrappingWidth;const d=Math.max(e.label?l??0:0,e?.assetWidth??a);const f=e.constraint==="on"?e?.assetHeight?e.assetHeight*e.imageAspectRatio:d:d;const p=e.constraint==="on"?f/e.imageAspectRatio:e?.assetHeight??n;e.width=Math.max(f,l??0);const{shapeSvg:g,bbox:m,label:y}=await h(t,e,"image-shape default");const b=e.pos==="t";const x=-f/2;const C=-p/2;const v=e.label?8:0;const k=c.A.svg(g);const w=M(e,{});if(e.look!=="handDrawn"){w.roughness=0;w.fillStyle="solid"}const S=k.rectangle(x,C,f,p,w);const A=Math.max(f,m.width);const T=p+m.height+v;const B=k.rectangle(-A/2,-T/2,A,T,{...w,fill:"none",stroke:"none"});const _=g.insert((()=>S),":first-child");const F=g.insert((()=>B));if(e.img){const t=g.append("image");t.attr("href",e.img);t.attr("width",f);t.attr("height",p);t.attr("preserveAspectRatio","none");t.attr("transform",`translate(${-f/2},${b?T/2-p:-T/2})`)}y.attr("transform",`translate(${-m.width/2-(m.x-(m.left??0))},${b?-p/2-m.height/2-v/2:p/2-m.height/2+v/2})`);_.attr("transform",`translate(${0},${b?m.height/2+v/2:-m.height/2-v/2})`);u(e,F);e.intersect=function(t){s.Rm.info("iconSquare intersect",e,t);if(!e.label){return Z.rect(e,t)}const r=e.x??0;const i=e.y??0;const a=e.height??0;let n=[];if(b){n=[{x:r-m.width/2,y:i-a/2},{x:r+m.width/2,y:i-a/2},{x:r+m.width/2,y:i-a/2+m.height+v},{x:r+f/2,y:i-a/2+m.height+v},{x:r+f/2,y:i+a/2},{x:r-f/2,y:i+a/2},{x:r-f/2,y:i-a/2+m.height+v},{x:r-m.width/2,y:i-a/2+m.height+v}]}else{n=[{x:r-f/2,y:i-a/2},{x:r+f/2,y:i-a/2},{x:r+f/2,y:i-a/2+p},{x:r+m.width/2,y:i-a/2+p},{x:r+m.width/2/2,y:i+a/2},{x:r-m.width/2,y:i+a/2},{x:r-m.width/2,y:i-a/2+p},{x:r-f/2,y:i-a/2+p}]}const o=Z.polygon(e,n,t);return o};return g}(0,s.K2)(Ft,"imageSquare");async function $t(t,e){const{labelStyles:r,nodeStyles:i}=L(e);e.labelStyle=r;const{shapeSvg:a,bbox:n}=await h(t,e,f(e));const o=Math.max(n.width+(e.padding??0)*2,e?.width??0);const s=Math.max(n.height+(e.padding??0)*2,e?.height??0);const l=[{x:0,y:0},{x:o,y:0},{x:o+3*s/6,y:-s},{x:-3*s/6,y:-s}];let d;const{cssStyles:g}=e;if(e.look==="handDrawn"){const t=c.A.svg(a);const r=M(e,{});const i=p(l);const n=t.path(i,r);d=a.insert((()=>n),":first-child").attr("transform",`translate(${-o/2}, ${s/2})`);if(g){d.attr("style",g)}}else{d=et(a,o,s,l)}if(i){d.attr("style",i)}e.width=o;e.height=s;u(e,d);e.intersect=function(t){return Z.polygon(e,l,t)};return a}(0,s.K2)($t,"inv_trapezoid");async function Et(t,e,r){const{labelStyles:i,nodeStyles:a}=L(e);e.labelStyle=i;const{shapeSvg:n,bbox:s}=await h(t,e,f(e));const l=Math.max(s.width+r.labelPaddingX*2,e?.width||0);const d=Math.max(s.height+r.labelPaddingY*2,e?.height||0);const p=-l/2;const g=-d/2;let m;let{rx:y,ry:b}=e;const{cssStyles:x}=e;if(r?.rx&&r.ry){y=r.rx;b=r.ry}if(e.look==="handDrawn"){const t=c.A.svg(n);const r=M(e,{});const i=y||b?t.path(w(p,g,l,d,y||0),r):t.rectangle(p,g,l,d,r);m=n.insert((()=>i),":first-child");m.attr("class","basic label-container").attr("style",(0,o.KL)(x))}else{m=n.insert("rect",":first-child");m.attr("class","basic label-container").attr("style",a).attr("rx",(0,o.KL)(y)).attr("ry",(0,o.KL)(b)).attr("x",p).attr("y",g).attr("width",l).attr("height",d)}u(e,m);e.intersect=function(t){return Z.rect(e,t)};return n}(0,s.K2)(Et,"drawRect");async function Ot(t,e){const{shapeSvg:r,bbox:i,label:a}=await h(t,e,"label");const n=r.insert("rect",":first-child");const o=.1;const s=.1;n.attr("width",o).attr("height",s);r.attr("class","label edgeLabel");a.attr("transform",`translate(${-(i.width/2)-(i.x-(i.left??0))}, ${-(i.height/2)-(i.y-(i.top??0))})`);u(e,n);e.intersect=function(t){return Z.rect(e,t)};return r}(0,s.K2)(Ot,"labelRect");async function Dt(t,e){const{labelStyles:r,nodeStyles:i}=L(e);e.labelStyle=r;const{shapeSvg:a,bbox:n}=await h(t,e,f(e));const o=Math.max(n.width+(e.padding??0),e?.width??0);const s=Math.max(n.height+(e.padding??0),e?.height??0);const l=[{x:0,y:0},{x:o+3*s/6,y:0},{x:o,y:-s},{x:-(3*s)/6,y:-s}];let d;const{cssStyles:g}=e;if(e.look==="handDrawn"){const t=c.A.svg(a);const r=M(e,{});const i=p(l);const n=t.path(i,r);d=a.insert((()=>n),":first-child").attr("transform",`translate(${-o/2}, ${s/2})`);if(g){d.attr("style",g)}}else{d=et(a,o,s,l)}if(i){d.attr("style",i)}e.width=o;e.height=s;u(e,d);e.intersect=function(t){return Z.polygon(e,l,t)};return a}(0,s.K2)(Dt,"lean_left");async function It(t,e){const{labelStyles:r,nodeStyles:i}=L(e);e.labelStyle=r;const{shapeSvg:a,bbox:n}=await h(t,e,f(e));const o=Math.max(n.width+(e.padding??0),e?.width??0);const s=Math.max(n.height+(e.padding??0),e?.height??0);const l=[{x:-3*s/6,y:0},{x:o,y:0},{x:o+3*s/6,y:-s},{x:0,y:-s}];let d;const{cssStyles:g}=e;if(e.look==="handDrawn"){const t=c.A.svg(a);const r=M(e,{});const i=p(l);const n=t.path(i,r);d=a.insert((()=>n),":first-child").attr("transform",`translate(${-o/2}, ${s/2})`);if(g){d.attr("style",g)}}else{d=et(a,o,s,l)}if(i){d.attr("style",i)}e.width=o;e.height=s;u(e,d);e.intersect=function(t){return Z.polygon(e,l,t)};return a}(0,s.K2)(It,"lean_right");function Kt(t,e){const{labelStyles:r,nodeStyles:i}=L(e);e.label="";e.labelStyle=r;const a=t.insert("g").attr("class",f(e)).attr("id",e.domId??e.id);const{cssStyles:n}=e;const o=Math.max(35,e?.width??0);const l=Math.max(35,e?.height??0);const h=7;const d=[{x:o,y:0},{x:0,y:l+h/2},{x:o-2*h,y:l+h/2},{x:0,y:2*l},{x:o,y:l-h/2},{x:2*h,y:l-h/2}];const g=c.A.svg(a);const m=M(e,{});if(e.look!=="handDrawn"){m.roughness=0;m.fillStyle="solid"}const y=p(d);const b=g.path(y,m);const x=a.insert((()=>b),":first-child");if(n&&e.look!=="handDrawn"){x.selectAll("path").attr("style",n)}if(i&&e.look!=="handDrawn"){x.selectAll("path").attr("style",i)}x.attr("transform",`translate(-${o/2},${-l})`);u(e,x);e.intersect=function(t){s.Rm.info("lightningBolt intersect",e,t);const r=Z.polygon(e,d,t);return r};return a}(0,s.K2)(Kt,"lightningBolt");var Rt=(0,s.K2)(((t,e,r,i,a,n,o)=>[`M${t},${e+n}`,`a${a},${n} 0,0,0 ${r},0`,`a${a},${n} 0,0,0 ${-r},0`,`l0,${i}`,`a${a},${n} 0,0,0 ${r},0`,`l0,${-i}`,`M${t},${e+n+o}`,`a${a},${n} 0,0,0 ${r},0`].join(" ")),"createCylinderPathD");var Pt=(0,s.K2)(((t,e,r,i,a,n,o)=>[`M${t},${e+n}`,`M${t+r},${e+n}`,`a${a},${n} 0,0,0 ${-r},0`,`l0,${i}`,`a${a},${n} 0,0,0 ${r},0`,`l0,${-i}`,`M${t},${e+n+o}`,`a${a},${n} 0,0,0 ${r},0`].join(" ")),"createOuterCylinderPathD");var zt=(0,s.K2)(((t,e,r,i,a,n)=>[`M${t-r/2},${-i/2}`,`a${a},${n} 0,0,0 ${r},0`].join(" ")),"createInnerCylinderPathD");async function qt(t,e){const{labelStyles:r,nodeStyles:i}=L(e);e.labelStyle=r;const{shapeSvg:a,bbox:n,label:s}=await h(t,e,f(e));const l=Math.max(n.width+(e.padding??0),e.width??0);const d=l/2;const p=d/(2.5+l/50);const g=Math.max(n.height+p+(e.padding??0),e.height??0);const m=g*.1;let y;const{cssStyles:b}=e;if(e.look==="handDrawn"){const t=c.A.svg(a);const r=Pt(0,0,l,g,d,p,m);const i=zt(0,p,l,g,d,p);const n=M(e,{});const o=t.path(r,n);const s=t.path(i,n);const h=a.insert((()=>s),":first-child");h.attr("class","line");y=a.insert((()=>o),":first-child");y.attr("class","basic label-container");if(b){y.attr("style",b)}}else{const t=Rt(0,0,l,g,d,p,m);y=a.insert("path",":first-child").attr("d",t).attr("class","basic label-container").attr("style",(0,o.KL)(b)).attr("style",i)}y.attr("label-offset-y",p);y.attr("transform",`translate(${-l/2}, ${-(g/2+p)})`);u(e,y);s.attr("transform",`translate(${-(n.width/2)-(n.x-(n.left??0))}, ${-(n.height/2)+p-(n.y-(n.top??0))})`);e.intersect=function(t){const r=Z.rect(e,t);const i=r.x-(e.x??0);if(d!=0&&(Math.abs(i)<(e.width??0)/2||Math.abs(i)==(e.width??0)/2&&Math.abs(r.y-(e.y??0))>(e.height??0)/2-p)){let a=p*p*(1-i*i/(d*d));if(a>0){a=Math.sqrt(a)}a=p-a;if(t.y-(e.y??0)>0){a=-a}r.y+=a}return r};return a}(0,s.K2)(qt,"linedCylinder");async function Nt(t,e){const{labelStyles:r,nodeStyles:i}=L(e);e.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await h(t,e,f(e));const s=Math.max(n.width+(e.padding??0)*2,e?.width??0);const l=Math.max(n.height+(e.padding??0)*2,e?.height??0);const d=l/4;const p=l+d;const{cssStyles:m}=e;const y=c.A.svg(a);const b=M(e,{});if(e.look!=="handDrawn"){b.roughness=0;b.fillStyle="solid"}const x=[{x:-s/2-s/2*.1,y:-p/2},{x:-s/2-s/2*.1,y:p/2},...g(-s/2-s/2*.1,p/2,s/2+s/2*.1,p/2,d,.8),{x:s/2+s/2*.1,y:-p/2},{x:-s/2-s/2*.1,y:-p/2},{x:-s/2,y:-p/2},{x:-s/2,y:p/2*1.1},{x:-s/2,y:-p/2}];const C=y.polygon(x.map((t=>[t.x,t.y])),b);const v=a.insert((()=>C),":first-child");v.attr("class","basic label-container");if(m&&e.look!=="handDrawn"){v.selectAll("path").attr("style",m)}if(i&&e.look!=="handDrawn"){v.selectAll("path").attr("style",i)}v.attr("transform",`translate(0,${-d/2})`);o.attr("transform",`translate(${-s/2+(e.padding??0)+s/2*.1/2-(n.x-(n.left??0))},${-l/2+(e.padding??0)-d/2-(n.y-(n.top??0))})`);u(e,v);e.intersect=function(t){const r=Z.polygon(e,x,t);return r};return a}(0,s.K2)(Nt,"linedWaveEdgedRect");async function Wt(t,e){const{labelStyles:r,nodeStyles:i}=L(e);e.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await h(t,e,f(e));const s=Math.max(n.width+(e.padding??0)*2,e?.width??0);const l=Math.max(n.height+(e.padding??0)*2,e?.height??0);const d=5;const g=-s/2;const m=-l/2;const{cssStyles:y}=e;const b=c.A.svg(a);const x=M(e,{});const C=[{x:g-d,y:m+d},{x:g-d,y:m+l+d},{x:g+s-d,y:m+l+d},{x:g+s-d,y:m+l},{x:g+s,y:m+l},{x:g+s,y:m+l-d},{x:g+s+d,y:m+l-d},{x:g+s+d,y:m-d},{x:g+d,y:m-d},{x:g+d,y:m},{x:g,y:m},{x:g,y:m+d}];const v=[{x:g,y:m+d},{x:g+s-d,y:m+d},{x:g+s-d,y:m+l},{x:g+s,y:m+l},{x:g+s,y:m},{x:g,y:m}];if(e.look!=="handDrawn"){x.roughness=0;x.fillStyle="solid"}const k=p(C);const w=b.path(k,x);const S=p(v);const A=b.path(S,{...x,fill:"none"});const T=a.insert((()=>A),":first-child");T.insert((()=>w),":first-child");T.attr("class","basic label-container");if(y&&e.look!=="handDrawn"){T.selectAll("path").attr("style",y)}if(i&&e.look!=="handDrawn"){T.selectAll("path").attr("style",i)}o.attr("transform",`translate(${-(n.width/2)-d-(n.x-(n.left??0))}, ${-(n.height/2)+d-(n.y-(n.top??0))})`);u(e,T);e.intersect=function(t){const r=Z.polygon(e,C,t);return r};return a}(0,s.K2)(Wt,"multiRect");async function jt(t,e){const{labelStyles:r,nodeStyles:i}=L(e);e.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await h(t,e,f(e));const s=Math.max(n.width+(e.padding??0)*2,e?.width??0);const l=Math.max(n.height+(e.padding??0)*2,e?.height??0);const d=l/4;const m=l+d;const y=-s/2;const b=-m/2;const x=5;const{cssStyles:C}=e;const v=g(y-x,b+m+x,y+s-x,b+m+x,d,.8);const k=v?.[v.length-1];const w=[{x:y-x,y:b+x},{x:y-x,y:b+m+x},...v,{x:y+s-x,y:k.y-x},{x:y+s,y:k.y-x},{x:y+s,y:k.y-2*x},{x:y+s+x,y:k.y-2*x},{x:y+s+x,y:b-x},{x:y+x,y:b-x},{x:y+x,y:b},{x:y,y:b},{x:y,y:b+x}];const S=[{x:y,y:b+x},{x:y+s-x,y:b+x},{x:y+s-x,y:k.y-x},{x:y+s,y:k.y-x},{x:y+s,y:b},{x:y,y:b}];const A=c.A.svg(a);const T=M(e,{});if(e.look!=="handDrawn"){T.roughness=0;T.fillStyle="solid"}const B=p(w);const _=A.path(B,T);const F=p(S);const $=A.path(F,T);const E=a.insert((()=>_),":first-child");E.insert((()=>$));E.attr("class","basic label-container");if(C&&e.look!=="handDrawn"){E.selectAll("path").attr("style",C)}if(i&&e.look!=="handDrawn"){E.selectAll("path").attr("style",i)}E.attr("transform",`translate(0,${-d/2})`);o.attr("transform",`translate(${-(n.width/2)-x-(n.x-(n.left??0))}, ${-(n.height/2)+x-d/2-(n.y-(n.top??0))})`);u(e,E);e.intersect=function(t){const r=Z.polygon(e,w,t);return r};return a}(0,s.K2)(jt,"multiWaveEdgedRectangle");async function Ht(t,e,{config:{themeVariables:r}}){const{labelStyles:i,nodeStyles:a}=L(e);e.labelStyle=i;const n=e.useHtmlLabels||(0,s.zj)().flowchart?.htmlLabels!==false;if(!n){e.centerLabel=true}const{shapeSvg:o,bbox:l}=await h(t,e,f(e));const d=Math.max(l.width+(e.padding??0)*2,e?.width??0);const p=Math.max(l.height+(e.padding??0)*2,e?.height??0);const g=-d/2;const m=-p/2;const{cssStyles:y}=e;const b=c.A.svg(o);const x=M(e,{fill:r.noteBkgColor,stroke:r.noteBorderColor});if(e.look!=="handDrawn"){x.roughness=0;x.fillStyle="solid"}const C=b.rectangle(g,m,d,p,x);const v=o.insert((()=>C),":first-child");v.attr("class","basic label-container");if(y&&e.look!=="handDrawn"){v.selectAll("path").attr("style",y)}if(a&&e.look!=="handDrawn"){v.selectAll("path").attr("style",a)}u(e,v);e.intersect=function(t){return Z.rect(e,t)};return o}(0,s.K2)(Ht,"note");var Yt=(0,s.K2)(((t,e,r)=>[`M${t+r/2},${e}`,`L${t+r},${e-r/2}`,`L${t+r/2},${e-r}`,`L${t},${e-r/2}`,"Z"].join(" ")),"createDecisionBoxPathD");async function Ut(t,e){const{labelStyles:r,nodeStyles:i}=L(e);e.labelStyle=r;const{shapeSvg:a,bbox:n}=await h(t,e,f(e));const o=n.width+e.padding;const l=n.height+e.padding;const d=o+l;const p=[{x:d/2,y:0},{x:d,y:-d/2},{x:d/2,y:-d},{x:0,y:-d/2}];let g;const{cssStyles:m}=e;if(e.look==="handDrawn"){const t=c.A.svg(a);const r=M(e,{});const i=Yt(0,0,d);const n=t.path(i,r);g=a.insert((()=>n),":first-child").attr("transform",`translate(${-d/2}, ${d/2})`);if(m){g.attr("style",m)}}else{g=et(a,d,d,p)}if(i){g.attr("style",i)}u(e,g);e.intersect=function(t){s.Rm.debug("APA12 Intersect called SPLIT\npoint:",t,"\nnode:\n",e,"\nres:",Z.polygon(e,p,t));return Z.polygon(e,p,t)};return a}(0,s.K2)(Ut,"question");async function Gt(t,e){const{labelStyles:r,nodeStyles:i}=L(e);e.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await h(t,e,f(e));const s=Math.max(n.width+(e.padding??0),e?.width??0);const l=Math.max(n.height+(e.padding??0),e?.height??0);const d=-s/2;const g=-l/2;const m=g/2;const y=[{x:d+m,y:g},{x:d,y:0},{x:d+m,y:-g},{x:-d,y:-g},{x:-d,y:g}];const{cssStyles:b}=e;const x=c.A.svg(a);const C=M(e,{});if(e.look!=="handDrawn"){C.roughness=0;C.fillStyle="solid"}const v=p(y);const k=x.path(v,C);const w=a.insert((()=>k),":first-child");w.attr("class","basic label-container");if(b&&e.look!=="handDrawn"){w.selectAll("path").attr("style",b)}if(i&&e.look!=="handDrawn"){w.selectAll("path").attr("style",i)}w.attr("transform",`translate(${-m/2},0)`);o.attr("transform",`translate(${-m/2-n.width/2-(n.x-(n.left??0))}, ${-(n.height/2)-(n.y-(n.top??0))})`);u(e,w);e.intersect=function(t){return Z.polygon(e,y,t)};return a}(0,s.K2)(Gt,"rect_left_inv_arrow");async function Vt(t,e){const{labelStyles:r,nodeStyles:i}=L(e);e.labelStyle=r;let a;if(!e.cssClasses){a="node default"}else{a="node "+e.cssClasses}const n=t.insert("g").attr("class",a).attr("id",e.domId||e.id);const o=n.insert("g");const h=n.insert("g").attr("class","label").attr("style",i);const d=e.description;const f=e.label;const p=h.node().appendChild(await k(f,e.labelStyle,true,true));let g={width:0,height:0};if((0,s._3)((0,s.D7)()?.flowchart?.htmlLabels)){const t=p.children[0];const e=(0,l.Ltv)(p);g=t.getBoundingClientRect();e.attr("width",g.width);e.attr("height",g.height)}s.Rm.info("Text 2",d);const m=d||[];const y=p.getBBox();const b=h.node().appendChild(await k(m.join?m.join("<br/>"):m,e.labelStyle,true,true));const x=b.children[0];const C=(0,l.Ltv)(b);g=x.getBoundingClientRect();C.attr("width",g.width);C.attr("height",g.height);const v=(e.padding||0)/2;(0,l.Ltv)(b).attr("transform","translate( "+(g.width>y.width?0:(y.width-g.width)/2)+", "+(y.height+v+5)+")");(0,l.Ltv)(p).attr("transform","translate( "+(g.width<y.width?0:-(y.width-g.width)/2)+", 0)");g=h.node().getBBox();h.attr("transform","translate("+-g.width/2+", "+(-g.height/2-v+3)+")");const S=g.width+(e.padding||0);const A=g.height+(e.padding||0);const T=-g.width/2-v;const B=-g.height/2-v;let _;let F;if(e.look==="handDrawn"){const t=c.A.svg(n);const r=M(e,{});const i=t.path(w(T,B,S,A,e.rx||0),r);const a=t.line(-g.width/2-v,-g.height/2-v+y.height+v,g.width/2+v,-g.height/2-v+y.height+v,r);F=n.insert((()=>{s.Rm.debug("Rough node insert CXC",i);return a}),":first-child");_=n.insert((()=>{s.Rm.debug("Rough node insert CXC",i);return i}),":first-child")}else{_=o.insert("rect",":first-child");F=o.insert("line");_.attr("class","outer title-state").attr("style",i).attr("x",-g.width/2-v).attr("y",-g.height/2-v).attr("width",g.width+(e.padding||0)).attr("height",g.height+(e.padding||0));F.attr("class","divider").attr("x1",-g.width/2-v).attr("x2",g.width/2+v).attr("y1",-g.height/2-v+y.height+v).attr("y2",-g.height/2-v+y.height+v)}u(e,_);e.intersect=function(t){return Z.rect(e,t)};return n}(0,s.K2)(Vt,"rectWithTitle");async function Xt(t,e){const r={rx:5,ry:5,classes:"",labelPaddingX:(e?.padding||0)*1,labelPaddingY:(e?.padding||0)*1};return Et(t,e,r)}(0,s.K2)(Xt,"roundedRect");async function Zt(t,e){const{labelStyles:r,nodeStyles:i}=L(e);e.labelStyle=r;const{shapeSvg:a,bbox:n,label:s}=await h(t,e,f(e));const l=e?.padding??0;const d=Math.max(n.width+(e.padding??0)*2,e?.width??0);const p=Math.max(n.height+(e.padding??0)*2,e?.height??0);const g=-n.width/2-l;const m=-n.height/2-l;const{cssStyles:y}=e;const b=c.A.svg(a);const x=M(e,{});if(e.look!=="handDrawn"){x.roughness=0;x.fillStyle="solid"}const C=[{x:g,y:m},{x:g+d+8,y:m},{x:g+d+8,y:m+p},{x:g-8,y:m+p},{x:g-8,y:m},{x:g,y:m},{x:g,y:m+p}];const v=b.polygon(C.map((t=>[t.x,t.y])),x);const k=a.insert((()=>v),":first-child");k.attr("class","basic label-container").attr("style",(0,o.KL)(y));if(i&&e.look!=="handDrawn"){k.selectAll("path").attr("style",i)}if(y&&e.look!=="handDrawn"){k.selectAll("path").attr("style",i)}s.attr("transform",`translate(${-d/2+4+(e.padding??0)-(n.x-(n.left??0))},${-p/2+(e.padding??0)-(n.y-(n.top??0))})`);u(e,k);e.intersect=function(t){return Z.rect(e,t)};return a}(0,s.K2)(Zt,"shadedProcess");async function Jt(t,e){const{labelStyles:r,nodeStyles:i}=L(e);e.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await h(t,e,f(e));const s=Math.max(n.width+(e.padding??0)*2,e?.width??0);const l=Math.max(n.height+(e.padding??0)*2,e?.height??0);const d=-s/2;const g=-l/2;const{cssStyles:m}=e;const y=c.A.svg(a);const b=M(e,{});if(e.look!=="handDrawn"){b.roughness=0;b.fillStyle="solid"}const x=[{x:d,y:g},{x:d,y:g+l},{x:d+s,y:g+l},{x:d+s,y:g-l/2}];const C=p(x);const v=y.path(C,b);const k=a.insert((()=>v),":first-child");k.attr("class","basic label-container");if(m&&e.look!=="handDrawn"){k.selectChildren("path").attr("style",m)}if(i&&e.look!=="handDrawn"){k.selectChildren("path").attr("style",i)}k.attr("transform",`translate(0, ${l/4})`);o.attr("transform",`translate(${-s/2+(e.padding??0)-(n.x-(n.left??0))}, ${-l/4+(e.padding??0)-(n.y-(n.top??0))})`);u(e,k);e.intersect=function(t){const r=Z.polygon(e,x,t);return r};return a}(0,s.K2)(Jt,"slopedRect");async function Qt(t,e){const r={rx:0,ry:0,classes:"",labelPaddingX:(e?.padding||0)*2,labelPaddingY:(e?.padding||0)*1};return Et(t,e,r)}(0,s.K2)(Qt,"squareRect");async function te(t,e){const{labelStyles:r,nodeStyles:i}=L(e);e.labelStyle=r;const{shapeSvg:a,bbox:n}=await h(t,e,f(e));const s=n.height+e.padding;const l=n.width+s/4+e.padding;let d;const{cssStyles:p}=e;if(e.look==="handDrawn"){const t=c.A.svg(a);const r=M(e,{});const i=w(-l/2,-s/2,l,s,s/2);const n=t.path(i,r);d=a.insert((()=>n),":first-child");d.attr("class","basic label-container").attr("style",(0,o.KL)(p))}else{d=a.insert("rect",":first-child");d.attr("class","basic label-container").attr("style",i).attr("rx",s/2).attr("ry",s/2).attr("x",-l/2).attr("y",-s/2).attr("width",l).attr("height",s)}u(e,d);e.intersect=function(t){return Z.rect(e,t)};return a}(0,s.K2)(te,"stadium");async function ee(t,e){const r={rx:5,ry:5,classes:"flowchart-node"};return Et(t,e,r)}(0,s.K2)(ee,"state");function re(t,e,{config:{themeVariables:r}}){const{labelStyles:i,nodeStyles:a}=L(e);e.labelStyle=i;const{cssStyles:n}=e;const{lineColor:o,stateBorder:s,nodeBorder:l}=r;const h=t.insert("g").attr("class","node default").attr("id",e.domId||e.id);const d=c.A.svg(h);const f=M(e,{});if(e.look!=="handDrawn"){f.roughness=0;f.fillStyle="solid"}const p=d.circle(0,0,14,{...f,stroke:o,strokeWidth:2});const g=s??l;const m=d.circle(0,0,5,{...f,fill:g,stroke:g,strokeWidth:2,fillStyle:"solid"});const y=h.insert((()=>p),":first-child");y.insert((()=>m));if(n){y.selectAll("path").attr("style",n)}if(a){y.selectAll("path").attr("style",a)}u(e,y);e.intersect=function(t){return Z.circle(e,7,t)};return h}(0,s.K2)(re,"stateEnd");function ie(t,e,{config:{themeVariables:r}}){const{lineColor:i}=r;const a=t.insert("g").attr("class","node default").attr("id",e.domId||e.id);let n;if(e.look==="handDrawn"){const t=c.A.svg(a);const e=t.circle(0,0,14,S(i));n=a.insert((()=>e));n.attr("class","state-start").attr("r",7).attr("width",14).attr("height",14)}else{n=a.insert("circle",":first-child");n.attr("class","state-start").attr("r",7).attr("width",14).attr("height",14)}u(e,n);e.intersect=function(t){return Z.circle(e,7,t)};return a}(0,s.K2)(ie,"stateStart");async function ae(t,e){const{labelStyles:r,nodeStyles:i}=L(e);e.labelStyle=r;const{shapeSvg:a,bbox:n}=await h(t,e,f(e));const s=(e?.padding||0)/2;const l=n.width+e.padding;const d=n.height+e.padding;const p=-n.width/2-s;const g=-n.height/2-s;const m=[{x:0,y:0},{x:l,y:0},{x:l,y:-d},{x:0,y:-d},{x:0,y:0},{x:-8,y:0},{x:l+8,y:0},{x:l+8,y:-d},{x:-8,y:-d},{x:-8,y:0}];if(e.look==="handDrawn"){const t=c.A.svg(a);const r=M(e,{});const i=t.rectangle(p-8,g,l+16,d,r);const n=t.line(p,g,p,g+d,r);const s=t.line(p+l,g,p+l,g+d,r);a.insert((()=>n),":first-child");a.insert((()=>s),":first-child");const h=a.insert((()=>i),":first-child");const{cssStyles:f}=e;h.attr("class","basic label-container").attr("style",(0,o.KL)(f));u(e,h)}else{const t=et(a,l,d,m);if(i){t.attr("style",i)}u(e,t)}e.intersect=function(t){return Z.polygon(e,m,t)};return a}(0,s.K2)(ae,"subroutine");async function ne(t,e){const{labelStyles:r,nodeStyles:i}=L(e);e.labelStyle=r;const{shapeSvg:a,bbox:n}=await h(t,e,f(e));const o=Math.max(n.width+(e.padding??0)*2,e?.width??0);const s=Math.max(n.height+(e.padding??0)*2,e?.height??0);const l=-o/2;const d=-s/2;const g=.2*s;const m=.2*s;const{cssStyles:y}=e;const b=c.A.svg(a);const x=M(e,{});const C=[{x:l-g/2,y:d},{x:l+o+g/2,y:d},{x:l+o+g/2,y:d+s},{x:l-g/2,y:d+s}];const v=[{x:l+o-g/2,y:d+s},{x:l+o+g/2,y:d+s},{x:l+o+g/2,y:d+s-m}];if(e.look!=="handDrawn"){x.roughness=0;x.fillStyle="solid"}const k=p(C);const w=b.path(k,x);const S=p(v);const A=b.path(S,{...x,fillStyle:"solid"});const T=a.insert((()=>A),":first-child");T.insert((()=>w),":first-child");T.attr("class","basic label-container");if(y&&e.look!=="handDrawn"){T.selectAll("path").attr("style",y)}if(i&&e.look!=="handDrawn"){T.selectAll("path").attr("style",i)}u(e,T);e.intersect=function(t){const r=Z.polygon(e,C,t);return r};return a}(0,s.K2)(ne,"taggedRect");async function oe(t,e){const{labelStyles:r,nodeStyles:i}=L(e);e.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await h(t,e,f(e));const s=Math.max(n.width+(e.padding??0)*2,e?.width??0);const l=Math.max(n.height+(e.padding??0)*2,e?.height??0);const d=l/4;const m=.2*s;const y=.2*l;const b=l+d;const{cssStyles:x}=e;const C=c.A.svg(a);const v=M(e,{});if(e.look!=="handDrawn"){v.roughness=0;v.fillStyle="solid"}const k=[{x:-s/2-s/2*.1,y:b/2},...g(-s/2-s/2*.1,b/2,s/2+s/2*.1,b/2,d,.8),{x:s/2+s/2*.1,y:-b/2},{x:-s/2-s/2*.1,y:-b/2}];const w=-s/2+s/2*.1;const S=-b/2-y*.4;const A=[{x:w+s-m,y:(S+l)*1.4},{x:w+s,y:S+l-y},{x:w+s,y:(S+l)*.9},...g(w+s,(S+l)*1.3,w+s-m,(S+l)*1.5,-l*.03,.5)];const T=p(k);const B=C.path(T,v);const _=p(A);const F=C.path(_,{...v,fillStyle:"solid"});const $=a.insert((()=>F),":first-child");$.insert((()=>B),":first-child");$.attr("class","basic label-container");if(x&&e.look!=="handDrawn"){$.selectAll("path").attr("style",x)}if(i&&e.look!=="handDrawn"){$.selectAll("path").attr("style",i)}$.attr("transform",`translate(0,${-d/2})`);o.attr("transform",`translate(${-s/2+(e.padding??0)-(n.x-(n.left??0))},${-l/2+(e.padding??0)-d/2-(n.y-(n.top??0))})`);u(e,$);e.intersect=function(t){const r=Z.polygon(e,k,t);return r};return a}(0,s.K2)(oe,"taggedWaveEdgedRectangle");async function se(t,e){const{labelStyles:r,nodeStyles:i}=L(e);e.labelStyle=r;const{shapeSvg:a,bbox:n}=await h(t,e,f(e));const o=Math.max(n.width+e.padding,e?.width||0);const s=Math.max(n.height+e.padding,e?.height||0);const l=-o/2;const c=-s/2;const d=a.insert("rect",":first-child");d.attr("class","text").attr("style",i).attr("rx",0).attr("ry",0).attr("x",l).attr("y",c).attr("width",o).attr("height",s);u(e,d);e.intersect=function(t){return Z.rect(e,t)};return a}(0,s.K2)(se,"text");var le=(0,s.K2)(((t,e,r,i,a,n)=>`M${t},${e}\n    a${a},${n} 0,0,1 ${0},${-i}\n    l${r},${0}\n    a${a},${n} 0,0,1 ${0},${i}\n    M${r},${-i}\n    a${a},${n} 0,0,0 ${0},${i}\n    l${-r},${0}`),"createCylinderPathD");var ce=(0,s.K2)(((t,e,r,i,a,n)=>[`M${t},${e}`,`M${t+r},${e}`,`a${a},${n} 0,0,0 ${0},${-i}`,`l${-r},0`,`a${a},${n} 0,0,0 ${0},${i}`,`l${r},0`].join(" ")),"createOuterCylinderPathD");var he=(0,s.K2)(((t,e,r,i,a,n)=>[`M${t+r/2},${-i/2}`,`a${a},${n} 0,0,0 0,${i}`].join(" ")),"createInnerCylinderPathD");async function de(t,e){const{labelStyles:r,nodeStyles:i}=L(e);e.labelStyle=r;const{shapeSvg:a,bbox:n,label:s,halfPadding:l}=await h(t,e,f(e));const d=e.look==="neo"?l*2:l;const p=n.height+d;const g=p/2;const m=g/(2.5+p/50);const y=n.width+m+d;const{cssStyles:b}=e;let x;if(e.look==="handDrawn"){const t=c.A.svg(a);const r=ce(0,0,y,p,m,g);const i=he(0,0,y,p,m,g);const n=t.path(r,M(e,{}));const o=t.path(i,M(e,{fill:"none"}));x=a.insert((()=>o),":first-child");x=a.insert((()=>n),":first-child");x.attr("class","basic label-container");if(b){x.attr("style",b)}}else{const t=le(0,0,y,p,m,g);x=a.insert("path",":first-child").attr("d",t).attr("class","basic label-container").attr("style",(0,o.KL)(b)).attr("style",i);x.attr("class","basic label-container");if(b){x.selectAll("path").attr("style",b)}if(i){x.selectAll("path").attr("style",i)}}x.attr("label-offset-x",m);x.attr("transform",`translate(${-y/2}, ${p/2} )`);s.attr("transform",`translate(${-(n.width/2)-m-(n.x-(n.left??0))}, ${-(n.height/2)-(n.y-(n.top??0))})`);u(e,x);e.intersect=function(t){const r=Z.rect(e,t);const i=r.y-(e.y??0);if(g!=0&&(Math.abs(i)<(e.height??0)/2||Math.abs(i)==(e.height??0)/2&&Math.abs(r.x-(e.x??0))>(e.width??0)/2-m)){let a=m*m*(1-i*i/(g*g));if(a!=0){a=Math.sqrt(Math.abs(a))}a=m-a;if(t.x-(e.x??0)>0){a=-a}r.x+=a}return r};return a}(0,s.K2)(de,"tiltedCylinder");async function ue(t,e){const{labelStyles:r,nodeStyles:i}=L(e);e.labelStyle=r;const{shapeSvg:a,bbox:n}=await h(t,e,f(e));const o=n.width+e.padding;const s=n.height+e.padding;const l=[{x:-3*s/6,y:0},{x:o+3*s/6,y:0},{x:o,y:-s},{x:0,y:-s}];let d;const{cssStyles:g}=e;if(e.look==="handDrawn"){const t=c.A.svg(a);const r=M(e,{});const i=p(l);const n=t.path(i,r);d=a.insert((()=>n),":first-child").attr("transform",`translate(${-o/2}, ${s/2})`);if(g){d.attr("style",g)}}else{d=et(a,o,s,l)}if(i){d.attr("style",i)}e.width=o;e.height=s;u(e,d);e.intersect=function(t){return Z.polygon(e,l,t)};return a}(0,s.K2)(ue,"trapezoid");async function fe(t,e){const{labelStyles:r,nodeStyles:i}=L(e);e.labelStyle=r;const{shapeSvg:a,bbox:n}=await h(t,e,f(e));const o=60,s=20;const l=Math.max(o,n.width+(e.padding??0)*2,e?.width??0);const d=Math.max(s,n.height+(e.padding??0)*2,e?.height??0);const{cssStyles:g}=e;const m=c.A.svg(a);const y=M(e,{});if(e.look!=="handDrawn"){y.roughness=0;y.fillStyle="solid"}const b=[{x:-l/2*.8,y:-d/2},{x:l/2*.8,y:-d/2},{x:l/2,y:-d/2*.6},{x:l/2,y:d/2},{x:-l/2,y:d/2},{x:-l/2,y:-d/2*.6}];const x=p(b);const C=m.path(x,y);const v=a.insert((()=>C),":first-child");v.attr("class","basic label-container");if(g&&e.look!=="handDrawn"){v.selectChildren("path").attr("style",g)}if(i&&e.look!=="handDrawn"){v.selectChildren("path").attr("style",i)}u(e,v);e.intersect=function(t){const r=Z.polygon(e,b,t);return r};return a}(0,s.K2)(fe,"trapezoidalPentagon");async function pe(t,e){const{labelStyles:r,nodeStyles:i}=L(e);e.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await h(t,e,f(e));const l=(0,s._3)((0,s.D7)().flowchart?.htmlLabels);const d=n.width+(e.padding??0);const g=d+n.height;const m=d+n.height;const y=[{x:0,y:0},{x:m,y:0},{x:m/2,y:-g}];const{cssStyles:b}=e;const x=c.A.svg(a);const C=M(e,{});if(e.look!=="handDrawn"){C.roughness=0;C.fillStyle="solid"}const v=p(y);const k=x.path(v,C);const w=a.insert((()=>k),":first-child").attr("transform",`translate(${-g/2}, ${g/2})`);if(b&&e.look!=="handDrawn"){w.selectChildren("path").attr("style",b)}if(i&&e.look!=="handDrawn"){w.selectChildren("path").attr("style",i)}e.width=d;e.height=g;u(e,w);o.attr("transform",`translate(${-n.width/2-(n.x-(n.left??0))}, ${g/2-(n.height+(e.padding??0)/(l?2:1)-(n.y-(n.top??0)))})`);e.intersect=function(t){s.Rm.info("Triangle intersect",e,y,t);return Z.polygon(e,y,t)};return a}(0,s.K2)(pe,"triangle");async function ge(t,e){const{labelStyles:r,nodeStyles:i}=L(e);e.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await h(t,e,f(e));const s=Math.max(n.width+(e.padding??0)*2,e?.width??0);const l=Math.max(n.height+(e.padding??0)*2,e?.height??0);const d=l/8;const m=l+d;const{cssStyles:y}=e;const b=70;const x=b-s;const C=x>0?x/2:0;const v=c.A.svg(a);const k=M(e,{});if(e.look!=="handDrawn"){k.roughness=0;k.fillStyle="solid"}const w=[{x:-s/2-C,y:m/2},...g(-s/2-C,m/2,s/2+C,m/2,d,.8),{x:s/2+C,y:-m/2},{x:-s/2-C,y:-m/2}];const S=p(w);const A=v.path(S,k);const T=a.insert((()=>A),":first-child");T.attr("class","basic label-container");if(y&&e.look!=="handDrawn"){T.selectAll("path").attr("style",y)}if(i&&e.look!=="handDrawn"){T.selectAll("path").attr("style",i)}T.attr("transform",`translate(0,${-d/2})`);o.attr("transform",`translate(${-s/2+(e.padding??0)-(n.x-(n.left??0))},${-l/2+(e.padding??0)-d-(n.y-(n.top??0))})`);u(e,T);e.intersect=function(t){const r=Z.polygon(e,w,t);return r};return a}(0,s.K2)(ge,"waveEdgedRectangle");async function me(t,e){const{labelStyles:r,nodeStyles:i}=L(e);e.labelStyle=r;const{shapeSvg:a,bbox:n}=await h(t,e,f(e));const o=100;const s=50;const l=Math.max(n.width+(e.padding??0)*2,e?.width??0);const d=Math.max(n.height+(e.padding??0)*2,e?.height??0);const m=l/d;let y=l;let b=d;if(y>b*m){b=y/m}else{y=b*m}y=Math.max(y,o);b=Math.max(b,s);const x=Math.min(b*.2,b/4);const C=b+x*2;const{cssStyles:v}=e;const k=c.A.svg(a);const w=M(e,{});if(e.look!=="handDrawn"){w.roughness=0;w.fillStyle="solid"}const S=[{x:-y/2,y:C/2},...g(-y/2,C/2,y/2,C/2,x,1),{x:y/2,y:-C/2},...g(y/2,-C/2,-y/2,-C/2,x,-1)];const A=p(S);const T=k.path(A,w);const B=a.insert((()=>T),":first-child");B.attr("class","basic label-container");if(v&&e.look!=="handDrawn"){B.selectAll("path").attr("style",v)}if(i&&e.look!=="handDrawn"){B.selectAll("path").attr("style",i)}u(e,B);e.intersect=function(t){const r=Z.polygon(e,S,t);return r};return a}(0,s.K2)(me,"waveRectangle");async function ye(t,e){const{labelStyles:r,nodeStyles:i}=L(e);e.labelStyle=r;const{shapeSvg:a,bbox:n,label:o}=await h(t,e,f(e));const s=Math.max(n.width+(e.padding??0)*2,e?.width??0);const l=Math.max(n.height+(e.padding??0)*2,e?.height??0);const d=5;const p=-s/2;const g=-l/2;const{cssStyles:m}=e;const y=c.A.svg(a);const b=M(e,{});const x=[{x:p-d,y:g-d},{x:p-d,y:g+l},{x:p+s,y:g+l},{x:p+s,y:g-d}];const C=`M${p-d},${g-d} L${p+s},${g-d} L${p+s},${g+l} L${p-d},${g+l} L${p-d},${g-d}\n                M${p-d},${g} L${p+s},${g}\n                M${p},${g-d} L${p},${g+l}`;if(e.look!=="handDrawn"){b.roughness=0;b.fillStyle="solid"}const v=y.path(C,b);const k=a.insert((()=>v),":first-child");k.attr("transform",`translate(${d/2}, ${d/2})`);k.attr("class","basic label-container");if(m&&e.look!=="handDrawn"){k.selectAll("path").attr("style",m)}if(i&&e.look!=="handDrawn"){k.selectAll("path").attr("style",i)}o.attr("transform",`translate(${-(n.width/2)+d/2-(n.x-(n.left??0))}, ${-(n.height/2)+d/2-(n.y-(n.top??0))})`);u(e,k);e.intersect=function(t){const r=Z.polygon(e,x,t);return r};return a}(0,s.K2)(ye,"windowPane");async function be(t,e){const r=e;if(r.alias){e.label=r.alias}if(e.look==="handDrawn"){const{themeVariables:r}=(0,s.zj)();const{background:i}=r;const a={...e,id:e.id+"-background",look:"default",cssStyles:["stroke: none",`fill: ${i}`]};await be(t,a)}const i=(0,s.zj)();e.useHtmlLabels=i.htmlLabels;let a=i.er?.diagramPadding??10;let n=i.er?.entityPadding??6;const{cssStyles:h}=e;const{labelStyles:d}=L(e);if(r.attributes.length===0&&e.label){const r={rx:0,ry:0,labelPaddingX:a,labelPaddingY:a*1.5,classes:""};if((0,o.Un)(e.label,i)+r.labelPaddingX*2<i.er.minEntityWidth){e.width=i.er.minEntityWidth}const n=await Et(t,e,r);if(!(0,s._3)(i.htmlLabels)){const t=n.select("text");const e=t.node()?.getBBox();t.attr("transform",`translate(${-e.width/2}, 0)`)}return n}if(!i.htmlLabels){a*=1.25;n*=1.25}let p=f(e);if(!p){p="node default"}const g=t.insert("g").attr("class",p).attr("id",e.domId||e.id);const m=await xe(g,e.label??"",i,0,0,["name"],d);m.height+=n;let y=0;const b=[];let x=0;let C=0;let v=0;let k=0;let w=true;let S=true;for(const o of r.attributes){const t=await xe(g,o.type,i,0,y,["attribute-type"],d);x=Math.max(x,t.width+a);const e=await xe(g,o.name,i,0,y,["attribute-name"],d);C=Math.max(C,e.width+a);const r=await xe(g,o.keys.join(),i,0,y,["attribute-keys"],d);v=Math.max(v,r.width+a);const s=await xe(g,o.comment,i,0,y,["attribute-comment"],d);k=Math.max(k,s.width+a);y+=Math.max(t.height,e.height,r.height,s.height)+n;b.push(y)}b.pop();let A=4;if(v<=a){w=false;v=0;A--}if(k<=a){S=false;k=0;A--}const T=g.node().getBBox();if(m.width+a*2-(x+C+v+k)>0){const t=m.width+a*2-(x+C+v+k);x+=t/A;C+=t/A;if(v>0){v+=t/A}if(k>0){k+=t/A}}const B=x+C+v+k;const _=c.A.svg(g);const F=M(e,{});if(e.look!=="handDrawn"){F.roughness=0;F.fillStyle="solid"}const $=Math.max(T.width+a*2,e?.width||0,B);const E=Math.max(T.height+(b[0]||y)+n,e?.height||0);const O=-$/2;const D=-E/2;g.selectAll("g:not(:first-child)").each(((t,e,r)=>{const i=(0,l.Ltv)(r[e]);const o=i.attr("transform");let s=0;let c=0;if(o){const t=RegExp(/translate\(([^,]+),([^)]+)\)/);const e=t.exec(o);if(e){s=parseFloat(e[1]);c=parseFloat(e[2]);if(i.attr("class").includes("attribute-name")){s+=x}else if(i.attr("class").includes("attribute-keys")){s+=x+C}else if(i.attr("class").includes("attribute-comment")){s+=x+C+v}}}i.attr("transform",`translate(${O+a/2+s}, ${c+D+m.height+n/2})`)}));g.select(".name").attr("transform","translate("+-m.width/2+", "+(D+n/2)+")");const I=_.rectangle(O,D,$,E,F);const K=g.insert((()=>I),":first-child").attr("style",h.join(""));const{themeVariables:R}=(0,s.zj)();const{rowEven:P,rowOdd:z,nodeBorder:q}=R;b.push(0);for(const[o,s]of b.entries()){if(o===0&&b.length>1){continue}const t=o%2===0&&s!==0;const e=_.rectangle(O,m.height+D+s,$,m.height,{...F,fill:t?P:z,stroke:q});g.insert((()=>e),"g.label").attr("style",h.join("")).attr("class",`row-rect-${o%2===0?"even":"odd"}`)}let N=_.line(O,m.height+D,$+O,m.height+D,F);g.insert((()=>N)).attr("class","divider");N=_.line(x+O,m.height+D,x+O,E+D,F);g.insert((()=>N)).attr("class","divider");if(w){N=_.line(x+C+O,m.height+D,x+C+O,E+D,F);g.insert((()=>N)).attr("class","divider")}if(S){N=_.line(x+C+v+O,m.height+D,x+C+v+O,E+D,F);g.insert((()=>N)).attr("class","divider")}for(const o of b){N=_.line(O,m.height+D+o,$+O,m.height+D+o,F);g.insert((()=>N)).attr("class","divider")}u(e,K);e.intersect=function(t){return Z.rect(e,t)};return g}(0,s.K2)(be,"erBox");async function xe(t,e,r,i=0,a=0,c=[],h=""){const d=t.insert("g").attr("class",`label ${c.join(" ")}`).attr("transform",`translate(${i}, ${a})`).attr("style",h);if(e!==(0,s.QO)(e)){e=(0,s.QO)(e);e=e.replaceAll("<","&lt;").replaceAll(">","&gt;")}const u=d.node().appendChild(await(0,n.GZ)(d,e,{width:(0,o.Un)(e,r)+100,style:h,useHtmlLabels:r.htmlLabels},r));if(e.includes("&lt;")||e.includes("&gt;")){let t=u.children[0];t.textContent=t.textContent.replaceAll("&lt;","<").replaceAll("&gt;",">");while(t.childNodes[0]){t=t.childNodes[0];t.textContent=t.textContent.replaceAll("&lt;","<").replaceAll("&gt;",">")}}let f=u.getBBox();if((0,s._3)(r.htmlLabels)){const t=u.children[0];t.style.textAlign="start";const e=(0,l.Ltv)(u);f=t.getBoundingClientRect();e.attr("width",f.width);e.attr("height",f.height)}return f}(0,s.K2)(xe,"addText");async function Ce(t,e,r,i,a=r.class.padding??12){const n=!i?3:0;const o=t.insert("g").attr("class",f(e)).attr("id",e.domId||e.id);let s=null;let l=null;let c=null;let h=null;let d=0;let u=0;let p=0;s=o.insert("g").attr("class","annotation-group text");if(e.annotations.length>0){const t=e.annotations[0];await ve(s,{text:`«${t}»`},0);const r=s.node().getBBox();d=r.height}l=o.insert("g").attr("class","label-group text");await ve(l,e,0,["font-weight: bolder"]);const g=l.node().getBBox();u=g.height;c=o.insert("g").attr("class","members-group text");let m=0;for(const f of e.members){const t=await ve(c,f,m,[f.parseClassifier()]);m+=t+n}p=c.node().getBBox().height;if(p<=0){p=a/2}h=o.insert("g").attr("class","methods-group text");let y=0;for(const f of e.methods){const t=await ve(h,f,y,[f.parseClassifier()]);y+=t+n}let b=o.node().getBBox();if(s!==null){const t=s.node().getBBox();s.attr("transform",`translate(${-t.width/2})`)}l.attr("transform",`translate(${-g.width/2}, ${d})`);b=o.node().getBBox();c.attr("transform",`translate(${0}, ${d+u+a*2})`);b=o.node().getBBox();h.attr("transform",`translate(${0}, ${d+u+(p?p+a*4:a*2)})`);b=o.node().getBBox();return{shapeSvg:o,bbox:b}}(0,s.K2)(Ce,"textHelper");async function ve(t,e,r,i=[]){const a=t.insert("g").attr("class","label").attr("style",i.join("; "));const c=(0,s.zj)();let h="useHtmlLabels"in e?e.useHtmlLabels:(0,s._3)(c.htmlLabels)??true;let d="";if("text"in e){d=e.text}else{d=e.label}if(!h&&d.startsWith("\\")){d=d.substring(1)}if((0,s.Wi)(d)){h=true}const u=await(0,n.GZ)(a,(0,s.oB)((0,o.Sm)(d)),{width:(0,o.Un)(d,c)+50,classes:"markdown-node-label",useHtmlLabels:h},c);let f;let p=1;if(!h){if(i.includes("font-weight: bolder")){(0,l.Ltv)(u).selectAll("tspan").attr("font-weight","")}p=u.children.length;const t=u.children[0];if(u.textContent===""||u.textContent.includes("&gt")){t.textContent=d[0]+d.substring(1).replaceAll("&gt;",">").replaceAll("&lt;","<").trim();const e=d[1]===" ";if(e){t.textContent=t.textContent[0]+" "+t.textContent.substring(1)}}if(t.textContent==="undefined"){t.textContent=""}f=u.getBBox()}else{const t=u.children[0];const e=(0,l.Ltv)(u);p=t.innerHTML.split("<br>").length;if(t.innerHTML.includes("</math>")){p+=t.innerHTML.split("<mrow>").length-1}const r=t.getElementsByTagName("img");if(r){const t=d.replace(/<img[^>]*>/g,"").trim()==="";await Promise.all([...r].map((e=>new Promise((r=>{function i(){e.style.display="flex";e.style.flexDirection="column";if(t){const t=c.fontSize?.toString()??window.getComputedStyle(document.body).fontSize;const r=5;const i=parseInt(t,10)*r+"px";e.style.minWidth=i;e.style.maxWidth=i}else{e.style.width="100%"}r(e)}(0,s.K2)(i,"setupImage");setTimeout((()=>{if(e.complete){i()}}));e.addEventListener("error",i);e.addEventListener("load",i)})))))}f=t.getBoundingClientRect();e.attr("width",f.width);e.attr("height",f.height)}a.attr("transform","translate(0,"+(-f.height/(2*p)+r)+")");return f.height}(0,s.K2)(ve,"addText");async function ke(t,e){const r=(0,s.D7)();const i=r.class.padding??12;const a=i;const n=e.useHtmlLabels??(0,s._3)(r.htmlLabels)??true;const o=e;o.annotations=o.annotations??[];o.members=o.members??[];o.methods=o.methods??[];const{shapeSvg:h,bbox:d}=await Ce(t,e,r,n,a);const{labelStyles:f,nodeStyles:p}=L(e);e.labelStyle=f;e.cssStyles=o.styles||"";const g=o.styles?.join(";")||p||"";if(!e.cssStyles){e.cssStyles=g.replaceAll("!important","").split(";")}const m=o.members.length===0&&o.methods.length===0&&!r.class?.hideEmptyMembersBox;const y=c.A.svg(h);const b=M(e,{});if(e.look!=="handDrawn"){b.roughness=0;b.fillStyle="solid"}const x=d.width;let C=d.height;if(o.members.length===0&&o.methods.length===0){C+=a}else if(o.members.length>0&&o.methods.length===0){C+=a*2}const v=-x/2;const k=-C/2;const w=y.rectangle(v-i,k-i-(m?i:o.members.length===0&&o.methods.length===0?-i/2:0),x+2*i,C+2*i+(m?i*2:o.members.length===0&&o.methods.length===0?-i:0),b);const S=h.insert((()=>w),":first-child");S.attr("class","basic label-container");const A=S.node().getBBox();h.selectAll(".text").each(((t,e,r)=>{const a=(0,l.Ltv)(r[e]);const s=a.attr("transform");let c=0;if(s){const t=RegExp(/translate\(([^,]+),([^)]+)\)/);const e=t.exec(s);if(e){c=parseFloat(e[2])}}let d=c+k+i-(m?i:o.members.length===0&&o.methods.length===0?-i/2:0);if(!n){d-=4}let u=v;if(a.attr("class").includes("label-group")||a.attr("class").includes("annotation-group")){u=-a.node()?.getBBox().width/2||0;h.selectAll("text").each((function(t,e,r){if(window.getComputedStyle(r[e]).textAnchor==="middle"){u=0}}))}a.attr("transform",`translate(${u}, ${d})`)}));const T=h.select(".annotation-group").node().getBBox().height-(m?i/2:0)||0;const B=h.select(".label-group").node().getBBox().height-(m?i/2:0)||0;const _=h.select(".members-group").node().getBBox().height-(m?i/2:0)||0;if(o.members.length>0||o.methods.length>0||m){const t=y.line(A.x,T+B+k+i,A.x+A.width,T+B+k+i,b);const e=h.insert((()=>t));e.attr("class","divider").attr("style",g)}if(m||o.members.length>0||o.methods.length>0){const t=y.line(A.x,T+B+_+k+a*2+i,A.x+A.width,T+B+_+k+i+a*2,b);const e=h.insert((()=>t));e.attr("class","divider").attr("style",g)}if(o.look!=="handDrawn"){h.selectAll("path").attr("style",g)}S.select(":nth-child(2)").attr("style",g);h.selectAll(".divider").select("path").attr("style",g);if(e.labelStyle){h.selectAll("span").attr("style",e.labelStyle)}else{h.selectAll("span").attr("style",g)}if(!n){const t=RegExp(/color\s*:\s*([^;]*)/);const e=t.exec(g);if(e){const t=e[0].replace("color","fill");h.selectAll("tspan").attr("style",t)}else if(f){const e=t.exec(f);if(e){const t=e[0].replace("color","fill");h.selectAll("tspan").attr("style",t)}}}u(e,S);e.intersect=function(t){return Z.rect(e,t)};return h}(0,s.K2)(ke,"classBox");async function we(t,e){const{labelStyles:r,nodeStyles:i}=L(e);e.labelStyle=r;const a=e;const n=e;const o=20;const s=20;const h="verifyMethod"in e;const d=f(e);const p=t.insert("g").attr("class",d).attr("id",e.domId??e.id);let g;if(h){g=await Se(p,`&lt;&lt;${a.type}&gt;&gt;`,0,e.labelStyle)}else{g=await Se(p,"&lt;&lt;Element&gt;&gt;",0,e.labelStyle)}let m=g;const y=await Se(p,a.name,m,e.labelStyle+"; font-weight: bold;");m+=y+s;if(h){const t=await Se(p,`${a.requirementId?`Id: ${a.requirementId}`:""}`,m,e.labelStyle);m+=t;const r=await Se(p,`${a.text?`Text: ${a.text}`:""}`,m,e.labelStyle);m+=r;const i=await Se(p,`${a.risk?`Risk: ${a.risk}`:""}`,m,e.labelStyle);m+=i;await Se(p,`${a.verifyMethod?`Verification: ${a.verifyMethod}`:""}`,m,e.labelStyle)}else{const t=await Se(p,`${n.type?`Type: ${n.type}`:""}`,m,e.labelStyle);m+=t;await Se(p,`${n.docRef?`Doc Ref: ${n.docRef}`:""}`,m,e.labelStyle)}const b=(p.node()?.getBBox().width??200)+o;const x=(p.node()?.getBBox().height??200)+o;const C=-b/2;const v=-x/2;const k=c.A.svg(p);const w=M(e,{});if(e.look!=="handDrawn"){w.roughness=0;w.fillStyle="solid"}const S=k.rectangle(C,v,b,x,w);const A=p.insert((()=>S),":first-child");A.attr("class","basic label-container").attr("style",i);p.selectAll(".label").each(((t,e,r)=>{const i=(0,l.Ltv)(r[e]);const a=i.attr("transform");let n=0;let s=0;if(a){const t=RegExp(/translate\(([^,]+),([^)]+)\)/);const e=t.exec(a);if(e){n=parseFloat(e[1]);s=parseFloat(e[2])}}const c=s-x/2;let h=C+o/2;if(e===0||e===1){h=n}i.attr("transform",`translate(${h}, ${c+o})`)}));if(m>g+y+s){const t=k.line(C,v+g+y+s,C+b,v+g+y+s,w);const e=p.insert((()=>t));e.attr("style",i)}u(e,A);e.intersect=function(t){return Z.rect(e,t)};return p}(0,s.K2)(we,"requirementBox");async function Se(t,e,r,i=""){if(e===""){return 0}const a=t.insert("g").attr("class","label").attr("style",i);const c=(0,s.D7)();const h=c.htmlLabels??true;const d=await(0,n.GZ)(a,(0,s.oB)((0,o.Sm)(e)),{width:(0,o.Un)(e,c)+50,classes:"markdown-node-label",useHtmlLabels:h,style:i},c);let u;if(!h){const t=d.children[0];for(const e of t.children){e.textContent=e.textContent.replaceAll("&gt;",">").replaceAll("&lt;","<");if(i){e.setAttribute("style",i)}}u=d.getBBox();u.height+=6}else{const t=d.children[0];const e=(0,l.Ltv)(d);u=t.getBoundingClientRect();e.attr("width",u.width);e.attr("height",u.height)}a.attr("transform",`translate(${-u.width/2},${-u.height/2+r})`);return u.height}(0,s.K2)(Se,"addText");var Ae=(0,s.K2)((t=>{switch(t){case"Very High":return"red";case"High":return"orange";case"Medium":return null;case"Low":return"blue";case"Very Low":return"lightblue"}}),"colorFromPriority");async function Te(t,e,{config:r}){const{labelStyles:i,nodeStyles:a}=L(e);e.labelStyle=i||"";const n=10;const o=e.width;e.width=(e.width??200)-10;const{shapeSvg:s,bbox:l,label:p}=await h(t,e,f(e));const g=e.padding||10;let m="";let y;if("ticket"in e&&e.ticket&&r?.kanban?.ticketBaseUrl){m=r?.kanban?.ticketBaseUrl.replace("#TICKET#",e.ticket);y=s.insert("svg:a",":first-child").attr("class","kanban-ticket-link").attr("xlink:href",m).attr("target","_blank")}const b={useHtmlLabels:e.useHtmlLabels,labelStyle:e.labelStyle||"",width:e.width,img:e.img,padding:e.padding||8,centerLabel:false};let x,C;if(y){({label:x,bbox:C}=await d(y,"ticket"in e&&e.ticket||"",b))}else{({label:x,bbox:C}=await d(s,"ticket"in e&&e.ticket||"",b))}const{label:v,bbox:k}=await d(s,"assigned"in e&&e.assigned||"",b);e.width=o;const S=10;const A=e?.width||0;const T=Math.max(C.height,k.height)/2;const B=Math.max(l.height+S*2,e?.height||0)+T;const _=-A/2;const F=-B/2;p.attr("transform","translate("+(g-A/2)+", "+(-T-l.height/2)+")");x.attr("transform","translate("+(g-A/2)+", "+(-T+l.height/2)+")");v.attr("transform","translate("+(g+A/2-k.width-2*n)+", "+(-T+l.height/2)+")");let $;const{rx:E,ry:O}=e;const{cssStyles:D}=e;if(e.look==="handDrawn"){const t=c.A.svg(s);const r=M(e,{});const i=E||O?t.path(w(_,F,A,B,E||0),r):t.rectangle(_,F,A,B,r);$=s.insert((()=>i),":first-child");$.attr("class","basic label-container").attr("style",D?D:null)}else{$=s.insert("rect",":first-child");$.attr("class","basic label-container __APA__").attr("style",a).attr("rx",E??5).attr("ry",O??5).attr("x",_).attr("y",F).attr("width",A).attr("height",B);const t="priority"in e&&e.priority;if(t){const e=s.append("line");const r=_+2;const i=F+Math.floor((E??0)/2);const a=F+B-Math.floor((E??0)/2);e.attr("x1",r).attr("y1",i).attr("x2",r).attr("y2",a).attr("stroke-width","4").attr("stroke",Ae(t))}}u(e,$);e.height=B;e.intersect=function(t){return Z.rect(e,t)};return s}(0,s.K2)(Te,"kanbanItem");var Be=[{semanticName:"Process",name:"Rectangle",shortName:"rect",description:"Standard process shape",aliases:["proc","process","rectangle"],internalAliases:["squareRect"],handler:Qt},{semanticName:"Event",name:"Rounded Rectangle",shortName:"rounded",description:"Represents an event",aliases:["event"],internalAliases:["roundedRect"],handler:Xt},{semanticName:"Terminal Point",name:"Stadium",shortName:"stadium",description:"Terminal point",aliases:["terminal","pill"],handler:te},{semanticName:"Subprocess",name:"Framed Rectangle",shortName:"fr-rect",description:"Subprocess",aliases:["subprocess","subproc","framed-rectangle","subroutine"],handler:ae},{semanticName:"Database",name:"Cylinder",shortName:"cyl",description:"Database storage",aliases:["db","database","cylinder"],handler:yt},{semanticName:"Start",name:"Circle",shortName:"circle",description:"Starting point",aliases:["circ"],handler:at},{semanticName:"Decision",name:"Diamond",shortName:"diam",description:"Decision-making step",aliases:["decision","diamond","question"],handler:Ut},{semanticName:"Prepare Conditional",name:"Hexagon",shortName:"hex",description:"Preparation or condition step",aliases:["hexagon","prepare"],handler:At},{semanticName:"Data Input/Output",name:"Lean Right",shortName:"lean-r",description:"Represents input or output",aliases:["lean-right","in-out"],internalAliases:["lean_right"],handler:It},{semanticName:"Data Input/Output",name:"Lean Left",shortName:"lean-l",description:"Represents output or input",aliases:["lean-left","out-in"],internalAliases:["lean_left"],handler:Dt},{semanticName:"Priority Action",name:"Trapezoid Base Bottom",shortName:"trap-b",description:"Priority action",aliases:["priority","trapezoid-bottom","trapezoid"],handler:ue},{semanticName:"Manual Operation",name:"Trapezoid Base Top",shortName:"trap-t",description:"Represents a manual task",aliases:["manual","trapezoid-top","inv-trapezoid"],internalAliases:["inv_trapezoid"],handler:$t},{semanticName:"Stop",name:"Double Circle",shortName:"dbl-circ",description:"Represents a stop point",aliases:["double-circle"],internalAliases:["doublecircle"],handler:xt},{semanticName:"Text Block",name:"Text Block",shortName:"text",description:"Text block",handler:se},{semanticName:"Card",name:"Notched Rectangle",shortName:"notch-rect",description:"Represents a card",aliases:["card","notched-rectangle"],handler:rt},{semanticName:"Lined/Shaded Process",name:"Lined Rectangle",shortName:"lin-rect",description:"Lined process shape",aliases:["lined-rectangle","lined-process","lin-proc","shaded-process"],handler:Zt},{semanticName:"Start",name:"Small Circle",shortName:"sm-circ",description:"Small starting point",aliases:["start","small-circle"],internalAliases:["stateStart"],handler:ie},{semanticName:"Stop",name:"Framed Circle",shortName:"fr-circ",description:"Stop point",aliases:["stop","framed-circle"],internalAliases:["stateEnd"],handler:re},{semanticName:"Fork/Join",name:"Filled Rectangle",shortName:"fork",description:"Fork or join in process flow",aliases:["join"],internalAliases:["forkJoin"],handler:kt},{semanticName:"Collate",name:"Hourglass",shortName:"hourglass",description:"Represents a collate operation",aliases:["hourglass","collate"],handler:Tt},{semanticName:"Comment",name:"Curly Brace",shortName:"brace",description:"Adds a comment",aliases:["comment","brace-l"],handler:lt},{semanticName:"Comment Right",name:"Curly Brace",shortName:"brace-r",description:"Adds a comment",handler:ht},{semanticName:"Comment with braces on both sides",name:"Curly Braces",shortName:"braces",description:"Adds a comment",handler:ut},{semanticName:"Com Link",name:"Lightning Bolt",shortName:"bolt",description:"Communication link",aliases:["com-link","lightning-bolt"],handler:Kt},{semanticName:"Document",name:"Document",shortName:"doc",description:"Represents a document",aliases:["doc","document"],handler:ge},{semanticName:"Delay",name:"Half-Rounded Rectangle",shortName:"delay",description:"Represents a delay",aliases:["half-rounded-rectangle"],handler:wt},{semanticName:"Direct Access Storage",name:"Horizontal Cylinder",shortName:"h-cyl",description:"Direct access storage",aliases:["das","horizontal-cylinder"],handler:de},{semanticName:"Disk Storage",name:"Lined Cylinder",shortName:"lin-cyl",description:"Disk storage",aliases:["disk","lined-cylinder"],handler:qt},{semanticName:"Display",name:"Curved Trapezoid",shortName:"curv-trap",description:"Represents a display",aliases:["curved-trapezoid","display"],handler:ft},{semanticName:"Divided Process",name:"Divided Rectangle",shortName:"div-rect",description:"Divided process shape",aliases:["div-proc","divided-rectangle","divided-process"],handler:bt},{semanticName:"Extract",name:"Triangle",shortName:"tri",description:"Extraction process",aliases:["extract","triangle"],handler:pe},{semanticName:"Internal Storage",name:"Window Pane",shortName:"win-pane",description:"Internal storage",aliases:["internal-storage","window-pane"],handler:ye},{semanticName:"Junction",name:"Filled Circle",shortName:"f-circ",description:"Junction point",aliases:["junction","filled-circle"],handler:Ct},{semanticName:"Loop Limit",name:"Trapezoidal Pentagon",shortName:"notch-pent",description:"Loop limit step",aliases:["loop-limit","notched-pentagon"],handler:fe},{semanticName:"Manual File",name:"Flipped Triangle",shortName:"flip-tri",description:"Manual file operation",aliases:["manual-file","flipped-triangle"],handler:vt},{semanticName:"Manual Input",name:"Sloped Rectangle",shortName:"sl-rect",description:"Manual input step",aliases:["manual-input","sloped-rectangle"],handler:Jt},{semanticName:"Multi-Document",name:"Stacked Document",shortName:"docs",description:"Multiple documents",aliases:["documents","st-doc","stacked-document"],handler:jt},{semanticName:"Multi-Process",name:"Stacked Rectangle",shortName:"st-rect",description:"Multiple processes",aliases:["procs","processes","stacked-rectangle"],handler:Wt},{semanticName:"Stored Data",name:"Bow Tie Rectangle",shortName:"bow-rect",description:"Stored data",aliases:["stored-data","bow-tie-rectangle"],handler:tt},{semanticName:"Summary",name:"Crossed Circle",shortName:"cross-circ",description:"Summary",aliases:["summary","crossed-circle"],handler:ot},{semanticName:"Tagged Document",name:"Tagged Document",shortName:"tag-doc",description:"Tagged document",aliases:["tag-doc","tagged-document"],handler:oe},{semanticName:"Tagged Process",name:"Tagged Rectangle",shortName:"tag-rect",description:"Tagged process",aliases:["tagged-rectangle","tag-proc","tagged-process"],handler:ne},{semanticName:"Paper Tape",name:"Flag",shortName:"flag",description:"Paper tape",aliases:["paper-tape"],handler:me},{semanticName:"Odd",name:"Odd",shortName:"odd",description:"Odd shape",internalAliases:["rect_left_inv_arrow"],handler:Gt},{semanticName:"Lined Document",name:"Lined Document",shortName:"lin-doc",description:"Lined document",aliases:["lined-document"],handler:Nt}];var Le=(0,s.K2)((()=>{const t={state:ee,choice:it,note:Ht,rectWithTitle:Vt,labelRect:Ot,iconSquare:_t,iconCircle:Lt,icon:Bt,iconRounded:Mt,imageSquare:Ft,anchor:J,kanbanItem:Te,classBox:ke,erBox:be,requirementBox:we};const e=[...Object.entries(t),...Be.flatMap((t=>{const e=[t.shortName,..."aliases"in t?t.aliases:[],..."internalAliases"in t?t.internalAliases:[]];return e.map((e=>[e,t.handler]))}))];return Object.fromEntries(e)}),"generateShapeMap");var Me=Le();function _e(t){return t in Me}(0,s.K2)(_e,"isValidShape");var Fe=new Map;async function $e(t,e,r){let i;let a;if(e.shape==="rect"){if(e.rx&&e.ry){e.shape="roundedRect"}else{e.shape="squareRect"}}const n=e.shape?Me[e.shape]:void 0;if(!n){throw new Error(`No such shape: ${e.shape}. Please check your syntax.`)}if(e.link){let o;if(r.config.securityLevel==="sandbox"){o="_top"}else if(e.linkTarget){o=e.linkTarget||"_blank"}i=t.insert("svg:a").attr("xlink:href",e.link).attr("target",o??null);a=await n(i,e,r)}else{a=await n(t,e,r);i=a}if(e.tooltip){a.attr("title",e.tooltip)}Fe.set(e.id,i);if(e.haveCallback){i.attr("class",i.attr("class")+" clickable")}return i}(0,s.K2)($e,"insertNode");var Ee=(0,s.K2)(((t,e)=>{Fe.set(e.id,t)}),"setNodeElem");var Oe=(0,s.K2)((()=>{Fe.clear()}),"clear");var De=(0,s.K2)((t=>{const e=Fe.get(t.id);s.Rm.trace("Transforming node",t.diff,t,"translate("+(t.x-t.width/2-5)+", "+t.width/2+")");const r=8;const i=t.diff||0;if(t.clusterNode){e.attr("transform","translate("+(t.x+i-t.width/2)+", "+(t.y-t.height/2-r)+")")}else{e.attr("transform","translate("+t.x+", "+t.y+")")}return i}),"positionNode")},33416:(t,e,r)=>{"use strict";r.d(e,{IU:()=>m,Jo:()=>B,T_:()=>C,g0:()=>H,jP:()=>b});var i=r(94746);var a=r(20778);var n=r(57590);var o=r(76261);var s=r(96049);var l=r(75905);var c=r(24982);var h=r(52274);var d=(0,l.K2)(((t,e,r,i,a,n)=>{if(e.arrowTypeStart){f(t,"start",e.arrowTypeStart,r,i,a,n)}if(e.arrowTypeEnd){f(t,"end",e.arrowTypeEnd,r,i,a,n)}}),"addEdgeMarkers");var u={arrow_cross:{type:"cross",fill:false},arrow_point:{type:"point",fill:true},arrow_barb:{type:"barb",fill:true},arrow_circle:{type:"circle",fill:false},aggregation:{type:"aggregation",fill:false},extension:{type:"extension",fill:false},composition:{type:"composition",fill:true},dependency:{type:"dependency",fill:true},lollipop:{type:"lollipop",fill:false},only_one:{type:"onlyOne",fill:false},zero_or_one:{type:"zeroOrOne",fill:false},one_or_more:{type:"oneOrMore",fill:false},zero_or_more:{type:"zeroOrMore",fill:false},requirement_arrow:{type:"requirement_arrow",fill:false},requirement_contains:{type:"requirement_contains",fill:false}};var f=(0,l.K2)(((t,e,r,i,a,n,o)=>{const s=u[r];if(!s){l.Rm.warn(`Unknown arrow type: ${r}`);return}const c=s.type;const h=e==="start"?"Start":"End";const d=`${a}_${n}-${c}${h}`;if(o&&o.trim()!==""){const r=o.replace(/[^\dA-Za-z]/g,"_");const a=`${d}_${r}`;if(!document.getElementById(a)){const t=document.getElementById(d);if(t){const e=t.cloneNode(true);e.id=a;const r=e.querySelectorAll("path, circle, line");r.forEach((t=>{t.setAttribute("stroke",o);if(s.fill){t.setAttribute("fill",o)}}));t.parentNode?.appendChild(e)}}t.attr(`marker-${e}`,`url(${i}#${a})`)}else{t.attr(`marker-${e}`,`url(${i}#${d})`)}}),"addEdgeMarker");var p=new Map;var g=new Map;var m=(0,l.K2)((()=>{p.clear();g.clear()}),"clear");var y=(0,l.K2)((t=>{let e=t?t.reduce(((t,e)=>t+";"+e),""):"";return e}),"getLabelStyles");var b=(0,l.K2)((async(t,e)=>{let r=(0,l._3)((0,l.D7)().flowchart.htmlLabels);const i=await(0,o.GZ)(t,e.label,{style:y(e.labelStyle),useHtmlLabels:r,addSvgBackground:true,isNode:false});l.Rm.info("abc82",e,e.labelType);const n=t.insert("g").attr("class","edgeLabel");const s=n.insert("g").attr("class","label");s.node().appendChild(i);let h=i.getBBox();if(r){const t=i.children[0];const e=(0,c.Ltv)(i);h=t.getBoundingClientRect();e.attr("width",h.width);e.attr("height",h.height)}s.attr("transform","translate("+-h.width/2+", "+-h.height/2+")");p.set(e.id,n);e.width=h.width;e.height=h.height;let d;if(e.startLabelLeft){const r=await(0,a.DA)(e.startLabelLeft,y(e.labelStyle));const i=t.insert("g").attr("class","edgeTerminals");const n=i.insert("g").attr("class","inner");d=n.node().appendChild(r);const o=r.getBBox();n.attr("transform","translate("+-o.width/2+", "+-o.height/2+")");if(!g.get(e.id)){g.set(e.id,{})}g.get(e.id).startLeft=i;x(d,e.startLabelLeft)}if(e.startLabelRight){const r=await(0,a.DA)(e.startLabelRight,y(e.labelStyle));const i=t.insert("g").attr("class","edgeTerminals");const n=i.insert("g").attr("class","inner");d=i.node().appendChild(r);n.node().appendChild(r);const o=r.getBBox();n.attr("transform","translate("+-o.width/2+", "+-o.height/2+")");if(!g.get(e.id)){g.set(e.id,{})}g.get(e.id).startRight=i;x(d,e.startLabelRight)}if(e.endLabelLeft){const r=await(0,a.DA)(e.endLabelLeft,y(e.labelStyle));const i=t.insert("g").attr("class","edgeTerminals");const n=i.insert("g").attr("class","inner");d=n.node().appendChild(r);const o=r.getBBox();n.attr("transform","translate("+-o.width/2+", "+-o.height/2+")");i.node().appendChild(r);if(!g.get(e.id)){g.set(e.id,{})}g.get(e.id).endLeft=i;x(d,e.endLabelLeft)}if(e.endLabelRight){const r=await(0,a.DA)(e.endLabelRight,y(e.labelStyle));const i=t.insert("g").attr("class","edgeTerminals");const n=i.insert("g").attr("class","inner");d=n.node().appendChild(r);const o=r.getBBox();n.attr("transform","translate("+-o.width/2+", "+-o.height/2+")");i.node().appendChild(r);if(!g.get(e.id)){g.set(e.id,{})}g.get(e.id).endRight=i;x(d,e.endLabelRight)}return i}),"insertEdgeLabel");function x(t,e){if((0,l.D7)().flowchart.htmlLabels&&t){t.style.width=e.length*9+"px";t.style.height="12px"}}(0,l.K2)(x,"setTerminalWidth");var C=(0,l.K2)(((t,e)=>{l.Rm.debug("Moving label abc88 ",t.id,t.label,p.get(t.id),e);let r=e.updatedPath?e.updatedPath:e.originalPath;const i=(0,l.D7)();const{subGraphTitleTotalMargin:a}=(0,n.O)(i);if(t.label){const i=p.get(t.id);let n=t.x;let o=t.y;if(r){const i=s._K.calcLabelPosition(r);l.Rm.debug("Moving label "+t.label+" from (",n,",",o,") to (",i.x,",",i.y,") abc88");if(e.updatedPath){n=i.x;o=i.y}}i.attr("transform",`translate(${n}, ${o+a/2})`)}if(t.startLabelLeft){const e=g.get(t.id).startLeft;let i=t.x;let a=t.y;if(r){const e=s._K.calcTerminalLabelPosition(t.arrowTypeStart?10:0,"start_left",r);i=e.x;a=e.y}e.attr("transform",`translate(${i}, ${a})`)}if(t.startLabelRight){const e=g.get(t.id).startRight;let i=t.x;let a=t.y;if(r){const e=s._K.calcTerminalLabelPosition(t.arrowTypeStart?10:0,"start_right",r);i=e.x;a=e.y}e.attr("transform",`translate(${i}, ${a})`)}if(t.endLabelLeft){const e=g.get(t.id).endLeft;let i=t.x;let a=t.y;if(r){const e=s._K.calcTerminalLabelPosition(t.arrowTypeEnd?10:0,"end_left",r);i=e.x;a=e.y}e.attr("transform",`translate(${i}, ${a})`)}if(t.endLabelRight){const e=g.get(t.id).endRight;let i=t.x;let a=t.y;if(r){const e=s._K.calcTerminalLabelPosition(t.arrowTypeEnd?10:0,"end_right",r);i=e.x;a=e.y}e.attr("transform",`translate(${i}, ${a})`)}}),"positionEdgeLabel");var v=(0,l.K2)(((t,e)=>{const r=t.x;const i=t.y;const a=Math.abs(e.x-r);const n=Math.abs(e.y-i);const o=t.width/2;const s=t.height/2;return a>=o||n>=s}),"outsideNode");var k=(0,l.K2)(((t,e,r)=>{l.Rm.debug(`intersection calc abc89:\n  outsidePoint: ${JSON.stringify(e)}\n  insidePoint : ${JSON.stringify(r)}\n  node        : x:${t.x} y:${t.y} w:${t.width} h:${t.height}`);const i=t.x;const a=t.y;const n=Math.abs(i-r.x);const o=t.width/2;let s=r.x<e.x?o-n:o+n;const c=t.height/2;const h=Math.abs(e.y-r.y);const d=Math.abs(e.x-r.x);if(Math.abs(a-e.y)*o>Math.abs(i-e.x)*c){let t=r.y<e.y?e.y-c-a:a-c-e.y;s=d*t/h;const i={x:r.x<e.x?r.x+s:r.x-d+s,y:r.y<e.y?r.y+h-t:r.y-h+t};if(s===0){i.x=e.x;i.y=e.y}if(d===0){i.x=e.x}if(h===0){i.y=e.y}l.Rm.debug(`abc89 top/bottom calc, Q ${h}, q ${t}, R ${d}, r ${s}`,i);return i}else{if(r.x<e.x){s=e.x-o-i}else{s=i-o-e.x}let t=h*s/d;let a=r.x<e.x?r.x+d-s:r.x-d+s;let n=r.y<e.y?r.y+t:r.y-t;l.Rm.debug(`sides calc abc89, Q ${h}, q ${t}, R ${d}, r ${s}`,{_x:a,_y:n});if(s===0){a=e.x;n=e.y}if(d===0){a=e.x}if(h===0){n=e.y}return{x:a,y:n}}}),"intersection");var w=(0,l.K2)(((t,e)=>{l.Rm.warn("abc88 cutPathAtIntersect",t,e);let r=[];let i=t[0];let a=false;t.forEach((t=>{l.Rm.info("abc88 checking point",t,e);if(!v(e,t)&&!a){const n=k(e,i,t);l.Rm.debug("abc88 inside",t,i,n);l.Rm.debug("abc88 intersection",n,e);let o=false;r.forEach((t=>{o=o||t.x===n.x&&t.y===n.y}));if(!r.some((t=>t.x===n.x&&t.y===n.y))){r.push(n)}else{l.Rm.warn("abc88 no intersect",n,r)}a=true}else{l.Rm.warn("abc88 outside",t,i);i=t;if(!a){r.push(t)}}}));l.Rm.debug("returning points",r);return r}),"cutPathAtIntersect");function S(t){const e=[];const r=[];for(let i=1;i<t.length-1;i++){const a=t[i-1];const n=t[i];const o=t[i+1];if(a.x===n.x&&n.y===o.y&&Math.abs(n.x-o.x)>5&&Math.abs(n.y-a.y)>5){e.push(n);r.push(i)}else if(a.y===n.y&&n.x===o.x&&Math.abs(n.x-a.x)>5&&Math.abs(n.y-o.y)>5){e.push(n);r.push(i)}}return{cornerPoints:e,cornerPointPositions:r}}(0,l.K2)(S,"extractCornerPoints");var A=(0,l.K2)((function(t,e,r){const i=e.x-t.x;const a=e.y-t.y;const n=Math.sqrt(i*i+a*a);const o=r/n;return{x:e.x-o*i,y:e.y-o*a}}),"findAdjacentPoint");var T=(0,l.K2)((function(t){const{cornerPointPositions:e}=S(t);const r=[];for(let i=0;i<t.length;i++){if(e.includes(i)){const e=t[i-1];const a=t[i+1];const n=t[i];const o=A(e,n,5);const s=A(a,n,5);const c=s.x-o.x;const h=s.y-o.y;r.push(o);const d=Math.sqrt(2)*2;let u={x:n.x,y:n.y};if(Math.abs(a.x-e.x)>10&&Math.abs(a.y-e.y)>=10){l.Rm.debug("Corner point fixing",Math.abs(a.x-e.x),Math.abs(a.y-e.y));const t=5;if(n.x===o.x){u={x:c<0?o.x-t+d:o.x+t-d,y:h<0?o.y-d:o.y+d}}else{u={x:c<0?o.x-d:o.x+d,y:h<0?o.y-t+d:o.y+t-d}}}else{l.Rm.debug("Corner point skipping fixing",Math.abs(a.x-e.x),Math.abs(a.y-e.y))}r.push(u,s)}else{r.push(t[i])}}return r}),"fixCorners");var B=(0,l.K2)((function(t,e,r,n,o,s,u){const{handDrawnSeed:f}=(0,l.D7)();let p=e.points;let g=false;const m=o;var y=s;const b=[];for(const i in e.cssCompiledStyles){if((0,a.KX)(i)){continue}b.push(e.cssCompiledStyles[i])}if(y.intersect&&m.intersect){p=p.slice(1,e.points.length-1);p.unshift(m.intersect(p[0]));l.Rm.debug("Last point APA12",e.start,"--\x3e",e.end,p[p.length-1],y,y.intersect(p[p.length-1]));p.push(y.intersect(p[p.length-1]))}if(e.toCluster){l.Rm.info("to cluster abc88",r.get(e.toCluster));p=w(e.points,r.get(e.toCluster).node);g=true}if(e.fromCluster){l.Rm.debug("from cluster abc88",r.get(e.fromCluster),JSON.stringify(p,null,2));p=w(p.reverse(),r.get(e.fromCluster).node).reverse();g=true}let x=p.filter((t=>!Number.isNaN(t.y)));x=T(x);let C=c.qrM;C=c.lUB;switch(e.curve){case"linear":C=c.lUB;break;case"basis":C=c.qrM;break;case"cardinal":C=c.y8u;break;case"bumpX":C=c.Wi0;break;case"bumpY":C=c.PGM;break;case"catmullRom":C=c.oDi;break;case"monotoneX":C=c.nVG;break;case"monotoneY":C=c.uxU;break;case"natural":C=c.Xf2;break;case"step":C=c.GZz;break;case"stepAfter":C=c.UPb;break;case"stepBefore":C=c.dyv;break;default:C=c.qrM}const{x:v,y:k}=(0,i.R)(e);const S=(0,c.n8j)().x(v).y(k).curve(C);let A;switch(e.thickness){case"normal":A="edge-thickness-normal";break;case"thick":A="edge-thickness-thick";break;case"invisible":A="edge-thickness-invisible";break;default:A="edge-thickness-normal"}switch(e.pattern){case"solid":A+=" edge-pattern-solid";break;case"dotted":A+=" edge-pattern-dotted";break;case"dashed":A+=" edge-pattern-dashed";break;default:A+=" edge-pattern-solid"}let B;let L=S(x);const M=Array.isArray(e.style)?e.style:[e.style];let _=M.find((t=>t?.startsWith("stroke:")));if(e.look==="handDrawn"){const r=h.A.svg(t);Object.assign([],x);const i=r.path(L,{roughness:.3,seed:f});A+=" transition";B=(0,c.Ltv)(i).select("path").attr("id",e.id).attr("class"," "+A+(e.classes?" "+e.classes:"")).attr("style",M?M.reduce(((t,e)=>t+";"+e),""):"");let a=B.attr("d");B.attr("d",a);t.node().appendChild(B.node())}else{const r=b.join(";");const i=M?M.reduce(((t,e)=>t+e+";"),""):"";let a="";if(e.animate){a=" edge-animation-fast"}if(e.animation){a=" edge-animation-"+e.animation}const n=r?r+";"+i+";":i;B=t.append("path").attr("d",L).attr("id",e.id).attr("class"," "+A+(e.classes?" "+e.classes:"")+(a??"")).attr("style",n);_=n.match(/stroke:([^;]+)/)?.[1]}let F="";if((0,l.D7)().flowchart.arrowMarkerAbsolute||(0,l.D7)().state.arrowMarkerAbsolute){F=window.location.protocol+"//"+window.location.host+window.location.pathname+window.location.search;F=F.replace(/\(/g,"\\(").replace(/\)/g,"\\)")}l.Rm.info("arrowTypeStart",e.arrowTypeStart);l.Rm.info("arrowTypeEnd",e.arrowTypeEnd);d(B,e,F,u,n,_);let $={};if(g){$.updatedPath=p}$.originalPath=e.points;return $}),"insertEdge");var L=(0,l.K2)(((t,e,r,i)=>{e.forEach((e=>{j[e](t,r,i)}))}),"insertMarkers");var M=(0,l.K2)(((t,e,r)=>{l.Rm.trace("Making markers for ",r);t.append("defs").append("marker").attr("id",r+"_"+e+"-extensionStart").attr("class","marker extension "+e).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 1,7 L18,13 V 1 Z");t.append("defs").append("marker").attr("id",r+"_"+e+"-extensionEnd").attr("class","marker extension "+e).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 1,1 V 13 L18,7 Z")}),"extension");var _=(0,l.K2)(((t,e,r)=>{t.append("defs").append("marker").attr("id",r+"_"+e+"-compositionStart").attr("class","marker composition "+e).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z");t.append("defs").append("marker").attr("id",r+"_"+e+"-compositionEnd").attr("class","marker composition "+e).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z")}),"composition");var F=(0,l.K2)(((t,e,r)=>{t.append("defs").append("marker").attr("id",r+"_"+e+"-aggregationStart").attr("class","marker aggregation "+e).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z");t.append("defs").append("marker").attr("id",r+"_"+e+"-aggregationEnd").attr("class","marker aggregation "+e).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z")}),"aggregation");var $=(0,l.K2)(((t,e,r)=>{t.append("defs").append("marker").attr("id",r+"_"+e+"-dependencyStart").attr("class","marker dependency "+e).attr("refX",6).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 5,7 L9,13 L1,7 L9,1 Z");t.append("defs").append("marker").attr("id",r+"_"+e+"-dependencyEnd").attr("class","marker dependency "+e).attr("refX",13).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L14,7 L9,1 Z")}),"dependency");var E=(0,l.K2)(((t,e,r)=>{t.append("defs").append("marker").attr("id",r+"_"+e+"-lollipopStart").attr("class","marker lollipop "+e).attr("refX",13).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("circle").attr("stroke","black").attr("fill","transparent").attr("cx",7).attr("cy",7).attr("r",6);t.append("defs").append("marker").attr("id",r+"_"+e+"-lollipopEnd").attr("class","marker lollipop "+e).attr("refX",1).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("circle").attr("stroke","black").attr("fill","transparent").attr("cx",7).attr("cy",7).attr("r",6)}),"lollipop");var O=(0,l.K2)(((t,e,r)=>{t.append("marker").attr("id",r+"_"+e+"-pointEnd").attr("class","marker "+e).attr("viewBox","0 0 10 10").attr("refX",5).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",8).attr("markerHeight",8).attr("orient","auto").append("path").attr("d","M 0 0 L 10 5 L 0 10 z").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0");t.append("marker").attr("id",r+"_"+e+"-pointStart").attr("class","marker "+e).attr("viewBox","0 0 10 10").attr("refX",4.5).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",8).attr("markerHeight",8).attr("orient","auto").append("path").attr("d","M 0 5 L 10 10 L 10 0 z").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0")}),"point");var D=(0,l.K2)(((t,e,r)=>{t.append("marker").attr("id",r+"_"+e+"-circleEnd").attr("class","marker "+e).attr("viewBox","0 0 10 10").attr("refX",11).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("circle").attr("cx","5").attr("cy","5").attr("r","5").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0");t.append("marker").attr("id",r+"_"+e+"-circleStart").attr("class","marker "+e).attr("viewBox","0 0 10 10").attr("refX",-1).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("circle").attr("cx","5").attr("cy","5").attr("r","5").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0")}),"circle");var I=(0,l.K2)(((t,e,r)=>{t.append("marker").attr("id",r+"_"+e+"-crossEnd").attr("class","marker cross "+e).attr("viewBox","0 0 11 11").attr("refX",12).attr("refY",5.2).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("path").attr("d","M 1,1 l 9,9 M 10,1 l -9,9").attr("class","arrowMarkerPath").style("stroke-width",2).style("stroke-dasharray","1,0");t.append("marker").attr("id",r+"_"+e+"-crossStart").attr("class","marker cross "+e).attr("viewBox","0 0 11 11").attr("refX",-1).attr("refY",5.2).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("path").attr("d","M 1,1 l 9,9 M 10,1 l -9,9").attr("class","arrowMarkerPath").style("stroke-width",2).style("stroke-dasharray","1,0")}),"cross");var K=(0,l.K2)(((t,e,r)=>{t.append("defs").append("marker").attr("id",r+"_"+e+"-barbEnd").attr("refX",19).attr("refY",7).attr("markerWidth",20).attr("markerHeight",14).attr("markerUnits","userSpaceOnUse").attr("orient","auto").append("path").attr("d","M 19,7 L9,13 L14,7 L9,1 Z")}),"barb");var R=(0,l.K2)(((t,e,r)=>{t.append("defs").append("marker").attr("id",r+"_"+e+"-onlyOneStart").attr("class","marker onlyOne "+e).attr("refX",0).attr("refY",9).attr("markerWidth",18).attr("markerHeight",18).attr("orient","auto").append("path").attr("d","M9,0 L9,18 M15,0 L15,18");t.append("defs").append("marker").attr("id",r+"_"+e+"-onlyOneEnd").attr("class","marker onlyOne "+e).attr("refX",18).attr("refY",9).attr("markerWidth",18).attr("markerHeight",18).attr("orient","auto").append("path").attr("d","M3,0 L3,18 M9,0 L9,18")}),"only_one");var P=(0,l.K2)(((t,e,r)=>{const i=t.append("defs").append("marker").attr("id",r+"_"+e+"-zeroOrOneStart").attr("class","marker zeroOrOne "+e).attr("refX",0).attr("refY",9).attr("markerWidth",30).attr("markerHeight",18).attr("orient","auto");i.append("circle").attr("fill","white").attr("cx",21).attr("cy",9).attr("r",6);i.append("path").attr("d","M9,0 L9,18");const a=t.append("defs").append("marker").attr("id",r+"_"+e+"-zeroOrOneEnd").attr("class","marker zeroOrOne "+e).attr("refX",30).attr("refY",9).attr("markerWidth",30).attr("markerHeight",18).attr("orient","auto");a.append("circle").attr("fill","white").attr("cx",9).attr("cy",9).attr("r",6);a.append("path").attr("d","M21,0 L21,18")}),"zero_or_one");var z=(0,l.K2)(((t,e,r)=>{t.append("defs").append("marker").attr("id",r+"_"+e+"-oneOrMoreStart").attr("class","marker oneOrMore "+e).attr("refX",18).attr("refY",18).attr("markerWidth",45).attr("markerHeight",36).attr("orient","auto").append("path").attr("d","M0,18 Q 18,0 36,18 Q 18,36 0,18 M42,9 L42,27");t.append("defs").append("marker").attr("id",r+"_"+e+"-oneOrMoreEnd").attr("class","marker oneOrMore "+e).attr("refX",27).attr("refY",18).attr("markerWidth",45).attr("markerHeight",36).attr("orient","auto").append("path").attr("d","M3,9 L3,27 M9,18 Q27,0 45,18 Q27,36 9,18")}),"one_or_more");var q=(0,l.K2)(((t,e,r)=>{const i=t.append("defs").append("marker").attr("id",r+"_"+e+"-zeroOrMoreStart").attr("class","marker zeroOrMore "+e).attr("refX",18).attr("refY",18).attr("markerWidth",57).attr("markerHeight",36).attr("orient","auto");i.append("circle").attr("fill","white").attr("cx",48).attr("cy",18).attr("r",6);i.append("path").attr("d","M0,18 Q18,0 36,18 Q18,36 0,18");const a=t.append("defs").append("marker").attr("id",r+"_"+e+"-zeroOrMoreEnd").attr("class","marker zeroOrMore "+e).attr("refX",39).attr("refY",18).attr("markerWidth",57).attr("markerHeight",36).attr("orient","auto");a.append("circle").attr("fill","white").attr("cx",9).attr("cy",18).attr("r",6);a.append("path").attr("d","M21,18 Q39,0 57,18 Q39,36 21,18")}),"zero_or_more");var N=(0,l.K2)(((t,e,r)=>{t.append("defs").append("marker").attr("id",r+"_"+e+"-requirement_arrowEnd").attr("refX",20).attr("refY",10).attr("markerWidth",20).attr("markerHeight",20).attr("orient","auto").append("path").attr("d",`M0,0\n      L20,10\n      M20,10\n      L0,20`)}),"requirement_arrow");var W=(0,l.K2)(((t,e,r)=>{const i=t.append("defs").append("marker").attr("id",r+"_"+e+"-requirement_containsStart").attr("refX",0).attr("refY",10).attr("markerWidth",20).attr("markerHeight",20).attr("orient","auto").append("g");i.append("circle").attr("cx",10).attr("cy",10).attr("r",9).attr("fill","none");i.append("line").attr("x1",1).attr("x2",19).attr("y1",10).attr("y2",10);i.append("line").attr("y1",1).attr("y2",19).attr("x1",10).attr("x2",10)}),"requirement_contains");var j={extension:M,composition:_,aggregation:F,dependency:$,lollipop:E,point:O,circle:D,cross:I,barb:K,only_one:R,zero_or_one:P,one_or_more:z,zero_or_more:q,requirement_arrow:N,requirement_contains:W};var H=L},57590:(t,e,r)=>{"use strict";r.d(e,{O:()=>a});var i=r(75905);var a=(0,i.K2)((({flowchart:t})=>{const e=t?.subGraphTitleMargin?.top??0;const r=t?.subGraphTitleMargin?.bottom??0;const i=e+r;return{subGraphTitleTopMargin:e,subGraphTitleBottomMargin:r,subGraphTitleTotalMargin:i}}),"getSubGraphTitleMargins")},96049:(t,e,r)=>{"use strict";r.d(e,{$C:()=>B,$t:()=>W,C4:()=>H,I5:()=>N,Ib:()=>g,KL:()=>G,Sm:()=>Y,Un:()=>D,_K:()=>j,bH:()=>$,dq:()=>z,pe:()=>l,rY:()=>U,ru:()=>O,sM:()=>A,vU:()=>f,yT:()=>M});var i=r(75905);var a=r(16750);var n=r(24982);var o=r(307);var s=r(96901);var l="​";var c={curveBasis:n.qrM,curveBasisClosed:n.Yu4,curveBasisOpen:n.IA3,curveBumpX:n.Wi0,curveBumpY:n.PGM,curveBundle:n.OEq,curveCardinalClosed:n.olC,curveCardinalOpen:n.IrU,curveCardinal:n.y8u,curveCatmullRomClosed:n.Q7f,curveCatmullRomOpen:n.cVp,curveCatmullRom:n.oDi,curveLinear:n.lUB,curveLinearClosed:n.Lx9,curveMonotoneX:n.nVG,curveMonotoneY:n.uxU,curveNatural:n.Xf2,curveStep:n.GZz,curveStepAfter:n.UPb,curveStepBefore:n.dyv};var h=/\s*(?:(\w+)(?=:):|(\w+))\s*(?:(\w+)|((?:(?!}%{2}).|\r?\n)*))?\s*(?:}%{2})?/gi;var d=(0,i.K2)((function(t,e){const r=u(t,/(?:init\b)|(?:initialize\b)/);let a={};if(Array.isArray(r)){const t=r.map((t=>t.args));(0,i.$i)(t);a=(0,i.hH)(a,[...t])}else{a=r.args}if(!a){return}let n=(0,i.Ch)(t,e);const o="config";if(a[o]!==void 0){if(n==="flowchart-v2"){n="flowchart"}a[n]=a[o];delete a[o]}return a}),"detectInit");var u=(0,i.K2)((function(t,e=null){try{const r=new RegExp(`[%]{2}(?![{]${h.source})(?=[}][%]{2}).*\n`,"ig");t=t.trim().replace(r,"").replace(/'/gm,'"');i.Rm.debug(`Detecting diagram directive${e!==null?" type:"+e:""} based on the text:${t}`);let a;const n=[];while((a=i.DB.exec(t))!==null){if(a.index===i.DB.lastIndex){i.DB.lastIndex++}if(a&&!e||e&&a[1]?.match(e)||e&&a[2]?.match(e)){const t=a[1]?a[1]:a[2];const e=a[3]?a[3].trim():a[4]?JSON.parse(a[4].trim()):null;n.push({type:t,args:e})}}if(n.length===0){return{type:t,args:null}}return n.length===1?n[0]:n}catch(r){i.Rm.error(`ERROR: ${r.message} - Unable to parse directive type: '${e}' based on the text: '${t}'`);return{type:void 0,args:null}}}),"detectDirective");var f=(0,i.K2)((function(t){return t.replace(i.DB,"")}),"removeDirectives");var p=(0,i.K2)((function(t,e){for(const[r,i]of e.entries()){if(i.match(t)){return r}}return-1}),"isSubstringInArray");function g(t,e){if(!t){return e}const r=`curve${t.charAt(0).toUpperCase()+t.slice(1)}`;return c[r]??e}(0,i.K2)(g,"interpolateToCurve");function m(t,e){const r=t.trim();if(!r){return void 0}if(e.securityLevel!=="loose"){return(0,a.J)(r)}return r}(0,i.K2)(m,"formatUrl");var y=(0,i.K2)(((t,...e)=>{const r=t.split(".");const a=r.length-1;const n=r[a];let o=window;for(let s=0;s<a;s++){o=o[r[s]];if(!o){i.Rm.error(`Function name: ${t} not found in window`);return}}o[n](...e)}),"runFunc");function b(t,e){if(!t||!e){return 0}return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}(0,i.K2)(b,"distance");function x(t){let e;let r=0;t.forEach((t=>{r+=b(t,e);e=t}));const i=r/2;return k(t,i)}(0,i.K2)(x,"traverseEdge");function C(t){if(t.length===1){return t[0]}return x(t)}(0,i.K2)(C,"calcLabelPosition");var v=(0,i.K2)(((t,e=2)=>{const r=Math.pow(10,e);return Math.round(t*r)/r}),"roundNumber");var k=(0,i.K2)(((t,e)=>{let r=void 0;let i=e;for(const a of t){if(r){const t=b(a,r);if(t===0){return r}if(t<i){i-=t}else{const e=i/t;if(e<=0){return r}if(e>=1){return{x:a.x,y:a.y}}if(e>0&&e<1){return{x:v((1-e)*r.x+e*a.x,5),y:v((1-e)*r.y+e*a.y,5)}}}}r=a}throw new Error("Could not find a suitable point for the given distance")}),"calculatePoint");var w=(0,i.K2)(((t,e,r)=>{i.Rm.info(`our points ${JSON.stringify(e)}`);if(e[0]!==r){e=e.reverse()}const a=25;const n=k(e,a);const o=t?10:5;const s=Math.atan2(e[0].y-n.y,e[0].x-n.x);const l={x:0,y:0};l.x=Math.sin(s)*o+(e[0].x+n.x)/2;l.y=-Math.cos(s)*o+(e[0].y+n.y)/2;return l}),"calcCardinalityPosition");function S(t,e,r){const a=structuredClone(r);i.Rm.info("our points",a);if(e!=="start_left"&&e!=="start_right"){a.reverse()}const n=25+t;const o=k(a,n);const s=10+t*.5;const l=Math.atan2(a[0].y-o.y,a[0].x-o.x);const c={x:0,y:0};if(e==="start_left"){c.x=Math.sin(l+Math.PI)*s+(a[0].x+o.x)/2;c.y=-Math.cos(l+Math.PI)*s+(a[0].y+o.y)/2}else if(e==="end_right"){c.x=Math.sin(l-Math.PI)*s+(a[0].x+o.x)/2-5;c.y=-Math.cos(l-Math.PI)*s+(a[0].y+o.y)/2-5}else if(e==="end_left"){c.x=Math.sin(l)*s+(a[0].x+o.x)/2-5;c.y=-Math.cos(l)*s+(a[0].y+o.y)/2-5}else{c.x=Math.sin(l)*s+(a[0].x+o.x)/2;c.y=-Math.cos(l)*s+(a[0].y+o.y)/2}return c}(0,i.K2)(S,"calcTerminalLabelPosition");function A(t){let e="";let r="";for(const i of t){if(i!==void 0){if(i.startsWith("color:")||i.startsWith("text-align:")){r=r+i+";"}else{e=e+i+";"}}}return{style:e,labelStyle:r}}(0,i.K2)(A,"getStylesFromArray");var T=0;var B=(0,i.K2)((()=>{T++;return"id-"+Math.random().toString(36).substr(2,12)+"-"+T}),"generateId");function L(t){let e="";const r="0123456789abcdef";const i=r.length;for(let a=0;a<t;a++){e+=r.charAt(Math.floor(Math.random()*i))}return e}(0,i.K2)(L,"makeRandomHex");var M=(0,i.K2)((t=>L(t.length)),"random");var _=(0,i.K2)((function(){return{x:0,y:0,fill:void 0,anchor:"start",style:"#666",width:100,height:100,textMargin:0,rx:0,ry:0,valign:void 0,text:""}}),"getTextObj");var F=(0,i.K2)((function(t,e){const r=e.text.replace(i.Y2.lineBreakRegex," ");const[,a]=N(e.fontSize);const n=t.append("text");n.attr("x",e.x);n.attr("y",e.y);n.style("text-anchor",e.anchor);n.style("font-family",e.fontFamily);n.style("font-size",a);n.style("font-weight",e.fontWeight);n.attr("fill",e.fill);if(e.class!==void 0){n.attr("class",e.class)}const o=n.append("tspan");o.attr("x",e.x+e.textMargin*2);o.attr("fill",e.fill);o.text(r);return n}),"drawSimpleText");var $=(0,o.A)(((t,e,r)=>{if(!t){return t}r=Object.assign({fontSize:12,fontWeight:400,fontFamily:"Arial",joinWith:"<br/>"},r);if(i.Y2.lineBreakRegex.test(t)){return t}const a=t.split(" ").filter(Boolean);const n=[];let o="";a.forEach(((t,i)=>{const s=D(`${t} `,r);const l=D(o,r);if(s>e){const{hyphenatedStrings:i,remainingWord:a}=E(t,e,"-",r);n.push(o,...i);o=a}else if(l+s>=e){n.push(o);o=t}else{o=[o,t].filter(Boolean).join(" ")}const c=i+1;const h=c===a.length;if(h){n.push(o)}}));return n.filter((t=>t!=="")).join(r.joinWith)}),((t,e,r)=>`${t}${e}${r.fontSize}${r.fontWeight}${r.fontFamily}${r.joinWith}`));var E=(0,o.A)(((t,e,r="-",i)=>{i=Object.assign({fontSize:12,fontWeight:400,fontFamily:"Arial",margin:0},i);const a=[...t];const n=[];let o="";a.forEach(((t,s)=>{const l=`${o}${t}`;const c=D(l,i);if(c>=e){const t=s+1;const e=a.length===t;const i=`${l}${r}`;n.push(e?l:i);o=""}else{o=l}}));return{hyphenatedStrings:n,remainingWord:o}}),((t,e,r="-",i)=>`${t}${e}${r}${i.fontSize}${i.fontWeight}${i.fontFamily}`));function O(t,e){return I(t,e).height}(0,i.K2)(O,"calculateTextHeight");function D(t,e){return I(t,e).width}(0,i.K2)(D,"calculateTextWidth");var I=(0,o.A)(((t,e)=>{const{fontSize:r=12,fontFamily:a="Arial",fontWeight:o=400}=e;if(!t){return{width:0,height:0}}const[,s]=N(r);const c=["sans-serif",a];const h=t.split(i.Y2.lineBreakRegex);const d=[];const u=(0,n.Ltv)("body");if(!u.remove){return{width:0,height:0,lineHeight:0}}const f=u.append("svg");for(const i of c){let t=0;const e={width:0,height:0,lineHeight:0};for(const r of h){const a=_();a.text=r||l;const n=F(f,a).style("font-size",s).style("font-weight",o).style("font-family",i);const c=(n._groups||n)[0][0].getBBox();if(c.width===0&&c.height===0){throw new Error("svg element not in render tree")}e.width=Math.round(Math.max(e.width,c.width));t=Math.round(c.height);e.height+=t;e.lineHeight=Math.round(Math.max(e.lineHeight,t))}d.push(e)}f.remove();const p=isNaN(d[1].height)||isNaN(d[1].width)||isNaN(d[1].lineHeight)||d[0].height>d[1].height&&d[0].width>d[1].width&&d[0].lineHeight>d[1].lineHeight?0:1;return d[p]}),((t,e)=>`${t}${e.fontSize}${e.fontWeight}${e.fontFamily}`));var K=class{constructor(t=false,e){this.count=0;this.count=e?e.length:0;this.next=t?()=>this.count++:()=>Date.now()}static{(0,i.K2)(this,"InitIDGenerator")}};var R;var P=(0,i.K2)((function(t){R=R||document.createElement("div");t=escape(t).replace(/%26/g,"&").replace(/%23/g,"#").replace(/%3B/g,";");R.innerHTML=t;return unescape(R.textContent)}),"entityDecode");function z(t){return"str"in t}(0,i.K2)(z,"isDetailedError");var q=(0,i.K2)(((t,e,r,i)=>{if(!i){return}const a=t.node()?.getBBox();if(!a){return}t.append("text").text(i).attr("text-anchor","middle").attr("x",a.x+a.width/2).attr("y",-r).attr("class",e)}),"insertTitle");var N=(0,i.K2)((t=>{if(typeof t==="number"){return[t,t+"px"]}const e=parseInt(t??"",10);if(Number.isNaN(e)){return[void 0,void 0]}else if(t===String(e)){return[e,t+"px"]}else{return[e,t]}}),"parseFontSize");function W(t,e){return(0,s.A)({},t,e)}(0,i.K2)(W,"cleanAndMerge");var j={assignWithDepth:i.hH,wrapLabel:$,calculateTextHeight:O,calculateTextWidth:D,calculateTextDimensions:I,cleanAndMerge:W,detectInit:d,detectDirective:u,isSubstringInArray:p,interpolateToCurve:g,calcLabelPosition:C,calcCardinalityPosition:w,calcTerminalLabelPosition:S,formatUrl:m,getStylesFromArray:A,generateId:B,random:M,runFunc:y,entityDecode:P,insertTitle:q,parseFontSize:N,InitIDGenerator:K};var H=(0,i.K2)((function(t){let e=t;e=e.replace(/style.*:\S*#.*;/g,(function(t){return t.substring(0,t.length-1)}));e=e.replace(/classDef.*:\S*#.*;/g,(function(t){return t.substring(0,t.length-1)}));e=e.replace(/#\w+;/g,(function(t){const e=t.substring(1,t.length-1);const r=/^\+?\d+$/.test(e);if(r){return"ﬂ°°"+e+"¶ß"}else{return"ﬂ°"+e+"¶ß"}}));return e}),"encodeEntities");var Y=(0,i.K2)((function(t){return t.replace(/ﬂ°°/g,"&#").replace(/ﬂ°/g,"&").replace(/¶ß/g,";")}),"decodeEntities");var U=(0,i.K2)(((t,e,{counter:r=0,prefix:i,suffix:a},n)=>{if(n){return n}return`${i?`${i}_`:""}${t}_${e}_${r}${a?`_${a}`:""}`}),"getEdgeId");function G(t){return t??null}(0,i.K2)(G,"handleUndefinedAttr")},94065:(t,e,r)=>{"use strict";r.d(e,{XX:()=>d,q7:()=>u,sO:()=>c});var i=r(33416);var a=r(20778);var n=r(96049);var o=r(75905);var s={common:o.Y2,getConfig:o.zj,insertCluster:a.U,insertEdge:i.Jo,insertEdgeLabel:i.jP,insertMarkers:i.g0,insertNode:a.on,interpolateToCurve:n.Ib,labelHelper:a.Zk,log:o.Rm,positionEdgeLabel:i.T_};var l={};var c=(0,o.K2)((t=>{for(const e of t){l[e.name]=e}}),"registerLayoutLoaders");var h=(0,o.K2)((()=>{c([{name:"dagre",loader:(0,o.K2)((async()=>await Promise.all([r.e(1838),r.e(2211),r.e(6974)]).then(r.bind(r,66974))),"loader")}])}),"registerDefaultLayoutLoaders");h();var d=(0,o.K2)((async(t,e)=>{if(!(t.layoutAlgorithm in l)){throw new Error(`Unknown layout algorithm: ${t.layoutAlgorithm}`)}const r=l[t.layoutAlgorithm];const i=await r.loader();return i.render(t,e,s,{algorithm:r.algorithm})}),"render");var u=(0,o.K2)(((t="",{fallback:e="dagre"}={})=>{if(t in l){return t}if(e in l){o.Rm.warn(`Layout algorithm ${t} is not registered. Using ${e} as fallback.`);return e}throw new Error(`Both layout algorithms ${t} and ${e} are not registered.`)}),"getRegisteredLayoutAlgorithm")},94746:(t,e,r)=>{"use strict";r.d(e,{R:()=>s});var i=r(75905);var a={aggregation:18,extension:18,composition:18,dependency:6,lollipop:13.5,arrow_point:4};function n(t,e){if(t===void 0||e===void 0){return{angle:0,deltaX:0,deltaY:0}}t=o(t);e=o(e);const[r,i]=[t.x,t.y];const[a,n]=[e.x,e.y];const s=a-r;const l=n-i;return{angle:Math.atan(l/s),deltaX:s,deltaY:l}}(0,i.K2)(n,"calculateDeltaAndAngle");var o=(0,i.K2)((t=>{if(Array.isArray(t)){return{x:t[0],y:t[1]}}return t}),"pointTransformer");var s=(0,i.K2)((t=>({x:(0,i.K2)((function(e,r,i){let s=0;const l=o(i[0]).x<o(i[i.length-1]).x?"left":"right";if(r===0&&Object.hasOwn(a,t.arrowTypeStart)){const{angle:e,deltaX:r}=n(i[0],i[1]);s=a[t.arrowTypeStart]*Math.cos(e)*(r>=0?1:-1)}else if(r===i.length-1&&Object.hasOwn(a,t.arrowTypeEnd)){const{angle:e,deltaX:r}=n(i[i.length-1],i[i.length-2]);s=a[t.arrowTypeEnd]*Math.cos(e)*(r>=0?1:-1)}const c=Math.abs(o(e).x-o(i[i.length-1]).x);const h=Math.abs(o(e).y-o(i[i.length-1]).y);const d=Math.abs(o(e).x-o(i[0]).x);const u=Math.abs(o(e).y-o(i[0]).y);const f=a[t.arrowTypeStart];const p=a[t.arrowTypeEnd];const g=1;if(c<p&&c>0&&h<p){let t=p+g-c;t*=l==="right"?-1:1;s-=t}if(d<f&&d>0&&u<f){let t=f+g-d;t*=l==="right"?-1:1;s+=t}return o(e).x+s}),"x"),y:(0,i.K2)((function(e,r,i){let s=0;const l=o(i[0]).y<o(i[i.length-1]).y?"down":"up";if(r===0&&Object.hasOwn(a,t.arrowTypeStart)){const{angle:e,deltaY:r}=n(i[0],i[1]);s=a[t.arrowTypeStart]*Math.abs(Math.sin(e))*(r>=0?1:-1)}else if(r===i.length-1&&Object.hasOwn(a,t.arrowTypeEnd)){const{angle:e,deltaY:r}=n(i[i.length-1],i[i.length-2]);s=a[t.arrowTypeEnd]*Math.abs(Math.sin(e))*(r>=0?1:-1)}const c=Math.abs(o(e).y-o(i[i.length-1]).y);const h=Math.abs(o(e).x-o(i[i.length-1]).x);const d=Math.abs(o(e).y-o(i[0]).y);const u=Math.abs(o(e).x-o(i[0]).x);const f=a[t.arrowTypeStart];const p=a[t.arrowTypeEnd];const g=1;if(c<p&&c>0&&h<p){let t=p+g-c;t*=l==="up"?-1:1;s-=t}if(d<f&&d>0&&u<f){let t=f+g-d;t*=l==="up"?-1:1;s+=t}return o(e).y+s}),"y")})),"getLineFunctionsWithOffset");if(void 0){const{it:t,expect:e,describe:r}=void 0;r("calculateDeltaAndAngle",(()=>{t("should calculate the angle and deltas between two points",(()=>{e(n([0,0],[0,1])).toStrictEqual({angle:1.5707963267948966,deltaX:0,deltaY:1});e(n([1,0],[0,-1])).toStrictEqual({angle:.7853981633974483,deltaX:-1,deltaY:-1});e(n({x:1,y:0},[0,-1])).toStrictEqual({angle:.7853981633974483,deltaX:-1,deltaY:-1});e(n({x:1,y:0},{x:1,y:0})).toStrictEqual({angle:NaN,deltaX:0,deltaY:0})}));t("should calculate the angle and deltas if one point in undefined",(()=>{e(n(void 0,[0,1])).toStrictEqual({angle:0,deltaX:0,deltaY:0});e(n([0,1],void 0)).toStrictEqual({angle:0,deltaX:0,deltaY:0})}))}))}},75905:(t,e,r)=>{"use strict";r.d(e,{C0:()=>L,VA:()=>C,K2:()=>x,xA:()=>mt,hH:()=>D,Dl:()=>Ht,IU:()=>se,Wt:()=>re,Y2:()=>Ut,a$:()=>Xt,sb:()=>it,ME:()=>be,UI:()=>tt,Ch:()=>_,mW:()=>M,DB:()=>T,_3:()=>Dt,EJ:()=>A,m7:()=>de,iN:()=>ce,zj:()=>pt,D7:()=>me,Gs:()=>Se,J$:()=>E,ab:()=>fe,Q2:()=>ut,P$:()=>j,Wi:()=>jt,H1:()=>kt,Rm:()=>k,QO:()=>Rt,Js:()=>we,Xd:()=>F,VJ:()=>Yt,cL:()=>yt,$i:()=>et,jZ:()=>Lt,oB:()=>xe,wZ:()=>ht,EI:()=>he,SV:()=>le,Nk:()=>ft,XV:()=>ye,ke:()=>ue,He:()=>w,UU:()=>ct,ot:()=>Zt,mj:()=>Ce,tM:()=>ee,H$:()=>V,B6:()=>dt});var i=r(74353);var a=r.n(i);var n=r(63221);var o=r(69745);const s=(t,e)=>{const r=n.A.parse(t);const i={};for(const a in e){if(!e[a])continue;i[a]=r[a]+e[a]}return(0,o.A)(t,i)};const l=s;var c=r(3635);const h=(t,e,r=50)=>{const{r:i,g:a,b:o,a:s}=n.A.parse(t);const{r:l,g:h,b:d,a:u}=n.A.parse(e);const f=r/100;const p=f*2-1;const g=s-u;const m=p*g===-1?p:(p+g)/(1+p*g);const y=(m+1)/2;const b=1-y;const x=i*y+l*b;const C=a*y+h*b;const v=o*y+d*b;const k=s*f+u*(1-f);return(0,c.A)(x,C,v,k)};const d=h;const u=(t,e=100)=>{const r=n.A.parse(t);r.r=255-r.r;r.g=255-r.g;r.b=255-r.b;return d(r,t,e)};const f=u;var p=r(48750);var g=r(77470);var m=r(63170);var y=r(84997);var b=Object.defineProperty;var x=(t,e)=>b(t,"name",{value:e,configurable:true});var C=(t,e)=>{for(var r in e)b(t,r,{get:e[r],enumerable:true})};var v={trace:0,debug:1,info:2,warn:3,error:4,fatal:5};var k={trace:x(((...t)=>{}),"trace"),debug:x(((...t)=>{}),"debug"),info:x(((...t)=>{}),"info"),warn:x(((...t)=>{}),"warn"),error:x(((...t)=>{}),"error"),fatal:x(((...t)=>{}),"fatal")};var w=x((function(t="fatal"){let e=v.fatal;if(typeof t==="string"){if(t.toLowerCase()in v){e=v[t]}}else if(typeof t==="number"){e=t}k.trace=()=>{};k.debug=()=>{};k.info=()=>{};k.warn=()=>{};k.error=()=>{};k.fatal=()=>{};if(e<=v.fatal){k.fatal=console.error?console.error.bind(console,S("FATAL"),"color: orange"):console.log.bind(console,"[35m",S("FATAL"))}if(e<=v.error){k.error=console.error?console.error.bind(console,S("ERROR"),"color: orange"):console.log.bind(console,"[31m",S("ERROR"))}if(e<=v.warn){k.warn=console.warn?console.warn.bind(console,S("WARN"),"color: orange"):console.log.bind(console,`[33m`,S("WARN"))}if(e<=v.info){k.info=console.info?console.info.bind(console,S("INFO"),"color: lightblue"):console.log.bind(console,"[34m",S("INFO"))}if(e<=v.debug){k.debug=console.debug?console.debug.bind(console,S("DEBUG"),"color: lightgreen"):console.log.bind(console,"[32m",S("DEBUG"))}if(e<=v.trace){k.trace=console.debug?console.debug.bind(console,S("TRACE"),"color: lightgreen"):console.log.bind(console,"[32m",S("TRACE"))}}),"setLogLevel");var S=x((t=>{const e=a()().format("ss.SSS");return`%c${e} : ${t} : `}),"format");var A=/^-{3}\s*[\n\r](.*?)[\n\r]-{3}\s*[\n\r]+/s;var T=/%{2}{\s*(?:(\w+)\s*:|(\w+))\s*(?:(\w+)|((?:(?!}%{2}).|\r?\n)*))?\s*(?:}%{2})?/gi;var B=/\s*%%.*\n/gm;var L=class extends Error{static{x(this,"UnknownDiagramError")}constructor(t){super(t);this.name="UnknownDiagramError"}};var M={};var _=x((function(t,e){t=t.replace(A,"").replace(T,"").replace(B,"\n");for(const[r,{detector:i}]of Object.entries(M)){const a=i(t,e);if(a){return r}}throw new L(`No diagram type detected matching given configuration for text: ${t}`)}),"detectType");var F=x(((...t)=>{for(const{id:e,detector:r,loader:i}of t){$(e,r,i)}}),"registerLazyLoadedDiagrams");var $=x(((t,e,r)=>{if(M[t]){k.warn(`Detector with key ${t} already exists. Overwriting.`)}M[t]={detector:e,loader:r};k.debug(`Detector with key ${t} added${r?" with loader":""}`)}),"addDetector");var E=x((t=>M[t].loader),"getDiagramLoader");var O=x(((t,e,{depth:r=2,clobber:i=false}={})=>{const a={depth:r,clobber:i};if(Array.isArray(e)&&!Array.isArray(t)){e.forEach((e=>O(t,e,a)));return t}else if(Array.isArray(e)&&Array.isArray(t)){e.forEach((e=>{if(!t.includes(e)){t.push(e)}}));return t}if(t===void 0||r<=0){if(t!==void 0&&t!==null&&typeof t==="object"&&typeof e==="object"){return Object.assign(t,e)}else{return e}}if(e!==void 0&&typeof t==="object"&&typeof e==="object"){Object.keys(e).forEach((a=>{if(typeof e[a]==="object"&&(t[a]===void 0||typeof t[a]==="object")){if(t[a]===void 0){t[a]=Array.isArray(e[a])?[]:{}}t[a]=O(t[a],e[a],{depth:r-1,clobber:i})}else if(i||typeof t[a]!=="object"&&typeof e[a]!=="object"){t[a]=e[a]}}))}return t}),"assignWithDepth");var D=O;var I="#ffffff";var K="#f2f2f2";var R=x(((t,e)=>e?l(t,{s:-40,l:10}):l(t,{s:-40,l:-10})),"mkBorder");var P=class{static{x(this,"Theme")}constructor(){this.background="#f4f4f4";this.primaryColor="#fff4dd";this.noteBkgColor="#fff5ad";this.noteTextColor="#333";this.THEME_COLOR_LIMIT=12;this.fontFamily='"trebuchet ms", verdana, arial, sans-serif';this.fontSize="16px"}updateColors(){this.primaryTextColor=this.primaryTextColor||(this.darkMode?"#eee":"#333");this.secondaryColor=this.secondaryColor||l(this.primaryColor,{h:-120});this.tertiaryColor=this.tertiaryColor||l(this.primaryColor,{h:180,l:5});this.primaryBorderColor=this.primaryBorderColor||R(this.primaryColor,this.darkMode);this.secondaryBorderColor=this.secondaryBorderColor||R(this.secondaryColor,this.darkMode);this.tertiaryBorderColor=this.tertiaryBorderColor||R(this.tertiaryColor,this.darkMode);this.noteBorderColor=this.noteBorderColor||R(this.noteBkgColor,this.darkMode);this.noteBkgColor=this.noteBkgColor||"#fff5ad";this.noteTextColor=this.noteTextColor||"#333";this.secondaryTextColor=this.secondaryTextColor||f(this.secondaryColor);this.tertiaryTextColor=this.tertiaryTextColor||f(this.tertiaryColor);this.lineColor=this.lineColor||f(this.background);this.arrowheadColor=this.arrowheadColor||f(this.background);this.textColor=this.textColor||this.primaryTextColor;this.border2=this.border2||this.tertiaryBorderColor;this.nodeBkg=this.nodeBkg||this.primaryColor;this.mainBkg=this.mainBkg||this.primaryColor;this.nodeBorder=this.nodeBorder||this.primaryBorderColor;this.clusterBkg=this.clusterBkg||this.tertiaryColor;this.clusterBorder=this.clusterBorder||this.tertiaryBorderColor;this.defaultLinkColor=this.defaultLinkColor||this.lineColor;this.titleColor=this.titleColor||this.tertiaryTextColor;this.edgeLabelBackground=this.edgeLabelBackground||(this.darkMode?(0,p.A)(this.secondaryColor,30):this.secondaryColor);this.nodeTextColor=this.nodeTextColor||this.primaryTextColor;this.actorBorder=this.actorBorder||this.primaryBorderColor;this.actorBkg=this.actorBkg||this.mainBkg;this.actorTextColor=this.actorTextColor||this.primaryTextColor;this.actorLineColor=this.actorLineColor||this.actorBorder;this.labelBoxBkgColor=this.labelBoxBkgColor||this.actorBkg;this.signalColor=this.signalColor||this.textColor;this.signalTextColor=this.signalTextColor||this.textColor;this.labelBoxBorderColor=this.labelBoxBorderColor||this.actorBorder;this.labelTextColor=this.labelTextColor||this.actorTextColor;this.loopTextColor=this.loopTextColor||this.actorTextColor;this.activationBorderColor=this.activationBorderColor||(0,p.A)(this.secondaryColor,10);this.activationBkgColor=this.activationBkgColor||this.secondaryColor;this.sequenceNumberColor=this.sequenceNumberColor||f(this.lineColor);this.sectionBkgColor=this.sectionBkgColor||this.tertiaryColor;this.altSectionBkgColor=this.altSectionBkgColor||"white";this.sectionBkgColor=this.sectionBkgColor||this.secondaryColor;this.sectionBkgColor2=this.sectionBkgColor2||this.primaryColor;this.excludeBkgColor=this.excludeBkgColor||"#eeeeee";this.taskBorderColor=this.taskBorderColor||this.primaryBorderColor;this.taskBkgColor=this.taskBkgColor||this.primaryColor;this.activeTaskBorderColor=this.activeTaskBorderColor||this.primaryColor;this.activeTaskBkgColor=this.activeTaskBkgColor||(0,g.A)(this.primaryColor,23);this.gridColor=this.gridColor||"lightgrey";this.doneTaskBkgColor=this.doneTaskBkgColor||"lightgrey";this.doneTaskBorderColor=this.doneTaskBorderColor||"grey";this.critBorderColor=this.critBorderColor||"#ff8888";this.critBkgColor=this.critBkgColor||"red";this.todayLineColor=this.todayLineColor||"red";this.taskTextColor=this.taskTextColor||this.textColor;this.taskTextOutsideColor=this.taskTextOutsideColor||this.textColor;this.taskTextLightColor=this.taskTextLightColor||this.textColor;this.taskTextColor=this.taskTextColor||this.primaryTextColor;this.taskTextDarkColor=this.taskTextDarkColor||this.textColor;this.taskTextClickableColor=this.taskTextClickableColor||"#003163";this.personBorder=this.personBorder||this.primaryBorderColor;this.personBkg=this.personBkg||this.mainBkg;if(this.darkMode){this.rowOdd=this.rowOdd||(0,p.A)(this.mainBkg,5)||"#ffffff";this.rowEven=this.rowEven||(0,p.A)(this.mainBkg,10)}else{this.rowOdd=this.rowOdd||(0,g.A)(this.mainBkg,75)||"#ffffff";this.rowEven=this.rowEven||(0,g.A)(this.mainBkg,5)}this.transitionColor=this.transitionColor||this.lineColor;this.transitionLabelColor=this.transitionLabelColor||this.textColor;this.stateLabelColor=this.stateLabelColor||this.stateBkg||this.primaryTextColor;this.stateBkg=this.stateBkg||this.mainBkg;this.labelBackgroundColor=this.labelBackgroundColor||this.stateBkg;this.compositeBackground=this.compositeBackground||this.background||this.tertiaryColor;this.altBackground=this.altBackground||this.tertiaryColor;this.compositeTitleBackground=this.compositeTitleBackground||this.mainBkg;this.compositeBorder=this.compositeBorder||this.nodeBorder;this.innerEndBackground=this.nodeBorder;this.errorBkgColor=this.errorBkgColor||this.tertiaryColor;this.errorTextColor=this.errorTextColor||this.tertiaryTextColor;this.transitionColor=this.transitionColor||this.lineColor;this.specialStateColor=this.lineColor;this.cScale0=this.cScale0||this.primaryColor;this.cScale1=this.cScale1||this.secondaryColor;this.cScale2=this.cScale2||this.tertiaryColor;this.cScale3=this.cScale3||l(this.primaryColor,{h:30});this.cScale4=this.cScale4||l(this.primaryColor,{h:60});this.cScale5=this.cScale5||l(this.primaryColor,{h:90});this.cScale6=this.cScale6||l(this.primaryColor,{h:120});this.cScale7=this.cScale7||l(this.primaryColor,{h:150});this.cScale8=this.cScale8||l(this.primaryColor,{h:210,l:150});this.cScale9=this.cScale9||l(this.primaryColor,{h:270});this.cScale10=this.cScale10||l(this.primaryColor,{h:300});this.cScale11=this.cScale11||l(this.primaryColor,{h:330});if(this.darkMode){for(let t=0;t<this.THEME_COLOR_LIMIT;t++){this["cScale"+t]=(0,p.A)(this["cScale"+t],75)}}else{for(let t=0;t<this.THEME_COLOR_LIMIT;t++){this["cScale"+t]=(0,p.A)(this["cScale"+t],25)}}for(let e=0;e<this.THEME_COLOR_LIMIT;e++){this["cScaleInv"+e]=this["cScaleInv"+e]||f(this["cScale"+e])}for(let e=0;e<this.THEME_COLOR_LIMIT;e++){if(this.darkMode){this["cScalePeer"+e]=this["cScalePeer"+e]||(0,g.A)(this["cScale"+e],10)}else{this["cScalePeer"+e]=this["cScalePeer"+e]||(0,p.A)(this["cScale"+e],10)}}this.scaleLabelColor=this.scaleLabelColor||this.labelTextColor;for(let e=0;e<this.THEME_COLOR_LIMIT;e++){this["cScaleLabel"+e]=this["cScaleLabel"+e]||this.scaleLabelColor}const t=this.darkMode?-4:-1;for(let e=0;e<5;e++){this["surface"+e]=this["surface"+e]||l(this.mainBkg,{h:180,s:-15,l:t*(5+e*3)});this["surfacePeer"+e]=this["surfacePeer"+e]||l(this.mainBkg,{h:180,s:-15,l:t*(8+e*3)})}this.classText=this.classText||this.textColor;this.fillType0=this.fillType0||this.primaryColor;this.fillType1=this.fillType1||this.secondaryColor;this.fillType2=this.fillType2||l(this.primaryColor,{h:64});this.fillType3=this.fillType3||l(this.secondaryColor,{h:64});this.fillType4=this.fillType4||l(this.primaryColor,{h:-64});this.fillType5=this.fillType5||l(this.secondaryColor,{h:-64});this.fillType6=this.fillType6||l(this.primaryColor,{h:128});this.fillType7=this.fillType7||l(this.secondaryColor,{h:128});this.pie1=this.pie1||this.primaryColor;this.pie2=this.pie2||this.secondaryColor;this.pie3=this.pie3||this.tertiaryColor;this.pie4=this.pie4||l(this.primaryColor,{l:-10});this.pie5=this.pie5||l(this.secondaryColor,{l:-10});this.pie6=this.pie6||l(this.tertiaryColor,{l:-10});this.pie7=this.pie7||l(this.primaryColor,{h:60,l:-10});this.pie8=this.pie8||l(this.primaryColor,{h:-60,l:-10});this.pie9=this.pie9||l(this.primaryColor,{h:120,l:0});this.pie10=this.pie10||l(this.primaryColor,{h:60,l:-20});this.pie11=this.pie11||l(this.primaryColor,{h:-60,l:-20});this.pie12=this.pie12||l(this.primaryColor,{h:120,l:-10});this.pieTitleTextSize=this.pieTitleTextSize||"25px";this.pieTitleTextColor=this.pieTitleTextColor||this.taskTextDarkColor;this.pieSectionTextSize=this.pieSectionTextSize||"17px";this.pieSectionTextColor=this.pieSectionTextColor||this.textColor;this.pieLegendTextSize=this.pieLegendTextSize||"17px";this.pieLegendTextColor=this.pieLegendTextColor||this.taskTextDarkColor;this.pieStrokeColor=this.pieStrokeColor||"black";this.pieStrokeWidth=this.pieStrokeWidth||"2px";this.pieOuterStrokeWidth=this.pieOuterStrokeWidth||"2px";this.pieOuterStrokeColor=this.pieOuterStrokeColor||"black";this.pieOpacity=this.pieOpacity||"0.7";this.radar={axisColor:this.radar?.axisColor||this.lineColor,axisStrokeWidth:this.radar?.axisStrokeWidth||2,axisLabelFontSize:this.radar?.axisLabelFontSize||12,curveOpacity:this.radar?.curveOpacity||.5,curveStrokeWidth:this.radar?.curveStrokeWidth||2,graticuleColor:this.radar?.graticuleColor||"#DEDEDE",graticuleStrokeWidth:this.radar?.graticuleStrokeWidth||1,graticuleOpacity:this.radar?.graticuleOpacity||.3,legendBoxSize:this.radar?.legendBoxSize||12,legendFontSize:this.radar?.legendFontSize||12};this.archEdgeColor=this.archEdgeColor||"#777";this.archEdgeArrowColor=this.archEdgeArrowColor||"#777";this.archEdgeWidth=this.archEdgeWidth||"3";this.archGroupBorderColor=this.archGroupBorderColor||"#000";this.archGroupBorderWidth=this.archGroupBorderWidth||"2px";this.quadrant1Fill=this.quadrant1Fill||this.primaryColor;this.quadrant2Fill=this.quadrant2Fill||l(this.primaryColor,{r:5,g:5,b:5});this.quadrant3Fill=this.quadrant3Fill||l(this.primaryColor,{r:10,g:10,b:10});this.quadrant4Fill=this.quadrant4Fill||l(this.primaryColor,{r:15,g:15,b:15});this.quadrant1TextFill=this.quadrant1TextFill||this.primaryTextColor;this.quadrant2TextFill=this.quadrant2TextFill||l(this.primaryTextColor,{r:-5,g:-5,b:-5});this.quadrant3TextFill=this.quadrant3TextFill||l(this.primaryTextColor,{r:-10,g:-10,b:-10});this.quadrant4TextFill=this.quadrant4TextFill||l(this.primaryTextColor,{r:-15,g:-15,b:-15});this.quadrantPointFill=this.quadrantPointFill||(0,m.A)(this.quadrant1Fill)?(0,g.A)(this.quadrant1Fill):(0,p.A)(this.quadrant1Fill);this.quadrantPointTextFill=this.quadrantPointTextFill||this.primaryTextColor;this.quadrantXAxisTextFill=this.quadrantXAxisTextFill||this.primaryTextColor;this.quadrantYAxisTextFill=this.quadrantYAxisTextFill||this.primaryTextColor;this.quadrantInternalBorderStrokeFill=this.quadrantInternalBorderStrokeFill||this.primaryBorderColor;this.quadrantExternalBorderStrokeFill=this.quadrantExternalBorderStrokeFill||this.primaryBorderColor;this.quadrantTitleFill=this.quadrantTitleFill||this.primaryTextColor;this.xyChart={backgroundColor:this.xyChart?.backgroundColor||this.background,titleColor:this.xyChart?.titleColor||this.primaryTextColor,xAxisTitleColor:this.xyChart?.xAxisTitleColor||this.primaryTextColor,xAxisLabelColor:this.xyChart?.xAxisLabelColor||this.primaryTextColor,xAxisTickColor:this.xyChart?.xAxisTickColor||this.primaryTextColor,xAxisLineColor:this.xyChart?.xAxisLineColor||this.primaryTextColor,yAxisTitleColor:this.xyChart?.yAxisTitleColor||this.primaryTextColor,yAxisLabelColor:this.xyChart?.yAxisLabelColor||this.primaryTextColor,yAxisTickColor:this.xyChart?.yAxisTickColor||this.primaryTextColor,yAxisLineColor:this.xyChart?.yAxisLineColor||this.primaryTextColor,plotColorPalette:this.xyChart?.plotColorPalette||"#FFF4DD,#FFD8B1,#FFA07A,#ECEFF1,#D6DBDF,#C3E0A8,#FFB6A4,#FFD74D,#738FA7,#FFFFF0"};this.requirementBackground=this.requirementBackground||this.primaryColor;this.requirementBorderColor=this.requirementBorderColor||this.primaryBorderColor;this.requirementBorderSize=this.requirementBorderSize||"1";this.requirementTextColor=this.requirementTextColor||this.primaryTextColor;this.relationColor=this.relationColor||this.lineColor;this.relationLabelBackground=this.relationLabelBackground||(this.darkMode?(0,p.A)(this.secondaryColor,30):this.secondaryColor);this.relationLabelColor=this.relationLabelColor||this.actorTextColor;this.git0=this.git0||this.primaryColor;this.git1=this.git1||this.secondaryColor;this.git2=this.git2||this.tertiaryColor;this.git3=this.git3||l(this.primaryColor,{h:-30});this.git4=this.git4||l(this.primaryColor,{h:-60});this.git5=this.git5||l(this.primaryColor,{h:-90});this.git6=this.git6||l(this.primaryColor,{h:60});this.git7=this.git7||l(this.primaryColor,{h:120});if(this.darkMode){this.git0=(0,g.A)(this.git0,25);this.git1=(0,g.A)(this.git1,25);this.git2=(0,g.A)(this.git2,25);this.git3=(0,g.A)(this.git3,25);this.git4=(0,g.A)(this.git4,25);this.git5=(0,g.A)(this.git5,25);this.git6=(0,g.A)(this.git6,25);this.git7=(0,g.A)(this.git7,25)}else{this.git0=(0,p.A)(this.git0,25);this.git1=(0,p.A)(this.git1,25);this.git2=(0,p.A)(this.git2,25);this.git3=(0,p.A)(this.git3,25);this.git4=(0,p.A)(this.git4,25);this.git5=(0,p.A)(this.git5,25);this.git6=(0,p.A)(this.git6,25);this.git7=(0,p.A)(this.git7,25)}this.gitInv0=this.gitInv0||f(this.git0);this.gitInv1=this.gitInv1||f(this.git1);this.gitInv2=this.gitInv2||f(this.git2);this.gitInv3=this.gitInv3||f(this.git3);this.gitInv4=this.gitInv4||f(this.git4);this.gitInv5=this.gitInv5||f(this.git5);this.gitInv6=this.gitInv6||f(this.git6);this.gitInv7=this.gitInv7||f(this.git7);this.branchLabelColor=this.branchLabelColor||(this.darkMode?"black":this.labelTextColor);this.gitBranchLabel0=this.gitBranchLabel0||this.branchLabelColor;this.gitBranchLabel1=this.gitBranchLabel1||this.branchLabelColor;this.gitBranchLabel2=this.gitBranchLabel2||this.branchLabelColor;this.gitBranchLabel3=this.gitBranchLabel3||this.branchLabelColor;this.gitBranchLabel4=this.gitBranchLabel4||this.branchLabelColor;this.gitBranchLabel5=this.gitBranchLabel5||this.branchLabelColor;this.gitBranchLabel6=this.gitBranchLabel6||this.branchLabelColor;this.gitBranchLabel7=this.gitBranchLabel7||this.branchLabelColor;this.tagLabelColor=this.tagLabelColor||this.primaryTextColor;this.tagLabelBackground=this.tagLabelBackground||this.primaryColor;this.tagLabelBorder=this.tagBorder||this.primaryBorderColor;this.tagLabelFontSize=this.tagLabelFontSize||"10px";this.commitLabelColor=this.commitLabelColor||this.secondaryTextColor;this.commitLabelBackground=this.commitLabelBackground||this.secondaryColor;this.commitLabelFontSize=this.commitLabelFontSize||"10px";this.attributeBackgroundColorOdd=this.attributeBackgroundColorOdd||I;this.attributeBackgroundColorEven=this.attributeBackgroundColorEven||K}calculate(t){if(typeof t!=="object"){this.updateColors();return}const e=Object.keys(t);e.forEach((e=>{this[e]=t[e]}));this.updateColors();e.forEach((e=>{this[e]=t[e]}))}};var z=x((t=>{const e=new P;e.calculate(t);return e}),"getThemeVariables");var q=class{static{x(this,"Theme")}constructor(){this.background="#333";this.primaryColor="#1f2020";this.secondaryColor=(0,g.A)(this.primaryColor,16);this.tertiaryColor=l(this.primaryColor,{h:-160});this.primaryBorderColor=f(this.background);this.secondaryBorderColor=R(this.secondaryColor,this.darkMode);this.tertiaryBorderColor=R(this.tertiaryColor,this.darkMode);this.primaryTextColor=f(this.primaryColor);this.secondaryTextColor=f(this.secondaryColor);this.tertiaryTextColor=f(this.tertiaryColor);this.lineColor=f(this.background);this.textColor=f(this.background);this.mainBkg="#1f2020";this.secondBkg="calculated";this.mainContrastColor="lightgrey";this.darkTextColor=(0,g.A)(f("#323D47"),10);this.lineColor="calculated";this.border1="#ccc";this.border2=(0,c.A)(255,255,255,.25);this.arrowheadColor="calculated";this.fontFamily='"trebuchet ms", verdana, arial, sans-serif';this.fontSize="16px";this.labelBackground="#181818";this.textColor="#ccc";this.THEME_COLOR_LIMIT=12;this.nodeBkg="calculated";this.nodeBorder="calculated";this.clusterBkg="calculated";this.clusterBorder="calculated";this.defaultLinkColor="calculated";this.titleColor="#F9FFFE";this.edgeLabelBackground="calculated";this.actorBorder="calculated";this.actorBkg="calculated";this.actorTextColor="calculated";this.actorLineColor="calculated";this.signalColor="calculated";this.signalTextColor="calculated";this.labelBoxBkgColor="calculated";this.labelBoxBorderColor="calculated";this.labelTextColor="calculated";this.loopTextColor="calculated";this.noteBorderColor="calculated";this.noteBkgColor="#fff5ad";this.noteTextColor="calculated";this.activationBorderColor="calculated";this.activationBkgColor="calculated";this.sequenceNumberColor="black";this.sectionBkgColor=(0,p.A)("#EAE8D9",30);this.altSectionBkgColor="calculated";this.sectionBkgColor2="#EAE8D9";this.excludeBkgColor=(0,p.A)(this.sectionBkgColor,10);this.taskBorderColor=(0,c.A)(255,255,255,70);this.taskBkgColor="calculated";this.taskTextColor="calculated";this.taskTextLightColor="calculated";this.taskTextOutsideColor="calculated";this.taskTextClickableColor="#003163";this.activeTaskBorderColor=(0,c.A)(255,255,255,50);this.activeTaskBkgColor="#81B1DB";this.gridColor="calculated";this.doneTaskBkgColor="calculated";this.doneTaskBorderColor="grey";this.critBorderColor="#E83737";this.critBkgColor="#E83737";this.taskTextDarkColor="calculated";this.todayLineColor="#DB5757";this.personBorder=this.primaryBorderColor;this.personBkg=this.mainBkg;this.archEdgeColor="calculated";this.archEdgeArrowColor="calculated";this.archEdgeWidth="3";this.archGroupBorderColor=this.primaryBorderColor;this.archGroupBorderWidth="2px";this.rowOdd=this.rowOdd||(0,g.A)(this.mainBkg,5)||"#ffffff";this.rowEven=this.rowEven||(0,p.A)(this.mainBkg,10);this.labelColor="calculated";this.errorBkgColor="#a44141";this.errorTextColor="#ddd"}updateColors(){this.secondBkg=(0,g.A)(this.mainBkg,16);this.lineColor=this.mainContrastColor;this.arrowheadColor=this.mainContrastColor;this.nodeBkg=this.mainBkg;this.nodeBorder=this.border1;this.clusterBkg=this.secondBkg;this.clusterBorder=this.border2;this.defaultLinkColor=this.lineColor;this.edgeLabelBackground=(0,g.A)(this.labelBackground,25);this.actorBorder=this.border1;this.actorBkg=this.mainBkg;this.actorTextColor=this.mainContrastColor;this.actorLineColor=this.actorBorder;this.signalColor=this.mainContrastColor;this.signalTextColor=this.mainContrastColor;this.labelBoxBkgColor=this.actorBkg;this.labelBoxBorderColor=this.actorBorder;this.labelTextColor=this.mainContrastColor;this.loopTextColor=this.mainContrastColor;this.noteBorderColor=this.secondaryBorderColor;this.noteBkgColor=this.secondBkg;this.noteTextColor=this.secondaryTextColor;this.activationBorderColor=this.border1;this.activationBkgColor=this.secondBkg;this.altSectionBkgColor=this.background;this.taskBkgColor=(0,g.A)(this.mainBkg,23);this.taskTextColor=this.darkTextColor;this.taskTextLightColor=this.mainContrastColor;this.taskTextOutsideColor=this.taskTextLightColor;this.gridColor=this.mainContrastColor;this.doneTaskBkgColor=this.mainContrastColor;this.taskTextDarkColor=this.darkTextColor;this.archEdgeColor=this.lineColor;this.archEdgeArrowColor=this.lineColor;this.transitionColor=this.transitionColor||this.lineColor;this.transitionLabelColor=this.transitionLabelColor||this.textColor;this.stateLabelColor=this.stateLabelColor||this.stateBkg||this.primaryTextColor;this.stateBkg=this.stateBkg||this.mainBkg;this.labelBackgroundColor=this.labelBackgroundColor||this.stateBkg;this.compositeBackground=this.compositeBackground||this.background||this.tertiaryColor;this.altBackground=this.altBackground||"#555";this.compositeTitleBackground=this.compositeTitleBackground||this.mainBkg;this.compositeBorder=this.compositeBorder||this.nodeBorder;this.innerEndBackground=this.primaryBorderColor;this.specialStateColor="#f4f4f4";this.errorBkgColor=this.errorBkgColor||this.tertiaryColor;this.errorTextColor=this.errorTextColor||this.tertiaryTextColor;this.fillType0=this.primaryColor;this.fillType1=this.secondaryColor;this.fillType2=l(this.primaryColor,{h:64});this.fillType3=l(this.secondaryColor,{h:64});this.fillType4=l(this.primaryColor,{h:-64});this.fillType5=l(this.secondaryColor,{h:-64});this.fillType6=l(this.primaryColor,{h:128});this.fillType7=l(this.secondaryColor,{h:128});this.cScale1=this.cScale1||"#0b0000";this.cScale2=this.cScale2||"#4d1037";this.cScale3=this.cScale3||"#3f5258";this.cScale4=this.cScale4||"#4f2f1b";this.cScale5=this.cScale5||"#6e0a0a";this.cScale6=this.cScale6||"#3b0048";this.cScale7=this.cScale7||"#995a01";this.cScale8=this.cScale8||"#154706";this.cScale9=this.cScale9||"#161722";this.cScale10=this.cScale10||"#00296f";this.cScale11=this.cScale11||"#01629c";this.cScale12=this.cScale12||"#010029";this.cScale0=this.cScale0||this.primaryColor;this.cScale1=this.cScale1||this.secondaryColor;this.cScale2=this.cScale2||this.tertiaryColor;this.cScale3=this.cScale3||l(this.primaryColor,{h:30});this.cScale4=this.cScale4||l(this.primaryColor,{h:60});this.cScale5=this.cScale5||l(this.primaryColor,{h:90});this.cScale6=this.cScale6||l(this.primaryColor,{h:120});this.cScale7=this.cScale7||l(this.primaryColor,{h:150});this.cScale8=this.cScale8||l(this.primaryColor,{h:210});this.cScale9=this.cScale9||l(this.primaryColor,{h:270});this.cScale10=this.cScale10||l(this.primaryColor,{h:300});this.cScale11=this.cScale11||l(this.primaryColor,{h:330});for(let t=0;t<this.THEME_COLOR_LIMIT;t++){this["cScaleInv"+t]=this["cScaleInv"+t]||f(this["cScale"+t])}for(let t=0;t<this.THEME_COLOR_LIMIT;t++){this["cScalePeer"+t]=this["cScalePeer"+t]||(0,g.A)(this["cScale"+t],10)}for(let t=0;t<5;t++){this["surface"+t]=this["surface"+t]||l(this.mainBkg,{h:30,s:-30,l:-(-10+t*4)});this["surfacePeer"+t]=this["surfacePeer"+t]||l(this.mainBkg,{h:30,s:-30,l:-(-7+t*4)})}this.scaleLabelColor=this.scaleLabelColor||(this.darkMode?"black":this.labelTextColor);for(let t=0;t<this.THEME_COLOR_LIMIT;t++){this["cScaleLabel"+t]=this["cScaleLabel"+t]||this.scaleLabelColor}for(let t=0;t<this.THEME_COLOR_LIMIT;t++){this["pie"+t]=this["cScale"+t]}this.pieTitleTextSize=this.pieTitleTextSize||"25px";this.pieTitleTextColor=this.pieTitleTextColor||this.taskTextDarkColor;this.pieSectionTextSize=this.pieSectionTextSize||"17px";this.pieSectionTextColor=this.pieSectionTextColor||this.textColor;this.pieLegendTextSize=this.pieLegendTextSize||"17px";this.pieLegendTextColor=this.pieLegendTextColor||this.taskTextDarkColor;this.pieStrokeColor=this.pieStrokeColor||"black";this.pieStrokeWidth=this.pieStrokeWidth||"2px";this.pieOuterStrokeWidth=this.pieOuterStrokeWidth||"2px";this.pieOuterStrokeColor=this.pieOuterStrokeColor||"black";this.pieOpacity=this.pieOpacity||"0.7";this.quadrant1Fill=this.quadrant1Fill||this.primaryColor;this.quadrant2Fill=this.quadrant2Fill||l(this.primaryColor,{r:5,g:5,b:5});this.quadrant3Fill=this.quadrant3Fill||l(this.primaryColor,{r:10,g:10,b:10});this.quadrant4Fill=this.quadrant4Fill||l(this.primaryColor,{r:15,g:15,b:15});this.quadrant1TextFill=this.quadrant1TextFill||this.primaryTextColor;this.quadrant2TextFill=this.quadrant2TextFill||l(this.primaryTextColor,{r:-5,g:-5,b:-5});this.quadrant3TextFill=this.quadrant3TextFill||l(this.primaryTextColor,{r:-10,g:-10,b:-10});this.quadrant4TextFill=this.quadrant4TextFill||l(this.primaryTextColor,{r:-15,g:-15,b:-15});this.quadrantPointFill=this.quadrantPointFill||(0,m.A)(this.quadrant1Fill)?(0,g.A)(this.quadrant1Fill):(0,p.A)(this.quadrant1Fill);this.quadrantPointTextFill=this.quadrantPointTextFill||this.primaryTextColor;this.quadrantXAxisTextFill=this.quadrantXAxisTextFill||this.primaryTextColor;this.quadrantYAxisTextFill=this.quadrantYAxisTextFill||this.primaryTextColor;this.quadrantInternalBorderStrokeFill=this.quadrantInternalBorderStrokeFill||this.primaryBorderColor;this.quadrantExternalBorderStrokeFill=this.quadrantExternalBorderStrokeFill||this.primaryBorderColor;this.quadrantTitleFill=this.quadrantTitleFill||this.primaryTextColor;this.xyChart={backgroundColor:this.xyChart?.backgroundColor||this.background,titleColor:this.xyChart?.titleColor||this.primaryTextColor,xAxisTitleColor:this.xyChart?.xAxisTitleColor||this.primaryTextColor,xAxisLabelColor:this.xyChart?.xAxisLabelColor||this.primaryTextColor,xAxisTickColor:this.xyChart?.xAxisTickColor||this.primaryTextColor,xAxisLineColor:this.xyChart?.xAxisLineColor||this.primaryTextColor,yAxisTitleColor:this.xyChart?.yAxisTitleColor||this.primaryTextColor,yAxisLabelColor:this.xyChart?.yAxisLabelColor||this.primaryTextColor,yAxisTickColor:this.xyChart?.yAxisTickColor||this.primaryTextColor,yAxisLineColor:this.xyChart?.yAxisLineColor||this.primaryTextColor,plotColorPalette:this.xyChart?.plotColorPalette||"#3498db,#2ecc71,#e74c3c,#f1c40f,#bdc3c7,#ffffff,#34495e,#9b59b6,#1abc9c,#e67e22"};this.packet={startByteColor:this.primaryTextColor,endByteColor:this.primaryTextColor,labelColor:this.primaryTextColor,titleColor:this.primaryTextColor,blockStrokeColor:this.primaryTextColor,blockFillColor:this.background};this.radar={axisColor:this.radar?.axisColor||this.lineColor,axisStrokeWidth:this.radar?.axisStrokeWidth||2,axisLabelFontSize:this.radar?.axisLabelFontSize||12,curveOpacity:this.radar?.curveOpacity||.5,curveStrokeWidth:this.radar?.curveStrokeWidth||2,graticuleColor:this.radar?.graticuleColor||"#DEDEDE",graticuleStrokeWidth:this.radar?.graticuleStrokeWidth||1,graticuleOpacity:this.radar?.graticuleOpacity||.3,legendBoxSize:this.radar?.legendBoxSize||12,legendFontSize:this.radar?.legendFontSize||12};this.classText=this.primaryTextColor;this.requirementBackground=this.requirementBackground||this.primaryColor;this.requirementBorderColor=this.requirementBorderColor||this.primaryBorderColor;this.requirementBorderSize=this.requirementBorderSize||"1";this.requirementTextColor=this.requirementTextColor||this.primaryTextColor;this.relationColor=this.relationColor||this.lineColor;this.relationLabelBackground=this.relationLabelBackground||(this.darkMode?(0,p.A)(this.secondaryColor,30):this.secondaryColor);this.relationLabelColor=this.relationLabelColor||this.actorTextColor;this.git0=(0,g.A)(this.secondaryColor,20);this.git1=(0,g.A)(this.pie2||this.secondaryColor,20);this.git2=(0,g.A)(this.pie3||this.tertiaryColor,20);this.git3=(0,g.A)(this.pie4||l(this.primaryColor,{h:-30}),20);this.git4=(0,g.A)(this.pie5||l(this.primaryColor,{h:-60}),20);this.git5=(0,g.A)(this.pie6||l(this.primaryColor,{h:-90}),10);this.git6=(0,g.A)(this.pie7||l(this.primaryColor,{h:60}),10);this.git7=(0,g.A)(this.pie8||l(this.primaryColor,{h:120}),20);this.gitInv0=this.gitInv0||f(this.git0);this.gitInv1=this.gitInv1||f(this.git1);this.gitInv2=this.gitInv2||f(this.git2);this.gitInv3=this.gitInv3||f(this.git3);this.gitInv4=this.gitInv4||f(this.git4);this.gitInv5=this.gitInv5||f(this.git5);this.gitInv6=this.gitInv6||f(this.git6);this.gitInv7=this.gitInv7||f(this.git7);this.gitBranchLabel0=this.gitBranchLabel0||f(this.labelTextColor);this.gitBranchLabel1=this.gitBranchLabel1||this.labelTextColor;this.gitBranchLabel2=this.gitBranchLabel2||this.labelTextColor;this.gitBranchLabel3=this.gitBranchLabel3||f(this.labelTextColor);this.gitBranchLabel4=this.gitBranchLabel4||this.labelTextColor;this.gitBranchLabel5=this.gitBranchLabel5||this.labelTextColor;this.gitBranchLabel6=this.gitBranchLabel6||this.labelTextColor;this.gitBranchLabel7=this.gitBranchLabel7||this.labelTextColor;this.tagLabelColor=this.tagLabelColor||this.primaryTextColor;this.tagLabelBackground=this.tagLabelBackground||this.primaryColor;this.tagLabelBorder=this.tagBorder||this.primaryBorderColor;this.tagLabelFontSize=this.tagLabelFontSize||"10px";this.commitLabelColor=this.commitLabelColor||this.secondaryTextColor;this.commitLabelBackground=this.commitLabelBackground||this.secondaryColor;this.commitLabelFontSize=this.commitLabelFontSize||"10px";this.attributeBackgroundColorOdd=this.attributeBackgroundColorOdd||(0,g.A)(this.background,12);this.attributeBackgroundColorEven=this.attributeBackgroundColorEven||(0,g.A)(this.background,2);this.nodeBorder=this.nodeBorder||"#999"}calculate(t){if(typeof t!=="object"){this.updateColors();return}const e=Object.keys(t);e.forEach((e=>{this[e]=t[e]}));this.updateColors();e.forEach((e=>{this[e]=t[e]}))}};var N=x((t=>{const e=new q;e.calculate(t);return e}),"getThemeVariables");var W=class{static{x(this,"Theme")}constructor(){this.background="#f4f4f4";this.primaryColor="#ECECFF";this.secondaryColor=l(this.primaryColor,{h:120});this.secondaryColor="#ffffde";this.tertiaryColor=l(this.primaryColor,{h:-160});this.primaryBorderColor=R(this.primaryColor,this.darkMode);this.secondaryBorderColor=R(this.secondaryColor,this.darkMode);this.tertiaryBorderColor=R(this.tertiaryColor,this.darkMode);this.primaryTextColor=f(this.primaryColor);this.secondaryTextColor=f(this.secondaryColor);this.tertiaryTextColor=f(this.tertiaryColor);this.lineColor=f(this.background);this.textColor=f(this.background);this.background="white";this.mainBkg="#ECECFF";this.secondBkg="#ffffde";this.lineColor="#333333";this.border1="#9370DB";this.border2="#aaaa33";this.arrowheadColor="#333333";this.fontFamily='"trebuchet ms", verdana, arial, sans-serif';this.fontSize="16px";this.labelBackground="rgba(232,232,232, 0.8)";this.textColor="#333";this.THEME_COLOR_LIMIT=12;this.nodeBkg="calculated";this.nodeBorder="calculated";this.clusterBkg="calculated";this.clusterBorder="calculated";this.defaultLinkColor="calculated";this.titleColor="calculated";this.edgeLabelBackground="calculated";this.actorBorder="calculated";this.actorBkg="calculated";this.actorTextColor="black";this.actorLineColor="calculated";this.signalColor="calculated";this.signalTextColor="calculated";this.labelBoxBkgColor="calculated";this.labelBoxBorderColor="calculated";this.labelTextColor="calculated";this.loopTextColor="calculated";this.noteBorderColor="calculated";this.noteBkgColor="#fff5ad";this.noteTextColor="calculated";this.activationBorderColor="#666";this.activationBkgColor="#f4f4f4";this.sequenceNumberColor="white";this.sectionBkgColor="calculated";this.altSectionBkgColor="calculated";this.sectionBkgColor2="calculated";this.excludeBkgColor="#eeeeee";this.taskBorderColor="calculated";this.taskBkgColor="calculated";this.taskTextLightColor="calculated";this.taskTextColor=this.taskTextLightColor;this.taskTextDarkColor="calculated";this.taskTextOutsideColor=this.taskTextDarkColor;this.taskTextClickableColor="calculated";this.activeTaskBorderColor="calculated";this.activeTaskBkgColor="calculated";this.gridColor="calculated";this.doneTaskBkgColor="calculated";this.doneTaskBorderColor="calculated";this.critBorderColor="calculated";this.critBkgColor="calculated";this.todayLineColor="calculated";this.sectionBkgColor=(0,c.A)(102,102,255,.49);this.altSectionBkgColor="white";this.sectionBkgColor2="#fff400";this.taskBorderColor="#534fbc";this.taskBkgColor="#8a90dd";this.taskTextLightColor="white";this.taskTextColor="calculated";this.taskTextDarkColor="black";this.taskTextOutsideColor="calculated";this.taskTextClickableColor="#003163";this.activeTaskBorderColor="#534fbc";this.activeTaskBkgColor="#bfc7ff";this.gridColor="lightgrey";this.doneTaskBkgColor="lightgrey";this.doneTaskBorderColor="grey";this.critBorderColor="#ff8888";this.critBkgColor="red";this.todayLineColor="red";this.personBorder=this.primaryBorderColor;this.personBkg=this.mainBkg;this.archEdgeColor="calculated";this.archEdgeArrowColor="calculated";this.archEdgeWidth="3";this.archGroupBorderColor=this.primaryBorderColor;this.archGroupBorderWidth="2px";this.rowOdd="calculated";this.rowEven="calculated";this.labelColor="black";this.errorBkgColor="#552222";this.errorTextColor="#552222";this.updateColors()}updateColors(){this.cScale0=this.cScale0||this.primaryColor;this.cScale1=this.cScale1||this.secondaryColor;this.cScale2=this.cScale2||this.tertiaryColor;this.cScale3=this.cScale3||l(this.primaryColor,{h:30});this.cScale4=this.cScale4||l(this.primaryColor,{h:60});this.cScale5=this.cScale5||l(this.primaryColor,{h:90});this.cScale6=this.cScale6||l(this.primaryColor,{h:120});this.cScale7=this.cScale7||l(this.primaryColor,{h:150});this.cScale8=this.cScale8||l(this.primaryColor,{h:210});this.cScale9=this.cScale9||l(this.primaryColor,{h:270});this.cScale10=this.cScale10||l(this.primaryColor,{h:300});this.cScale11=this.cScale11||l(this.primaryColor,{h:330});this["cScalePeer1"]=this["cScalePeer1"]||(0,p.A)(this.secondaryColor,45);this["cScalePeer2"]=this["cScalePeer2"]||(0,p.A)(this.tertiaryColor,40);for(let t=0;t<this.THEME_COLOR_LIMIT;t++){this["cScale"+t]=(0,p.A)(this["cScale"+t],10);this["cScalePeer"+t]=this["cScalePeer"+t]||(0,p.A)(this["cScale"+t],25)}for(let t=0;t<this.THEME_COLOR_LIMIT;t++){this["cScaleInv"+t]=this["cScaleInv"+t]||l(this["cScale"+t],{h:180})}for(let t=0;t<5;t++){this["surface"+t]=this["surface"+t]||l(this.mainBkg,{h:30,l:-(5+t*5)});this["surfacePeer"+t]=this["surfacePeer"+t]||l(this.mainBkg,{h:30,l:-(7+t*5)})}this.scaleLabelColor=this.scaleLabelColor!=="calculated"&&this.scaleLabelColor?this.scaleLabelColor:this.labelTextColor;if(this.labelTextColor!=="calculated"){this.cScaleLabel0=this.cScaleLabel0||f(this.labelTextColor);this.cScaleLabel3=this.cScaleLabel3||f(this.labelTextColor);for(let t=0;t<this.THEME_COLOR_LIMIT;t++){this["cScaleLabel"+t]=this["cScaleLabel"+t]||this.labelTextColor}}this.nodeBkg=this.mainBkg;this.nodeBorder=this.border1;this.clusterBkg=this.secondBkg;this.clusterBorder=this.border2;this.defaultLinkColor=this.lineColor;this.titleColor=this.textColor;this.edgeLabelBackground=this.labelBackground;this.actorBorder=(0,g.A)(this.border1,23);this.actorBkg=this.mainBkg;this.labelBoxBkgColor=this.actorBkg;this.signalColor=this.textColor;this.signalTextColor=this.textColor;this.labelBoxBorderColor=this.actorBorder;this.labelTextColor=this.actorTextColor;this.loopTextColor=this.actorTextColor;this.noteBorderColor=this.border2;this.noteTextColor=this.actorTextColor;this.actorLineColor=this.actorBorder;this.taskTextColor=this.taskTextLightColor;this.taskTextOutsideColor=this.taskTextDarkColor;this.archEdgeColor=this.lineColor;this.archEdgeArrowColor=this.lineColor;this.rowOdd=this.rowOdd||(0,g.A)(this.primaryColor,75)||"#ffffff";this.rowEven=this.rowEven||(0,g.A)(this.primaryColor,1);this.transitionColor=this.transitionColor||this.lineColor;this.transitionLabelColor=this.transitionLabelColor||this.textColor;this.stateLabelColor=this.stateLabelColor||this.stateBkg||this.primaryTextColor;this.stateBkg=this.stateBkg||this.mainBkg;this.labelBackgroundColor=this.labelBackgroundColor||this.stateBkg;this.compositeBackground=this.compositeBackground||this.background||this.tertiaryColor;this.altBackground=this.altBackground||"#f0f0f0";this.compositeTitleBackground=this.compositeTitleBackground||this.mainBkg;this.compositeBorder=this.compositeBorder||this.nodeBorder;this.innerEndBackground=this.nodeBorder;this.specialStateColor=this.lineColor;this.errorBkgColor=this.errorBkgColor||this.tertiaryColor;this.errorTextColor=this.errorTextColor||this.tertiaryTextColor;this.transitionColor=this.transitionColor||this.lineColor;this.classText=this.primaryTextColor;this.fillType0=this.primaryColor;this.fillType1=this.secondaryColor;this.fillType2=l(this.primaryColor,{h:64});this.fillType3=l(this.secondaryColor,{h:64});this.fillType4=l(this.primaryColor,{h:-64});this.fillType5=l(this.secondaryColor,{h:-64});this.fillType6=l(this.primaryColor,{h:128});this.fillType7=l(this.secondaryColor,{h:128});this.pie1=this.pie1||this.primaryColor;this.pie2=this.pie2||this.secondaryColor;this.pie3=this.pie3||l(this.tertiaryColor,{l:-40});this.pie4=this.pie4||l(this.primaryColor,{l:-10});this.pie5=this.pie5||l(this.secondaryColor,{l:-30});this.pie6=this.pie6||l(this.tertiaryColor,{l:-20});this.pie7=this.pie7||l(this.primaryColor,{h:60,l:-20});this.pie8=this.pie8||l(this.primaryColor,{h:-60,l:-40});this.pie9=this.pie9||l(this.primaryColor,{h:120,l:-40});this.pie10=this.pie10||l(this.primaryColor,{h:60,l:-40});this.pie11=this.pie11||l(this.primaryColor,{h:-90,l:-40});this.pie12=this.pie12||l(this.primaryColor,{h:120,l:-30});this.pieTitleTextSize=this.pieTitleTextSize||"25px";this.pieTitleTextColor=this.pieTitleTextColor||this.taskTextDarkColor;this.pieSectionTextSize=this.pieSectionTextSize||"17px";this.pieSectionTextColor=this.pieSectionTextColor||this.textColor;this.pieLegendTextSize=this.pieLegendTextSize||"17px";this.pieLegendTextColor=this.pieLegendTextColor||this.taskTextDarkColor;this.pieStrokeColor=this.pieStrokeColor||"black";this.pieStrokeWidth=this.pieStrokeWidth||"2px";this.pieOuterStrokeWidth=this.pieOuterStrokeWidth||"2px";this.pieOuterStrokeColor=this.pieOuterStrokeColor||"black";this.pieOpacity=this.pieOpacity||"0.7";this.quadrant1Fill=this.quadrant1Fill||this.primaryColor;this.quadrant2Fill=this.quadrant2Fill||l(this.primaryColor,{r:5,g:5,b:5});this.quadrant3Fill=this.quadrant3Fill||l(this.primaryColor,{r:10,g:10,b:10});this.quadrant4Fill=this.quadrant4Fill||l(this.primaryColor,{r:15,g:15,b:15});this.quadrant1TextFill=this.quadrant1TextFill||this.primaryTextColor;this.quadrant2TextFill=this.quadrant2TextFill||l(this.primaryTextColor,{r:-5,g:-5,b:-5});this.quadrant3TextFill=this.quadrant3TextFill||l(this.primaryTextColor,{r:-10,g:-10,b:-10});this.quadrant4TextFill=this.quadrant4TextFill||l(this.primaryTextColor,{r:-15,g:-15,b:-15});this.quadrantPointFill=this.quadrantPointFill||(0,m.A)(this.quadrant1Fill)?(0,g.A)(this.quadrant1Fill):(0,p.A)(this.quadrant1Fill);this.quadrantPointTextFill=this.quadrantPointTextFill||this.primaryTextColor;this.quadrantXAxisTextFill=this.quadrantXAxisTextFill||this.primaryTextColor;this.quadrantYAxisTextFill=this.quadrantYAxisTextFill||this.primaryTextColor;this.quadrantInternalBorderStrokeFill=this.quadrantInternalBorderStrokeFill||this.primaryBorderColor;this.quadrantExternalBorderStrokeFill=this.quadrantExternalBorderStrokeFill||this.primaryBorderColor;this.quadrantTitleFill=this.quadrantTitleFill||this.primaryTextColor;this.radar={axisColor:this.radar?.axisColor||this.lineColor,axisStrokeWidth:this.radar?.axisStrokeWidth||2,axisLabelFontSize:this.radar?.axisLabelFontSize||12,curveOpacity:this.radar?.curveOpacity||.5,curveStrokeWidth:this.radar?.curveStrokeWidth||2,graticuleColor:this.radar?.graticuleColor||"#DEDEDE",graticuleStrokeWidth:this.radar?.graticuleStrokeWidth||1,graticuleOpacity:this.radar?.graticuleOpacity||.3,legendBoxSize:this.radar?.legendBoxSize||12,legendFontSize:this.radar?.legendFontSize||12};this.xyChart={backgroundColor:this.xyChart?.backgroundColor||this.background,titleColor:this.xyChart?.titleColor||this.primaryTextColor,xAxisTitleColor:this.xyChart?.xAxisTitleColor||this.primaryTextColor,xAxisLabelColor:this.xyChart?.xAxisLabelColor||this.primaryTextColor,xAxisTickColor:this.xyChart?.xAxisTickColor||this.primaryTextColor,xAxisLineColor:this.xyChart?.xAxisLineColor||this.primaryTextColor,yAxisTitleColor:this.xyChart?.yAxisTitleColor||this.primaryTextColor,yAxisLabelColor:this.xyChart?.yAxisLabelColor||this.primaryTextColor,yAxisTickColor:this.xyChart?.yAxisTickColor||this.primaryTextColor,yAxisLineColor:this.xyChart?.yAxisLineColor||this.primaryTextColor,plotColorPalette:this.xyChart?.plotColorPalette||"#ECECFF,#8493A6,#FFC3A0,#DCDDE1,#B8E994,#D1A36F,#C3CDE6,#FFB6C1,#496078,#F8F3E3"};this.requirementBackground=this.requirementBackground||this.primaryColor;this.requirementBorderColor=this.requirementBorderColor||this.primaryBorderColor;this.requirementBorderSize=this.requirementBorderSize||"1";this.requirementTextColor=this.requirementTextColor||this.primaryTextColor;this.relationColor=this.relationColor||this.lineColor;this.relationLabelBackground=this.relationLabelBackground||this.labelBackground;this.relationLabelColor=this.relationLabelColor||this.actorTextColor;this.git0=this.git0||this.primaryColor;this.git1=this.git1||this.secondaryColor;this.git2=this.git2||this.tertiaryColor;this.git3=this.git3||l(this.primaryColor,{h:-30});this.git4=this.git4||l(this.primaryColor,{h:-60});this.git5=this.git5||l(this.primaryColor,{h:-90});this.git6=this.git6||l(this.primaryColor,{h:60});this.git7=this.git7||l(this.primaryColor,{h:120});if(this.darkMode){this.git0=(0,g.A)(this.git0,25);this.git1=(0,g.A)(this.git1,25);this.git2=(0,g.A)(this.git2,25);this.git3=(0,g.A)(this.git3,25);this.git4=(0,g.A)(this.git4,25);this.git5=(0,g.A)(this.git5,25);this.git6=(0,g.A)(this.git6,25);this.git7=(0,g.A)(this.git7,25)}else{this.git0=(0,p.A)(this.git0,25);this.git1=(0,p.A)(this.git1,25);this.git2=(0,p.A)(this.git2,25);this.git3=(0,p.A)(this.git3,25);this.git4=(0,p.A)(this.git4,25);this.git5=(0,p.A)(this.git5,25);this.git6=(0,p.A)(this.git6,25);this.git7=(0,p.A)(this.git7,25)}this.gitInv0=this.gitInv0||(0,p.A)(f(this.git0),25);this.gitInv1=this.gitInv1||f(this.git1);this.gitInv2=this.gitInv2||f(this.git2);this.gitInv3=this.gitInv3||f(this.git3);this.gitInv4=this.gitInv4||f(this.git4);this.gitInv5=this.gitInv5||f(this.git5);this.gitInv6=this.gitInv6||f(this.git6);this.gitInv7=this.gitInv7||f(this.git7);this.gitBranchLabel0=this.gitBranchLabel0||f(this.labelTextColor);this.gitBranchLabel1=this.gitBranchLabel1||this.labelTextColor;this.gitBranchLabel2=this.gitBranchLabel2||this.labelTextColor;this.gitBranchLabel3=this.gitBranchLabel3||f(this.labelTextColor);this.gitBranchLabel4=this.gitBranchLabel4||this.labelTextColor;this.gitBranchLabel5=this.gitBranchLabel5||this.labelTextColor;this.gitBranchLabel6=this.gitBranchLabel6||this.labelTextColor;this.gitBranchLabel7=this.gitBranchLabel7||this.labelTextColor;this.tagLabelColor=this.tagLabelColor||this.primaryTextColor;this.tagLabelBackground=this.tagLabelBackground||this.primaryColor;this.tagLabelBorder=this.tagBorder||this.primaryBorderColor;this.tagLabelFontSize=this.tagLabelFontSize||"10px";this.commitLabelColor=this.commitLabelColor||this.secondaryTextColor;this.commitLabelBackground=this.commitLabelBackground||this.secondaryColor;this.commitLabelFontSize=this.commitLabelFontSize||"10px";this.attributeBackgroundColorOdd=this.attributeBackgroundColorOdd||I;this.attributeBackgroundColorEven=this.attributeBackgroundColorEven||K}calculate(t){Object.keys(this).forEach((t=>{if(this[t]==="calculated"){this[t]=void 0}}));if(typeof t!=="object"){this.updateColors();return}const e=Object.keys(t);e.forEach((e=>{this[e]=t[e]}));this.updateColors();e.forEach((e=>{this[e]=t[e]}))}};var j=x((t=>{const e=new W;e.calculate(t);return e}),"getThemeVariables");var H=class{static{x(this,"Theme")}constructor(){this.background="#f4f4f4";this.primaryColor="#cde498";this.secondaryColor="#cdffb2";this.background="white";this.mainBkg="#cde498";this.secondBkg="#cdffb2";this.lineColor="green";this.border1="#13540c";this.border2="#6eaa49";this.arrowheadColor="green";this.fontFamily='"trebuchet ms", verdana, arial, sans-serif';this.fontSize="16px";this.tertiaryColor=(0,g.A)("#cde498",10);this.primaryBorderColor=R(this.primaryColor,this.darkMode);this.secondaryBorderColor=R(this.secondaryColor,this.darkMode);this.tertiaryBorderColor=R(this.tertiaryColor,this.darkMode);this.primaryTextColor=f(this.primaryColor);this.secondaryTextColor=f(this.secondaryColor);this.tertiaryTextColor=f(this.primaryColor);this.lineColor=f(this.background);this.textColor=f(this.background);this.THEME_COLOR_LIMIT=12;this.nodeBkg="calculated";this.nodeBorder="calculated";this.clusterBkg="calculated";this.clusterBorder="calculated";this.defaultLinkColor="calculated";this.titleColor="#333";this.edgeLabelBackground="#e8e8e8";this.actorBorder="calculated";this.actorBkg="calculated";this.actorTextColor="black";this.actorLineColor="calculated";this.signalColor="#333";this.signalTextColor="#333";this.labelBoxBkgColor="calculated";this.labelBoxBorderColor="#326932";this.labelTextColor="calculated";this.loopTextColor="calculated";this.noteBorderColor="calculated";this.noteBkgColor="#fff5ad";this.noteTextColor="calculated";this.activationBorderColor="#666";this.activationBkgColor="#f4f4f4";this.sequenceNumberColor="white";this.sectionBkgColor="#6eaa49";this.altSectionBkgColor="white";this.sectionBkgColor2="#6eaa49";this.excludeBkgColor="#eeeeee";this.taskBorderColor="calculated";this.taskBkgColor="#487e3a";this.taskTextLightColor="white";this.taskTextColor="calculated";this.taskTextDarkColor="black";this.taskTextOutsideColor="calculated";this.taskTextClickableColor="#003163";this.activeTaskBorderColor="calculated";this.activeTaskBkgColor="calculated";this.gridColor="lightgrey";this.doneTaskBkgColor="lightgrey";this.doneTaskBorderColor="grey";this.critBorderColor="#ff8888";this.critBkgColor="red";this.todayLineColor="red";this.personBorder=this.primaryBorderColor;this.personBkg=this.mainBkg;this.archEdgeColor="calculated";this.archEdgeArrowColor="calculated";this.archEdgeWidth="3";this.archGroupBorderColor=this.primaryBorderColor;this.archGroupBorderWidth="2px";this.labelColor="black";this.errorBkgColor="#552222";this.errorTextColor="#552222"}updateColors(){this.actorBorder=(0,p.A)(this.mainBkg,20);this.actorBkg=this.mainBkg;this.labelBoxBkgColor=this.actorBkg;this.labelTextColor=this.actorTextColor;this.loopTextColor=this.actorTextColor;this.noteBorderColor=this.border2;this.noteTextColor=this.actorTextColor;this.actorLineColor=this.actorBorder;this.cScale0=this.cScale0||this.primaryColor;this.cScale1=this.cScale1||this.secondaryColor;this.cScale2=this.cScale2||this.tertiaryColor;this.cScale3=this.cScale3||l(this.primaryColor,{h:30});this.cScale4=this.cScale4||l(this.primaryColor,{h:60});this.cScale5=this.cScale5||l(this.primaryColor,{h:90});this.cScale6=this.cScale6||l(this.primaryColor,{h:120});this.cScale7=this.cScale7||l(this.primaryColor,{h:150});this.cScale8=this.cScale8||l(this.primaryColor,{h:210});this.cScale9=this.cScale9||l(this.primaryColor,{h:270});this.cScale10=this.cScale10||l(this.primaryColor,{h:300});this.cScale11=this.cScale11||l(this.primaryColor,{h:330});this["cScalePeer1"]=this["cScalePeer1"]||(0,p.A)(this.secondaryColor,45);this["cScalePeer2"]=this["cScalePeer2"]||(0,p.A)(this.tertiaryColor,40);for(let t=0;t<this.THEME_COLOR_LIMIT;t++){this["cScale"+t]=(0,p.A)(this["cScale"+t],10);this["cScalePeer"+t]=this["cScalePeer"+t]||(0,p.A)(this["cScale"+t],25)}for(let t=0;t<this.THEME_COLOR_LIMIT;t++){this["cScaleInv"+t]=this["cScaleInv"+t]||l(this["cScale"+t],{h:180})}this.scaleLabelColor=this.scaleLabelColor!=="calculated"&&this.scaleLabelColor?this.scaleLabelColor:this.labelTextColor;for(let t=0;t<this.THEME_COLOR_LIMIT;t++){this["cScaleLabel"+t]=this["cScaleLabel"+t]||this.scaleLabelColor}for(let t=0;t<5;t++){this["surface"+t]=this["surface"+t]||l(this.mainBkg,{h:30,s:-30,l:-(5+t*5)});this["surfacePeer"+t]=this["surfacePeer"+t]||l(this.mainBkg,{h:30,s:-30,l:-(8+t*5)})}this.nodeBkg=this.mainBkg;this.nodeBorder=this.border1;this.clusterBkg=this.secondBkg;this.clusterBorder=this.border2;this.defaultLinkColor=this.lineColor;this.taskBorderColor=this.border1;this.taskTextColor=this.taskTextLightColor;this.taskTextOutsideColor=this.taskTextDarkColor;this.activeTaskBorderColor=this.taskBorderColor;this.activeTaskBkgColor=this.mainBkg;this.archEdgeColor=this.lineColor;this.archEdgeArrowColor=this.lineColor;this.rowOdd=this.rowOdd||(0,g.A)(this.mainBkg,75)||"#ffffff";this.rowEven=this.rowEven||(0,g.A)(this.mainBkg,20);this.transitionColor=this.transitionColor||this.lineColor;this.transitionLabelColor=this.transitionLabelColor||this.textColor;this.stateLabelColor=this.stateLabelColor||this.stateBkg||this.primaryTextColor;this.stateBkg=this.stateBkg||this.mainBkg;this.labelBackgroundColor=this.labelBackgroundColor||this.stateBkg;this.compositeBackground=this.compositeBackground||this.background||this.tertiaryColor;this.altBackground=this.altBackground||"#f0f0f0";this.compositeTitleBackground=this.compositeTitleBackground||this.mainBkg;this.compositeBorder=this.compositeBorder||this.nodeBorder;this.innerEndBackground=this.primaryBorderColor;this.specialStateColor=this.lineColor;this.errorBkgColor=this.errorBkgColor||this.tertiaryColor;this.errorTextColor=this.errorTextColor||this.tertiaryTextColor;this.transitionColor=this.transitionColor||this.lineColor;this.classText=this.primaryTextColor;this.fillType0=this.primaryColor;this.fillType1=this.secondaryColor;this.fillType2=l(this.primaryColor,{h:64});this.fillType3=l(this.secondaryColor,{h:64});this.fillType4=l(this.primaryColor,{h:-64});this.fillType5=l(this.secondaryColor,{h:-64});this.fillType6=l(this.primaryColor,{h:128});this.fillType7=l(this.secondaryColor,{h:128});this.pie1=this.pie1||this.primaryColor;this.pie2=this.pie2||this.secondaryColor;this.pie3=this.pie3||this.tertiaryColor;this.pie4=this.pie4||l(this.primaryColor,{l:-30});this.pie5=this.pie5||l(this.secondaryColor,{l:-30});this.pie6=this.pie6||l(this.tertiaryColor,{h:40,l:-40});this.pie7=this.pie7||l(this.primaryColor,{h:60,l:-10});this.pie8=this.pie8||l(this.primaryColor,{h:-60,l:-10});this.pie9=this.pie9||l(this.primaryColor,{h:120,l:0});this.pie10=this.pie10||l(this.primaryColor,{h:60,l:-50});this.pie11=this.pie11||l(this.primaryColor,{h:-60,l:-50});this.pie12=this.pie12||l(this.primaryColor,{h:120,l:-50});this.pieTitleTextSize=this.pieTitleTextSize||"25px";this.pieTitleTextColor=this.pieTitleTextColor||this.taskTextDarkColor;this.pieSectionTextSize=this.pieSectionTextSize||"17px";this.pieSectionTextColor=this.pieSectionTextColor||this.textColor;this.pieLegendTextSize=this.pieLegendTextSize||"17px";this.pieLegendTextColor=this.pieLegendTextColor||this.taskTextDarkColor;this.pieStrokeColor=this.pieStrokeColor||"black";this.pieStrokeWidth=this.pieStrokeWidth||"2px";this.pieOuterStrokeWidth=this.pieOuterStrokeWidth||"2px";this.pieOuterStrokeColor=this.pieOuterStrokeColor||"black";this.pieOpacity=this.pieOpacity||"0.7";this.quadrant1Fill=this.quadrant1Fill||this.primaryColor;this.quadrant2Fill=this.quadrant2Fill||l(this.primaryColor,{r:5,g:5,b:5});this.quadrant3Fill=this.quadrant3Fill||l(this.primaryColor,{r:10,g:10,b:10});this.quadrant4Fill=this.quadrant4Fill||l(this.primaryColor,{r:15,g:15,b:15});this.quadrant1TextFill=this.quadrant1TextFill||this.primaryTextColor;this.quadrant2TextFill=this.quadrant2TextFill||l(this.primaryTextColor,{r:-5,g:-5,b:-5});this.quadrant3TextFill=this.quadrant3TextFill||l(this.primaryTextColor,{r:-10,g:-10,b:-10});this.quadrant4TextFill=this.quadrant4TextFill||l(this.primaryTextColor,{r:-15,g:-15,b:-15});this.quadrantPointFill=this.quadrantPointFill||(0,m.A)(this.quadrant1Fill)?(0,g.A)(this.quadrant1Fill):(0,p.A)(this.quadrant1Fill);this.quadrantPointTextFill=this.quadrantPointTextFill||this.primaryTextColor;this.quadrantXAxisTextFill=this.quadrantXAxisTextFill||this.primaryTextColor;this.quadrantYAxisTextFill=this.quadrantYAxisTextFill||this.primaryTextColor;this.quadrantInternalBorderStrokeFill=this.quadrantInternalBorderStrokeFill||this.primaryBorderColor;this.quadrantExternalBorderStrokeFill=this.quadrantExternalBorderStrokeFill||this.primaryBorderColor;this.quadrantTitleFill=this.quadrantTitleFill||this.primaryTextColor;this.packet={startByteColor:this.primaryTextColor,endByteColor:this.primaryTextColor,labelColor:this.primaryTextColor,titleColor:this.primaryTextColor,blockStrokeColor:this.primaryTextColor,blockFillColor:this.mainBkg};this.radar={axisColor:this.radar?.axisColor||this.lineColor,axisStrokeWidth:this.radar?.axisStrokeWidth||2,axisLabelFontSize:this.radar?.axisLabelFontSize||12,curveOpacity:this.radar?.curveOpacity||.5,curveStrokeWidth:this.radar?.curveStrokeWidth||2,graticuleColor:this.radar?.graticuleColor||"#DEDEDE",graticuleStrokeWidth:this.radar?.graticuleStrokeWidth||1,graticuleOpacity:this.radar?.graticuleOpacity||.3,legendBoxSize:this.radar?.legendBoxSize||12,legendFontSize:this.radar?.legendFontSize||12};this.xyChart={backgroundColor:this.xyChart?.backgroundColor||this.background,titleColor:this.xyChart?.titleColor||this.primaryTextColor,xAxisTitleColor:this.xyChart?.xAxisTitleColor||this.primaryTextColor,xAxisLabelColor:this.xyChart?.xAxisLabelColor||this.primaryTextColor,xAxisTickColor:this.xyChart?.xAxisTickColor||this.primaryTextColor,xAxisLineColor:this.xyChart?.xAxisLineColor||this.primaryTextColor,yAxisTitleColor:this.xyChart?.yAxisTitleColor||this.primaryTextColor,yAxisLabelColor:this.xyChart?.yAxisLabelColor||this.primaryTextColor,yAxisTickColor:this.xyChart?.yAxisTickColor||this.primaryTextColor,yAxisLineColor:this.xyChart?.yAxisLineColor||this.primaryTextColor,plotColorPalette:this.xyChart?.plotColorPalette||"#CDE498,#FF6B6B,#A0D2DB,#D7BDE2,#F0F0F0,#FFC3A0,#7FD8BE,#FF9A8B,#FAF3E0,#FFF176"};this.requirementBackground=this.requirementBackground||this.primaryColor;this.requirementBorderColor=this.requirementBorderColor||this.primaryBorderColor;this.requirementBorderSize=this.requirementBorderSize||"1";this.requirementTextColor=this.requirementTextColor||this.primaryTextColor;this.relationColor=this.relationColor||this.lineColor;this.relationLabelBackground=this.relationLabelBackground||this.edgeLabelBackground;this.relationLabelColor=this.relationLabelColor||this.actorTextColor;this.git0=this.git0||this.primaryColor;this.git1=this.git1||this.secondaryColor;this.git2=this.git2||this.tertiaryColor;this.git3=this.git3||l(this.primaryColor,{h:-30});this.git4=this.git4||l(this.primaryColor,{h:-60});this.git5=this.git5||l(this.primaryColor,{h:-90});this.git6=this.git6||l(this.primaryColor,{h:60});this.git7=this.git7||l(this.primaryColor,{h:120});if(this.darkMode){this.git0=(0,g.A)(this.git0,25);this.git1=(0,g.A)(this.git1,25);this.git2=(0,g.A)(this.git2,25);this.git3=(0,g.A)(this.git3,25);this.git4=(0,g.A)(this.git4,25);this.git5=(0,g.A)(this.git5,25);this.git6=(0,g.A)(this.git6,25);this.git7=(0,g.A)(this.git7,25)}else{this.git0=(0,p.A)(this.git0,25);this.git1=(0,p.A)(this.git1,25);this.git2=(0,p.A)(this.git2,25);this.git3=(0,p.A)(this.git3,25);this.git4=(0,p.A)(this.git4,25);this.git5=(0,p.A)(this.git5,25);this.git6=(0,p.A)(this.git6,25);this.git7=(0,p.A)(this.git7,25)}this.gitInv0=this.gitInv0||f(this.git0);this.gitInv1=this.gitInv1||f(this.git1);this.gitInv2=this.gitInv2||f(this.git2);this.gitInv3=this.gitInv3||f(this.git3);this.gitInv4=this.gitInv4||f(this.git4);this.gitInv5=this.gitInv5||f(this.git5);this.gitInv6=this.gitInv6||f(this.git6);this.gitInv7=this.gitInv7||f(this.git7);this.gitBranchLabel0=this.gitBranchLabel0||f(this.labelTextColor);this.gitBranchLabel1=this.gitBranchLabel1||this.labelTextColor;this.gitBranchLabel2=this.gitBranchLabel2||this.labelTextColor;this.gitBranchLabel3=this.gitBranchLabel3||f(this.labelTextColor);this.gitBranchLabel4=this.gitBranchLabel4||this.labelTextColor;this.gitBranchLabel5=this.gitBranchLabel5||this.labelTextColor;this.gitBranchLabel6=this.gitBranchLabel6||this.labelTextColor;this.gitBranchLabel7=this.gitBranchLabel7||this.labelTextColor;this.tagLabelColor=this.tagLabelColor||this.primaryTextColor;this.tagLabelBackground=this.tagLabelBackground||this.primaryColor;this.tagLabelBorder=this.tagBorder||this.primaryBorderColor;this.tagLabelFontSize=this.tagLabelFontSize||"10px";this.commitLabelColor=this.commitLabelColor||this.secondaryTextColor;this.commitLabelBackground=this.commitLabelBackground||this.secondaryColor;this.commitLabelFontSize=this.commitLabelFontSize||"10px";this.attributeBackgroundColorOdd=this.attributeBackgroundColorOdd||I;this.attributeBackgroundColorEven=this.attributeBackgroundColorEven||K}calculate(t){if(typeof t!=="object"){this.updateColors();return}const e=Object.keys(t);e.forEach((e=>{this[e]=t[e]}));this.updateColors();e.forEach((e=>{this[e]=t[e]}))}};var Y=x((t=>{const e=new H;e.calculate(t);return e}),"getThemeVariables");var U=class{static{x(this,"Theme")}constructor(){this.primaryColor="#eee";this.contrast="#707070";this.secondaryColor=(0,g.A)(this.contrast,55);this.background="#ffffff";this.tertiaryColor=l(this.primaryColor,{h:-160});this.primaryBorderColor=R(this.primaryColor,this.darkMode);this.secondaryBorderColor=R(this.secondaryColor,this.darkMode);this.tertiaryBorderColor=R(this.tertiaryColor,this.darkMode);this.primaryTextColor=f(this.primaryColor);this.secondaryTextColor=f(this.secondaryColor);this.tertiaryTextColor=f(this.tertiaryColor);this.lineColor=f(this.background);this.textColor=f(this.background);this.mainBkg="#eee";this.secondBkg="calculated";this.lineColor="#666";this.border1="#999";this.border2="calculated";this.note="#ffa";this.text="#333";this.critical="#d42";this.done="#bbb";this.arrowheadColor="#333333";this.fontFamily='"trebuchet ms", verdana, arial, sans-serif';this.fontSize="16px";this.THEME_COLOR_LIMIT=12;this.nodeBkg="calculated";this.nodeBorder="calculated";this.clusterBkg="calculated";this.clusterBorder="calculated";this.defaultLinkColor="calculated";this.titleColor="calculated";this.edgeLabelBackground="white";this.actorBorder="calculated";this.actorBkg="calculated";this.actorTextColor="calculated";this.actorLineColor=this.actorBorder;this.signalColor="calculated";this.signalTextColor="calculated";this.labelBoxBkgColor="calculated";this.labelBoxBorderColor="calculated";this.labelTextColor="calculated";this.loopTextColor="calculated";this.noteBorderColor="calculated";this.noteBkgColor="calculated";this.noteTextColor="calculated";this.activationBorderColor="#666";this.activationBkgColor="#f4f4f4";this.sequenceNumberColor="white";this.sectionBkgColor="calculated";this.altSectionBkgColor="white";this.sectionBkgColor2="calculated";this.excludeBkgColor="#eeeeee";this.taskBorderColor="calculated";this.taskBkgColor="calculated";this.taskTextLightColor="white";this.taskTextColor="calculated";this.taskTextDarkColor="calculated";this.taskTextOutsideColor="calculated";this.taskTextClickableColor="#003163";this.activeTaskBorderColor="calculated";this.activeTaskBkgColor="calculated";this.gridColor="calculated";this.doneTaskBkgColor="calculated";this.doneTaskBorderColor="calculated";this.critBkgColor="calculated";this.critBorderColor="calculated";this.todayLineColor="calculated";this.personBorder=this.primaryBorderColor;this.personBkg=this.mainBkg;this.archEdgeColor="calculated";this.archEdgeArrowColor="calculated";this.archEdgeWidth="3";this.archGroupBorderColor=this.primaryBorderColor;this.archGroupBorderWidth="2px";this.rowOdd=this.rowOdd||(0,g.A)(this.mainBkg,75)||"#ffffff";this.rowEven=this.rowEven||"#f4f4f4";this.labelColor="black";this.errorBkgColor="#552222";this.errorTextColor="#552222"}updateColors(){this.secondBkg=(0,g.A)(this.contrast,55);this.border2=this.contrast;this.actorBorder=(0,g.A)(this.border1,23);this.actorBkg=this.mainBkg;this.actorTextColor=this.text;this.actorLineColor=this.actorBorder;this.signalColor=this.text;this.signalTextColor=this.text;this.labelBoxBkgColor=this.actorBkg;this.labelBoxBorderColor=this.actorBorder;this.labelTextColor=this.text;this.loopTextColor=this.text;this.noteBorderColor="#999";this.noteBkgColor="#666";this.noteTextColor="#fff";this.cScale0=this.cScale0||"#555";this.cScale1=this.cScale1||"#F4F4F4";this.cScale2=this.cScale2||"#555";this.cScale3=this.cScale3||"#BBB";this.cScale4=this.cScale4||"#777";this.cScale5=this.cScale5||"#999";this.cScale6=this.cScale6||"#DDD";this.cScale7=this.cScale7||"#FFF";this.cScale8=this.cScale8||"#DDD";this.cScale9=this.cScale9||"#BBB";this.cScale10=this.cScale10||"#999";this.cScale11=this.cScale11||"#777";for(let t=0;t<this.THEME_COLOR_LIMIT;t++){this["cScaleInv"+t]=this["cScaleInv"+t]||f(this["cScale"+t])}for(let t=0;t<this.THEME_COLOR_LIMIT;t++){if(this.darkMode){this["cScalePeer"+t]=this["cScalePeer"+t]||(0,g.A)(this["cScale"+t],10)}else{this["cScalePeer"+t]=this["cScalePeer"+t]||(0,p.A)(this["cScale"+t],10)}}this.scaleLabelColor=this.scaleLabelColor||(this.darkMode?"black":this.labelTextColor);this.cScaleLabel0=this.cScaleLabel0||this.cScale1;this.cScaleLabel2=this.cScaleLabel2||this.cScale1;for(let t=0;t<this.THEME_COLOR_LIMIT;t++){this["cScaleLabel"+t]=this["cScaleLabel"+t]||this.scaleLabelColor}for(let t=0;t<5;t++){this["surface"+t]=this["surface"+t]||l(this.mainBkg,{l:-(5+t*5)});this["surfacePeer"+t]=this["surfacePeer"+t]||l(this.mainBkg,{l:-(8+t*5)})}this.nodeBkg=this.mainBkg;this.nodeBorder=this.border1;this.clusterBkg=this.secondBkg;this.clusterBorder=this.border2;this.defaultLinkColor=this.lineColor;this.titleColor=this.text;this.sectionBkgColor=(0,g.A)(this.contrast,30);this.sectionBkgColor2=(0,g.A)(this.contrast,30);this.taskBorderColor=(0,p.A)(this.contrast,10);this.taskBkgColor=this.contrast;this.taskTextColor=this.taskTextLightColor;this.taskTextDarkColor=this.text;this.taskTextOutsideColor=this.taskTextDarkColor;this.activeTaskBorderColor=this.taskBorderColor;this.activeTaskBkgColor=this.mainBkg;this.gridColor=(0,g.A)(this.border1,30);this.doneTaskBkgColor=this.done;this.doneTaskBorderColor=this.lineColor;this.critBkgColor=this.critical;this.critBorderColor=(0,p.A)(this.critBkgColor,10);this.todayLineColor=this.critBkgColor;this.archEdgeColor=this.lineColor;this.archEdgeArrowColor=this.lineColor;this.transitionColor=this.transitionColor||"#000";this.transitionLabelColor=this.transitionLabelColor||this.textColor;this.stateLabelColor=this.stateLabelColor||this.stateBkg||this.primaryTextColor;this.stateBkg=this.stateBkg||this.mainBkg;this.labelBackgroundColor=this.labelBackgroundColor||this.stateBkg;this.compositeBackground=this.compositeBackground||this.background||this.tertiaryColor;this.altBackground=this.altBackground||"#f4f4f4";this.compositeTitleBackground=this.compositeTitleBackground||this.mainBkg;this.stateBorder=this.stateBorder||"#000";this.innerEndBackground=this.primaryBorderColor;this.specialStateColor="#222";this.errorBkgColor=this.errorBkgColor||this.tertiaryColor;this.errorTextColor=this.errorTextColor||this.tertiaryTextColor;this.classText=this.primaryTextColor;this.fillType0=this.primaryColor;this.fillType1=this.secondaryColor;this.fillType2=l(this.primaryColor,{h:64});this.fillType3=l(this.secondaryColor,{h:64});this.fillType4=l(this.primaryColor,{h:-64});this.fillType5=l(this.secondaryColor,{h:-64});this.fillType6=l(this.primaryColor,{h:128});this.fillType7=l(this.secondaryColor,{h:128});for(let t=0;t<this.THEME_COLOR_LIMIT;t++){this["pie"+t]=this["cScale"+t]}this.pie12=this.pie0;this.pieTitleTextSize=this.pieTitleTextSize||"25px";this.pieTitleTextColor=this.pieTitleTextColor||this.taskTextDarkColor;this.pieSectionTextSize=this.pieSectionTextSize||"17px";this.pieSectionTextColor=this.pieSectionTextColor||this.textColor;this.pieLegendTextSize=this.pieLegendTextSize||"17px";this.pieLegendTextColor=this.pieLegendTextColor||this.taskTextDarkColor;this.pieStrokeColor=this.pieStrokeColor||"black";this.pieStrokeWidth=this.pieStrokeWidth||"2px";this.pieOuterStrokeWidth=this.pieOuterStrokeWidth||"2px";this.pieOuterStrokeColor=this.pieOuterStrokeColor||"black";this.pieOpacity=this.pieOpacity||"0.7";this.quadrant1Fill=this.quadrant1Fill||this.primaryColor;this.quadrant2Fill=this.quadrant2Fill||l(this.primaryColor,{r:5,g:5,b:5});this.quadrant3Fill=this.quadrant3Fill||l(this.primaryColor,{r:10,g:10,b:10});this.quadrant4Fill=this.quadrant4Fill||l(this.primaryColor,{r:15,g:15,b:15});this.quadrant1TextFill=this.quadrant1TextFill||this.primaryTextColor;this.quadrant2TextFill=this.quadrant2TextFill||l(this.primaryTextColor,{r:-5,g:-5,b:-5});this.quadrant3TextFill=this.quadrant3TextFill||l(this.primaryTextColor,{r:-10,g:-10,b:-10});this.quadrant4TextFill=this.quadrant4TextFill||l(this.primaryTextColor,{r:-15,g:-15,b:-15});this.quadrantPointFill=this.quadrantPointFill||(0,m.A)(this.quadrant1Fill)?(0,g.A)(this.quadrant1Fill):(0,p.A)(this.quadrant1Fill);this.quadrantPointTextFill=this.quadrantPointTextFill||this.primaryTextColor;this.quadrantXAxisTextFill=this.quadrantXAxisTextFill||this.primaryTextColor;this.quadrantYAxisTextFill=this.quadrantYAxisTextFill||this.primaryTextColor;this.quadrantInternalBorderStrokeFill=this.quadrantInternalBorderStrokeFill||this.primaryBorderColor;this.quadrantExternalBorderStrokeFill=this.quadrantExternalBorderStrokeFill||this.primaryBorderColor;this.quadrantTitleFill=this.quadrantTitleFill||this.primaryTextColor;this.xyChart={backgroundColor:this.xyChart?.backgroundColor||this.background,titleColor:this.xyChart?.titleColor||this.primaryTextColor,xAxisTitleColor:this.xyChart?.xAxisTitleColor||this.primaryTextColor,xAxisLabelColor:this.xyChart?.xAxisLabelColor||this.primaryTextColor,xAxisTickColor:this.xyChart?.xAxisTickColor||this.primaryTextColor,xAxisLineColor:this.xyChart?.xAxisLineColor||this.primaryTextColor,yAxisTitleColor:this.xyChart?.yAxisTitleColor||this.primaryTextColor,yAxisLabelColor:this.xyChart?.yAxisLabelColor||this.primaryTextColor,yAxisTickColor:this.xyChart?.yAxisTickColor||this.primaryTextColor,yAxisLineColor:this.xyChart?.yAxisLineColor||this.primaryTextColor,plotColorPalette:this.xyChart?.plotColorPalette||"#EEE,#6BB8E4,#8ACB88,#C7ACD6,#E8DCC2,#FFB2A8,#FFF380,#7E8D91,#FFD8B1,#FAF3E0"};this.radar={axisColor:this.radar?.axisColor||this.lineColor,axisStrokeWidth:this.radar?.axisStrokeWidth||2,axisLabelFontSize:this.radar?.axisLabelFontSize||12,curveOpacity:this.radar?.curveOpacity||.5,curveStrokeWidth:this.radar?.curveStrokeWidth||2,graticuleColor:this.radar?.graticuleColor||"#DEDEDE",graticuleStrokeWidth:this.radar?.graticuleStrokeWidth||1,graticuleOpacity:this.radar?.graticuleOpacity||.3,legendBoxSize:this.radar?.legendBoxSize||12,legendFontSize:this.radar?.legendFontSize||12};this.requirementBackground=this.requirementBackground||this.primaryColor;this.requirementBorderColor=this.requirementBorderColor||this.primaryBorderColor;this.requirementBorderSize=this.requirementBorderSize||"1";this.requirementTextColor=this.requirementTextColor||this.primaryTextColor;this.relationColor=this.relationColor||this.lineColor;this.relationLabelBackground=this.relationLabelBackground||this.edgeLabelBackground;this.relationLabelColor=this.relationLabelColor||this.actorTextColor;this.git0=(0,p.A)(this.pie1,25)||this.primaryColor;this.git1=this.pie2||this.secondaryColor;this.git2=this.pie3||this.tertiaryColor;this.git3=this.pie4||l(this.primaryColor,{h:-30});this.git4=this.pie5||l(this.primaryColor,{h:-60});this.git5=this.pie6||l(this.primaryColor,{h:-90});this.git6=this.pie7||l(this.primaryColor,{h:60});this.git7=this.pie8||l(this.primaryColor,{h:120});this.gitInv0=this.gitInv0||f(this.git0);this.gitInv1=this.gitInv1||f(this.git1);this.gitInv2=this.gitInv2||f(this.git2);this.gitInv3=this.gitInv3||f(this.git3);this.gitInv4=this.gitInv4||f(this.git4);this.gitInv5=this.gitInv5||f(this.git5);this.gitInv6=this.gitInv6||f(this.git6);this.gitInv7=this.gitInv7||f(this.git7);this.branchLabelColor=this.branchLabelColor||this.labelTextColor;this.gitBranchLabel0=this.branchLabelColor;this.gitBranchLabel1="white";this.gitBranchLabel2=this.branchLabelColor;this.gitBranchLabel3="white";this.gitBranchLabel4=this.branchLabelColor;this.gitBranchLabel5=this.branchLabelColor;this.gitBranchLabel6=this.branchLabelColor;this.gitBranchLabel7=this.branchLabelColor;this.tagLabelColor=this.tagLabelColor||this.primaryTextColor;this.tagLabelBackground=this.tagLabelBackground||this.primaryColor;this.tagLabelBorder=this.tagBorder||this.primaryBorderColor;this.tagLabelFontSize=this.tagLabelFontSize||"10px";this.commitLabelColor=this.commitLabelColor||this.secondaryTextColor;this.commitLabelBackground=this.commitLabelBackground||this.secondaryColor;this.commitLabelFontSize=this.commitLabelFontSize||"10px";this.attributeBackgroundColorOdd=this.attributeBackgroundColorOdd||I;this.attributeBackgroundColorEven=this.attributeBackgroundColorEven||K}calculate(t){if(typeof t!=="object"){this.updateColors();return}const e=Object.keys(t);e.forEach((e=>{this[e]=t[e]}));this.updateColors();e.forEach((e=>{this[e]=t[e]}))}};var G=x((t=>{const e=new U;e.calculate(t);return e}),"getThemeVariables");var V={base:{getThemeVariables:z},dark:{getThemeVariables:N},default:{getThemeVariables:j},forest:{getThemeVariables:Y},neutral:{getThemeVariables:G}};var X={flowchart:{useMaxWidth:true,titleTopMargin:25,subGraphTitleMargin:{top:0,bottom:0},diagramPadding:8,htmlLabels:true,nodeSpacing:50,rankSpacing:50,curve:"basis",padding:15,defaultRenderer:"dagre-wrapper",wrappingWidth:200},sequence:{useMaxWidth:true,hideUnusedParticipants:false,activationWidth:10,diagramMarginX:50,diagramMarginY:10,actorMargin:50,width:150,height:65,boxMargin:10,boxTextMargin:5,noteMargin:10,messageMargin:35,messageAlign:"center",mirrorActors:true,forceMenus:false,bottomMarginAdj:1,rightAngles:false,showSequenceNumbers:false,actorFontSize:14,actorFontFamily:'"Open Sans", sans-serif',actorFontWeight:400,noteFontSize:14,noteFontFamily:'"trebuchet ms", verdana, arial, sans-serif',noteFontWeight:400,noteAlign:"center",messageFontSize:16,messageFontFamily:'"trebuchet ms", verdana, arial, sans-serif',messageFontWeight:400,wrap:false,wrapPadding:10,labelBoxWidth:50,labelBoxHeight:20},gantt:{useMaxWidth:true,titleTopMargin:25,barHeight:20,barGap:4,topPadding:50,rightPadding:75,leftPadding:75,gridLineStartPadding:35,fontSize:11,sectionFontSize:11,numberSectionStyles:4,axisFormat:"%Y-%m-%d",topAxis:false,displayMode:"",weekday:"sunday"},journey:{useMaxWidth:true,diagramMarginX:50,diagramMarginY:10,leftMargin:150,width:150,height:50,boxMargin:10,boxTextMargin:5,noteMargin:10,messageMargin:35,messageAlign:"center",bottomMarginAdj:1,rightAngles:false,taskFontSize:14,taskFontFamily:'"Open Sans", sans-serif',taskMargin:50,activationWidth:10,textPlacement:"fo",actorColours:["#8FBC8F","#7CFC00","#00FFFF","#20B2AA","#B0E0E6","#FFFFE0"],sectionFills:["#191970","#8B008B","#4B0082","#2F4F4F","#800000","#8B4513","#00008B"],sectionColours:["#fff"]},class:{useMaxWidth:true,titleTopMargin:25,arrowMarkerAbsolute:false,dividerMargin:10,padding:5,textHeight:10,defaultRenderer:"dagre-wrapper",htmlLabels:false,hideEmptyMembersBox:false},state:{useMaxWidth:true,titleTopMargin:25,dividerMargin:10,sizeUnit:5,padding:8,textHeight:10,titleShift:-15,noteMargin:10,forkWidth:70,forkHeight:7,miniPadding:2,fontSizeFactor:5.02,fontSize:24,labelHeight:16,edgeLengthFactor:"20",compositTitleSize:35,radius:5,defaultRenderer:"dagre-wrapper"},er:{useMaxWidth:true,titleTopMargin:25,diagramPadding:20,layoutDirection:"TB",minEntityWidth:100,minEntityHeight:75,entityPadding:15,nodeSpacing:140,rankSpacing:80,stroke:"gray",fill:"honeydew",fontSize:12},pie:{useMaxWidth:true,textPosition:.75},quadrantChart:{useMaxWidth:true,chartWidth:500,chartHeight:500,titleFontSize:20,titlePadding:10,quadrantPadding:5,xAxisLabelPadding:5,yAxisLabelPadding:5,xAxisLabelFontSize:16,yAxisLabelFontSize:16,quadrantLabelFontSize:16,quadrantTextTopPadding:5,pointTextPadding:5,pointLabelFontSize:12,pointRadius:5,xAxisPosition:"top",yAxisPosition:"left",quadrantInternalBorderStrokeWidth:1,quadrantExternalBorderStrokeWidth:2},xyChart:{useMaxWidth:true,width:700,height:500,titleFontSize:20,titlePadding:10,showTitle:true,xAxis:{$ref:"#/$defs/XYChartAxisConfig",showLabel:true,labelFontSize:14,labelPadding:5,showTitle:true,titleFontSize:16,titlePadding:5,showTick:true,tickLength:5,tickWidth:2,showAxisLine:true,axisLineWidth:2},yAxis:{$ref:"#/$defs/XYChartAxisConfig",showLabel:true,labelFontSize:14,labelPadding:5,showTitle:true,titleFontSize:16,titlePadding:5,showTick:true,tickLength:5,tickWidth:2,showAxisLine:true,axisLineWidth:2},chartOrientation:"vertical",plotReservedSpacePercent:50},requirement:{useMaxWidth:true,rect_fill:"#f9f9f9",text_color:"#333",rect_border_size:"0.5px",rect_border_color:"#bbb",rect_min_width:200,rect_min_height:200,fontSize:14,rect_padding:10,line_height:20},mindmap:{useMaxWidth:true,padding:10,maxNodeWidth:200},kanban:{useMaxWidth:true,padding:8,sectionWidth:200,ticketBaseUrl:""},timeline:{useMaxWidth:true,diagramMarginX:50,diagramMarginY:10,leftMargin:150,width:150,height:50,boxMargin:10,boxTextMargin:5,noteMargin:10,messageMargin:35,messageAlign:"center",bottomMarginAdj:1,rightAngles:false,taskFontSize:14,taskFontFamily:'"Open Sans", sans-serif',taskMargin:50,activationWidth:10,textPlacement:"fo",actorColours:["#8FBC8F","#7CFC00","#00FFFF","#20B2AA","#B0E0E6","#FFFFE0"],sectionFills:["#191970","#8B008B","#4B0082","#2F4F4F","#800000","#8B4513","#00008B"],sectionColours:["#fff"],disableMulticolor:false},gitGraph:{useMaxWidth:true,titleTopMargin:25,diagramPadding:8,nodeLabel:{width:75,height:100,x:-25,y:0},mainBranchName:"main",mainBranchOrder:0,showCommitLabel:true,showBranches:true,rotateCommitLabel:true,parallelCommits:false,arrowMarkerAbsolute:false},c4:{useMaxWidth:true,diagramMarginX:50,diagramMarginY:10,c4ShapeMargin:50,c4ShapePadding:20,width:216,height:60,boxMargin:10,c4ShapeInRow:4,nextLinePaddingX:0,c4BoundaryInRow:2,personFontSize:14,personFontFamily:'"Open Sans", sans-serif',personFontWeight:"normal",external_personFontSize:14,external_personFontFamily:'"Open Sans", sans-serif',external_personFontWeight:"normal",systemFontSize:14,systemFontFamily:'"Open Sans", sans-serif',systemFontWeight:"normal",external_systemFontSize:14,external_systemFontFamily:'"Open Sans", sans-serif',external_systemFontWeight:"normal",system_dbFontSize:14,system_dbFontFamily:'"Open Sans", sans-serif',system_dbFontWeight:"normal",external_system_dbFontSize:14,external_system_dbFontFamily:'"Open Sans", sans-serif',external_system_dbFontWeight:"normal",system_queueFontSize:14,system_queueFontFamily:'"Open Sans", sans-serif',system_queueFontWeight:"normal",external_system_queueFontSize:14,external_system_queueFontFamily:'"Open Sans", sans-serif',external_system_queueFontWeight:"normal",boundaryFontSize:14,boundaryFontFamily:'"Open Sans", sans-serif',boundaryFontWeight:"normal",messageFontSize:12,messageFontFamily:'"Open Sans", sans-serif',messageFontWeight:"normal",containerFontSize:14,containerFontFamily:'"Open Sans", sans-serif',containerFontWeight:"normal",external_containerFontSize:14,external_containerFontFamily:'"Open Sans", sans-serif',external_containerFontWeight:"normal",container_dbFontSize:14,container_dbFontFamily:'"Open Sans", sans-serif',container_dbFontWeight:"normal",external_container_dbFontSize:14,external_container_dbFontFamily:'"Open Sans", sans-serif',external_container_dbFontWeight:"normal",container_queueFontSize:14,container_queueFontFamily:'"Open Sans", sans-serif',container_queueFontWeight:"normal",external_container_queueFontSize:14,external_container_queueFontFamily:'"Open Sans", sans-serif',external_container_queueFontWeight:"normal",componentFontSize:14,componentFontFamily:'"Open Sans", sans-serif',componentFontWeight:"normal",external_componentFontSize:14,external_componentFontFamily:'"Open Sans", sans-serif',external_componentFontWeight:"normal",component_dbFontSize:14,component_dbFontFamily:'"Open Sans", sans-serif',component_dbFontWeight:"normal",external_component_dbFontSize:14,external_component_dbFontFamily:'"Open Sans", sans-serif',external_component_dbFontWeight:"normal",component_queueFontSize:14,component_queueFontFamily:'"Open Sans", sans-serif',component_queueFontWeight:"normal",external_component_queueFontSize:14,external_component_queueFontFamily:'"Open Sans", sans-serif',external_component_queueFontWeight:"normal",wrap:true,wrapPadding:10,person_bg_color:"#08427B",person_border_color:"#073B6F",external_person_bg_color:"#686868",external_person_border_color:"#8A8A8A",system_bg_color:"#1168BD",system_border_color:"#3C7FC0",system_db_bg_color:"#1168BD",system_db_border_color:"#3C7FC0",system_queue_bg_color:"#1168BD",system_queue_border_color:"#3C7FC0",external_system_bg_color:"#999999",external_system_border_color:"#8A8A8A",external_system_db_bg_color:"#999999",external_system_db_border_color:"#8A8A8A",external_system_queue_bg_color:"#999999",external_system_queue_border_color:"#8A8A8A",container_bg_color:"#438DD5",container_border_color:"#3C7FC0",container_db_bg_color:"#438DD5",container_db_border_color:"#3C7FC0",container_queue_bg_color:"#438DD5",container_queue_border_color:"#3C7FC0",external_container_bg_color:"#B3B3B3",external_container_border_color:"#A6A6A6",external_container_db_bg_color:"#B3B3B3",external_container_db_border_color:"#A6A6A6",external_container_queue_bg_color:"#B3B3B3",external_container_queue_border_color:"#A6A6A6",component_bg_color:"#85BBF0",component_border_color:"#78A8D8",component_db_bg_color:"#85BBF0",component_db_border_color:"#78A8D8",component_queue_bg_color:"#85BBF0",component_queue_border_color:"#78A8D8",external_component_bg_color:"#CCCCCC",external_component_border_color:"#BFBFBF",external_component_db_bg_color:"#CCCCCC",external_component_db_border_color:"#BFBFBF",external_component_queue_bg_color:"#CCCCCC",external_component_queue_border_color:"#BFBFBF"},sankey:{useMaxWidth:true,width:600,height:400,linkColor:"gradient",nodeAlignment:"justify",showValues:true,prefix:"",suffix:""},block:{useMaxWidth:true,padding:8},packet:{useMaxWidth:true,rowHeight:32,bitWidth:32,bitsPerRow:32,showBits:true,paddingX:5,paddingY:5},architecture:{useMaxWidth:true,padding:40,iconSize:80,fontSize:16},radar:{useMaxWidth:true,width:600,height:600,marginTop:50,marginRight:50,marginBottom:50,marginLeft:50,axisScaleFactor:1,axisLabelFactor:1.05,curveTension:.17},theme:"default",look:"classic",handDrawnSeed:0,layout:"dagre",maxTextSize:5e4,maxEdges:500,darkMode:false,fontFamily:'"trebuchet ms", verdana, arial, sans-serif;',logLevel:5,securityLevel:"strict",startOnLoad:true,arrowMarkerAbsolute:false,secure:["secure","securityLevel","startOnLoad","maxTextSize","suppressErrorRendering","maxEdges"],legacyMathML:false,forceLegacyMathML:false,deterministicIds:false,fontSize:16,markdownAutoWrap:true,suppressErrorRendering:false};var Z={...X,deterministicIDSeed:void 0,elk:{mergeEdges:false,nodePlacementStrategy:"BRANDES_KOEPF"},themeCSS:void 0,themeVariables:V.default.getThemeVariables(),sequence:{...X.sequence,messageFont:x((function(){return{fontFamily:this.messageFontFamily,fontSize:this.messageFontSize,fontWeight:this.messageFontWeight}}),"messageFont"),noteFont:x((function(){return{fontFamily:this.noteFontFamily,fontSize:this.noteFontSize,fontWeight:this.noteFontWeight}}),"noteFont"),actorFont:x((function(){return{fontFamily:this.actorFontFamily,fontSize:this.actorFontSize,fontWeight:this.actorFontWeight}}),"actorFont")},class:{hideEmptyMembersBox:false},gantt:{...X.gantt,tickInterval:void 0,useWidth:void 0},c4:{...X.c4,useWidth:void 0,personFont:x((function(){return{fontFamily:this.personFontFamily,fontSize:this.personFontSize,fontWeight:this.personFontWeight}}),"personFont"),external_personFont:x((function(){return{fontFamily:this.external_personFontFamily,fontSize:this.external_personFontSize,fontWeight:this.external_personFontWeight}}),"external_personFont"),systemFont:x((function(){return{fontFamily:this.systemFontFamily,fontSize:this.systemFontSize,fontWeight:this.systemFontWeight}}),"systemFont"),external_systemFont:x((function(){return{fontFamily:this.external_systemFontFamily,fontSize:this.external_systemFontSize,fontWeight:this.external_systemFontWeight}}),"external_systemFont"),system_dbFont:x((function(){return{fontFamily:this.system_dbFontFamily,fontSize:this.system_dbFontSize,fontWeight:this.system_dbFontWeight}}),"system_dbFont"),external_system_dbFont:x((function(){return{fontFamily:this.external_system_dbFontFamily,fontSize:this.external_system_dbFontSize,fontWeight:this.external_system_dbFontWeight}}),"external_system_dbFont"),system_queueFont:x((function(){return{fontFamily:this.system_queueFontFamily,fontSize:this.system_queueFontSize,fontWeight:this.system_queueFontWeight}}),"system_queueFont"),external_system_queueFont:x((function(){return{fontFamily:this.external_system_queueFontFamily,fontSize:this.external_system_queueFontSize,fontWeight:this.external_system_queueFontWeight}}),"external_system_queueFont"),containerFont:x((function(){return{fontFamily:this.containerFontFamily,fontSize:this.containerFontSize,fontWeight:this.containerFontWeight}}),"containerFont"),external_containerFont:x((function(){return{fontFamily:this.external_containerFontFamily,fontSize:this.external_containerFontSize,fontWeight:this.external_containerFontWeight}}),"external_containerFont"),container_dbFont:x((function(){return{fontFamily:this.container_dbFontFamily,fontSize:this.container_dbFontSize,fontWeight:this.container_dbFontWeight}}),"container_dbFont"),external_container_dbFont:x((function(){return{fontFamily:this.external_container_dbFontFamily,fontSize:this.external_container_dbFontSize,fontWeight:this.external_container_dbFontWeight}}),"external_container_dbFont"),container_queueFont:x((function(){return{fontFamily:this.container_queueFontFamily,fontSize:this.container_queueFontSize,fontWeight:this.container_queueFontWeight}}),"container_queueFont"),external_container_queueFont:x((function(){return{fontFamily:this.external_container_queueFontFamily,fontSize:this.external_container_queueFontSize,fontWeight:this.external_container_queueFontWeight}}),"external_container_queueFont"),componentFont:x((function(){return{fontFamily:this.componentFontFamily,fontSize:this.componentFontSize,fontWeight:this.componentFontWeight}}),"componentFont"),external_componentFont:x((function(){return{fontFamily:this.external_componentFontFamily,fontSize:this.external_componentFontSize,fontWeight:this.external_componentFontWeight}}),"external_componentFont"),component_dbFont:x((function(){return{fontFamily:this.component_dbFontFamily,fontSize:this.component_dbFontSize,fontWeight:this.component_dbFontWeight}}),"component_dbFont"),external_component_dbFont:x((function(){return{fontFamily:this.external_component_dbFontFamily,fontSize:this.external_component_dbFontSize,fontWeight:this.external_component_dbFontWeight}}),"external_component_dbFont"),component_queueFont:x((function(){return{fontFamily:this.component_queueFontFamily,fontSize:this.component_queueFontSize,fontWeight:this.component_queueFontWeight}}),"component_queueFont"),external_component_queueFont:x((function(){return{fontFamily:this.external_component_queueFontFamily,fontSize:this.external_component_queueFontSize,fontWeight:this.external_component_queueFontWeight}}),"external_component_queueFont"),boundaryFont:x((function(){return{fontFamily:this.boundaryFontFamily,fontSize:this.boundaryFontSize,fontWeight:this.boundaryFontWeight}}),"boundaryFont"),messageFont:x((function(){return{fontFamily:this.messageFontFamily,fontSize:this.messageFontSize,fontWeight:this.messageFontWeight}}),"messageFont")},pie:{...X.pie,useWidth:984},xyChart:{...X.xyChart,useWidth:void 0},requirement:{...X.requirement,useWidth:void 0},packet:{...X.packet},radar:{...X.radar}};var J=x(((t,e="")=>Object.keys(t).reduce(((r,i)=>{if(Array.isArray(t[i])){return r}else if(typeof t[i]==="object"&&t[i]!==null){return[...r,e+i,...J(t[i],"")]}return[...r,e+i]}),[])),"keyify");var Q=new Set(J(Z,""));var tt=Z;var et=x((t=>{k.debug("sanitizeDirective called with",t);if(typeof t!=="object"||t==null){return}if(Array.isArray(t)){t.forEach((t=>et(t)));return}for(const e of Object.keys(t)){k.debug("Checking key",e);if(e.startsWith("__")||e.includes("proto")||e.includes("constr")||!Q.has(e)||t[e]==null){k.debug("sanitize deleting key: ",e);delete t[e];continue}if(typeof t[e]==="object"){k.debug("sanitizing object",e);et(t[e]);continue}const r=["themeCSS","fontFamily","altFontFamily"];for(const i of r){if(e.includes(i)){k.debug("sanitizing css option",e);t[e]=rt(t[e])}}}if(t.themeVariables){for(const e of Object.keys(t.themeVariables)){const r=t.themeVariables[e];if(r?.match&&!r.match(/^[\d "#%(),.;A-Za-z]+$/)){t.themeVariables[e]=""}}}k.debug("After sanitization",t)}),"sanitizeDirective");var rt=x((t=>{let e=0;let r=0;for(const i of t){if(e<r){return"{ /* ERROR: Unbalanced CSS */ }"}if(i==="{"){e++}else if(i==="}"){r++}}if(e!==r){return"{ /* ERROR: Unbalanced CSS */ }"}return t}),"sanitizeCss");var it=Object.freeze(tt);var at=D({},it);var nt;var ot=[];var st=D({},it);var lt=x(((t,e)=>{let r=D({},t);let i={};for(const a of e){gt(a);i=D(i,a)}r=D(r,i);if(i.theme&&i.theme in V){const t=D({},nt);const e=D(t.themeVariables||{},i.themeVariables);if(r.theme&&r.theme in V){r.themeVariables=V[r.theme].getThemeVariables(e)}}st=r;vt(st);return st}),"updateCurrentConfig");var ct=x((t=>{at=D({},it);at=D(at,t);if(t.theme&&V[t.theme]){at.themeVariables=V[t.theme].getThemeVariables(t.themeVariables)}lt(at,ot);return at}),"setSiteConfig");var ht=x((t=>{nt=D({},t)}),"saveConfigFromInitialize");var dt=x((t=>{at=D(at,t);lt(at,ot);return at}),"updateSiteConfig");var ut=x((()=>D({},at)),"getSiteConfig");var ft=x((t=>{vt(t);D(st,t);return pt()}),"setConfig");var pt=x((()=>D({},st)),"getConfig");var gt=x((t=>{if(!t){return}["secure",...at.secure??[]].forEach((e=>{if(Object.hasOwn(t,e)){k.debug(`Denied attempt to modify a secure key ${e}`,t[e]);delete t[e]}}));Object.keys(t).forEach((e=>{if(e.startsWith("__")){delete t[e]}}));Object.keys(t).forEach((e=>{if(typeof t[e]==="string"&&(t[e].includes("<")||t[e].includes(">")||t[e].includes("url(data:"))){delete t[e]}if(typeof t[e]==="object"){gt(t[e])}}))}),"sanitize");var mt=x((t=>{et(t);if(t.fontFamily&&!t.themeVariables?.fontFamily){t.themeVariables={...t.themeVariables,fontFamily:t.fontFamily}}ot.push(t);lt(at,ot)}),"addDirective");var yt=x(((t=at)=>{ot=[];lt(t,ot)}),"reset");var bt={LAZY_LOAD_DEPRECATED:"The configuration options lazyLoadedDiagrams and loadExternalDiagramsAtStartup are deprecated. Please use registerExternalDiagrams instead."};var xt={};var Ct=x((t=>{if(xt[t]){return}k.warn(bt[t]);xt[t]=true}),"issueWarning");var vt=x((t=>{if(!t){return}if(t.lazyLoadedDiagrams||t.loadExternalDiagramsAtStartup){Ct("LAZY_LOAD_DEPRECATED")}}),"checkConfig");var kt=/<br\s*\/?>/gi;var wt=x((t=>{if(!t){return[""]}const e=Et(t).replace(/\\n/g,"#br#");return e.split("#br#")}),"getRows");var St=(()=>{let t=false;return()=>{if(!t){At();t=true}}})();function At(){const t="data-temp-href-target";y.A.addHook("beforeSanitizeAttributes",(e=>{if(e instanceof Element&&e.tagName==="A"&&e.hasAttribute("target")){e.setAttribute(t,e.getAttribute("target")??"")}}));y.A.addHook("afterSanitizeAttributes",(e=>{if(e instanceof Element&&e.tagName==="A"&&e.hasAttribute(t)){e.setAttribute("target",e.getAttribute(t)??"");e.removeAttribute(t);if(e.getAttribute("target")==="_blank"){e.setAttribute("rel","noopener")}}}))}x(At,"setupDompurifyHooks");var Tt=x((t=>{St();const e=y.A.sanitize(t);return e}),"removeScript");var Bt=x(((t,e)=>{if(e.flowchart?.htmlLabels!==false){const r=e.securityLevel;if(r==="antiscript"||r==="strict"){t=Tt(t)}else if(r!=="loose"){t=Et(t);t=t.replace(/</g,"&lt;").replace(/>/g,"&gt;");t=t.replace(/=/g,"&equals;");t=$t(t)}}return t}),"sanitizeMore");var Lt=x(((t,e)=>{if(!t){return t}if(e.dompurifyConfig){t=y.A.sanitize(Bt(t,e),e.dompurifyConfig).toString()}else{t=y.A.sanitize(Bt(t,e),{FORBID_TAGS:["style"]}).toString()}return t}),"sanitizeText");var Mt=x(((t,e)=>{if(typeof t==="string"){return Lt(t,e)}return t.flat().map((t=>Lt(t,e)))}),"sanitizeTextOrArray");var _t=x((t=>kt.test(t)),"hasBreaks");var Ft=x((t=>t.split(kt)),"splitBreaks");var $t=x((t=>t.replace(/#br#/g,"<br/>")),"placeholderToBreak");var Et=x((t=>t.replace(kt,"#br#")),"breakToPlaceholder");var Ot=x((t=>{let e="";if(t){e=window.location.protocol+"//"+window.location.host+window.location.pathname+window.location.search;e=e.replaceAll(/\(/g,"\\(");e=e.replaceAll(/\)/g,"\\)")}return e}),"getUrl");var Dt=x((t=>t===false||["false","null","0"].includes(String(t).trim().toLowerCase())?false:true),"evaluate");var It=x((function(...t){const e=t.filter((t=>!isNaN(t)));return Math.max(...e)}),"getMax");var Kt=x((function(...t){const e=t.filter((t=>!isNaN(t)));return Math.min(...e)}),"getMin");var Rt=x((function(t){const e=t.split(/(,)/);const r=[];for(let i=0;i<e.length;i++){let t=e[i];if(t===","&&i>0&&i+1<e.length){const a=e[i-1];const n=e[i+1];if(zt(a,n)){t=a+","+n;i++;r.pop()}}r.push(qt(t))}return r.join("")}),"parseGenericTypes");var Pt=x(((t,e)=>Math.max(0,t.split(e).length-1)),"countOccurrence");var zt=x(((t,e)=>{const r=Pt(t,"~");const i=Pt(e,"~");return r===1&&i===1}),"shouldCombineSets");var qt=x((t=>{const e=Pt(t,"~");let r=false;if(e<=1){return t}if(e%2!==0&&t.startsWith("~")){t=t.substring(1);r=true}const i=[...t];let a=i.indexOf("~");let n=i.lastIndexOf("~");while(a!==-1&&n!==-1&&a!==n){i[a]="<";i[n]=">";a=i.indexOf("~");n=i.lastIndexOf("~")}if(r){i.unshift("~")}return i.join("")}),"processSet");var Nt=x((()=>window.MathMLElement!==void 0),"isMathMLSupported");var Wt=/\$\$(.*)\$\$/g;var jt=x((t=>(t.match(Wt)?.length??0)>0),"hasKatex");var Ht=x((async(t,e)=>{t=await Yt(t,e);const r=document.createElement("div");r.innerHTML=t;r.id="katex-temp";r.style.visibility="hidden";r.style.position="absolute";r.style.top="0";const i=document.querySelector("body");i?.insertAdjacentElement("beforeend",r);const a={width:r.clientWidth,height:r.clientHeight};r.remove();return a}),"calculateMathMLDimensions");var Yt=x((async(t,e)=>{if(!jt(t)){return t}if(!(Nt()||e.legacyMathML||e.forceLegacyMathML)){return t.replace(Wt,"MathML is unsupported in this environment.")}const{default:i}=await r.e(5489).then(r.bind(r,25489));const a=e.forceLegacyMathML||!Nt()&&e.legacyMathML?"htmlAndMathml":"mathml";return t.split(kt).map((t=>jt(t)?`<div style="display: flex; align-items: center; justify-content: center; white-space: nowrap;">${t}</div>`:`<div>${t}</div>`)).join("").replace(Wt,((t,e)=>i.renderToString(e,{throwOnError:true,displayMode:true,output:a}).replace(/\n/g," ").replace(/<annotation.*<\/annotation>/g,"")))}),"renderKatex");var Ut={getRows:wt,sanitizeText:Lt,sanitizeTextOrArray:Mt,hasBreaks:_t,splitBreaks:Ft,lineBreakRegex:kt,removeScript:Tt,getUrl:Ot,evaluate:Dt,getMax:It,getMin:Kt};var Gt=x((function(t,e){for(let r of e){t.attr(r[0],r[1])}}),"d3Attrs");var Vt=x((function(t,e,r){let i=new Map;if(r){i.set("width","100%");i.set("style",`max-width: ${e}px;`)}else{i.set("height",t);i.set("width",e)}return i}),"calculateSvgSizeAttrs");var Xt=x((function(t,e,r,i){const a=Vt(e,r,i);Gt(t,a)}),"configureSvgSize");var Zt=x((function(t,e,r,i){const a=e.node().getBBox();const n=a.width;const o=a.height;k.info(`SVG bounds: ${n}x${o}`,a);let s=0;let l=0;k.info(`Graph bounds: ${s}x${l}`,t);s=n+r*2;l=o+r*2;k.info(`Calculated bounds: ${s}x${l}`);Xt(e,l,s,i);const c=`${a.x-r} ${a.y-r} ${a.width+2*r} ${a.height+2*r}`;e.attr("viewBox",c)}),"setupGraphViewbox");var Jt={};var Qt=x(((t,e,r)=>{let i="";if(t in Jt&&Jt[t]){i=Jt[t](r)}else{k.warn(`No theme found for ${t}`)}return` & {\n    font-family: ${r.fontFamily};\n    font-size: ${r.fontSize};\n    fill: ${r.textColor}\n  }\n  @keyframes edge-animation-frame {\n    from {\n      stroke-dashoffset: 0;\n    }\n  }\n  @keyframes dash {\n    to {\n      stroke-dashoffset: 0;\n    }\n  }\n  & .edge-animation-slow {\n    stroke-dasharray: 9,5 !important;\n    stroke-dashoffset: 900;\n    animation: dash 50s linear infinite;\n    stroke-linecap: round;\n  }\n  & .edge-animation-fast {\n    stroke-dasharray: 9,5 !important;\n    stroke-dashoffset: 900;\n    animation: dash 20s linear infinite;\n    stroke-linecap: round;\n  }\n  /* Classes common for multiple diagrams */\n\n  & .error-icon {\n    fill: ${r.errorBkgColor};\n  }\n  & .error-text {\n    fill: ${r.errorTextColor};\n    stroke: ${r.errorTextColor};\n  }\n\n  & .edge-thickness-normal {\n    stroke-width: 1px;\n  }\n  & .edge-thickness-thick {\n    stroke-width: 3.5px\n  }\n  & .edge-pattern-solid {\n    stroke-dasharray: 0;\n  }\n  & .edge-thickness-invisible {\n    stroke-width: 0;\n    fill: none;\n  }\n  & .edge-pattern-dashed{\n    stroke-dasharray: 3;\n  }\n  .edge-pattern-dotted {\n    stroke-dasharray: 2;\n  }\n\n  & .marker {\n    fill: ${r.lineColor};\n    stroke: ${r.lineColor};\n  }\n  & .marker.cross {\n    stroke: ${r.lineColor};\n  }\n\n  & svg {\n    font-family: ${r.fontFamily};\n    font-size: ${r.fontSize};\n  }\n   & p {\n    margin: 0\n   }\n\n  ${i}\n\n  ${e}\n`}),"getStyles");var te=x(((t,e)=>{if(e!==void 0){Jt[t]=e}}),"addStylesForDiagram");var ee=Qt;var re={};C(re,{clear:()=>se,getAccDescription:()=>de,getAccTitle:()=>ce,getDiagramTitle:()=>fe,setAccDescription:()=>he,setAccTitle:()=>le,setDiagramTitle:()=>ue});var ie="";var ae="";var ne="";var oe=x((t=>Lt(t,pt())),"sanitizeText");var se=x((()=>{ie="";ne="";ae=""}),"clear");var le=x((t=>{ie=oe(t).replace(/^\s+/g,"")}),"setAccTitle");var ce=x((()=>ie),"getAccTitle");var he=x((t=>{ne=oe(t).replace(/\n\s+/g,"\n")}),"setAccDescription");var de=x((()=>ne),"getAccDescription");var ue=x((t=>{ae=oe(t)}),"setDiagramTitle");var fe=x((()=>ae),"getDiagramTitle");var pe=k;var ge=w;var me=pt;var ye=ft;var be=it;var xe=x((t=>Lt(t,me())),"sanitizeText");var Ce=Zt;var ve=x((()=>re),"getCommonDb");var ke={};var we=x(((t,e,r)=>{if(ke[t]){pe.warn(`Diagram with id ${t} already registered. Overwriting.`)}ke[t]=e;if(r){$(t,r)}te(t,e.styles);e.injectUtils?.(pe,ge,me,xe,Ce,ve(),(()=>{}))}),"registerDiagram");var Se=x((t=>{if(t in ke){return ke[t]}throw new Ae(t)}),"getDiagram");var Ae=class extends Error{static{x(this,"DiagramNotFoundError")}constructor(t){super(`Diagram ${t} not found.`)}}},90227:(t,e,r)=>{"use strict";r.r(e);r.d(e,{default:()=>la});var i=r(97366);var a=r(94065);var n=r(33416);var o=r(94746);var s=r(20778);var l=r(57590);var c=r(68232);var h=r(76261);var d=r(96049);var u=r(59357);var f=r(93113);var p=r(75905);var g=r(60513);var m=r(24982);var y="-ms-";var b="-moz-";var x="-webkit-";var C="comm";var v="rule";var k="decl";var w="@page";var S="@media";var A="@import";var T="@charset";var B="@viewport";var L="@supports";var M="@document";var _="@namespace";var F="@keyframes";var $="@font-face";var E="@counter-style";var O="@font-feature-values";var D="@layer";var I="@scope";var K=Math.abs;var R=String.fromCharCode;var P=Object.assign;function z(t,e){return H(t,0)^45?(((e<<2^H(t,0))<<2^H(t,1))<<2^H(t,2))<<2^H(t,3):0}function q(t){return t.trim()}function N(t,e){return(t=e.exec(t))?t[0]:t}function W(t,e,r){return t.replace(e,r)}function j(t,e,r){return t.indexOf(e,r)}function H(t,e){return t.charCodeAt(e)|0}function Y(t,e,r){return t.slice(e,r)}function U(t){return t.length}function G(t){return t.length}function V(t,e){return e.push(t),t}function X(t,e){return t.map(e).join("")}function Z(t,e){return t.filter((function(t){return!N(t,e)}))}function J(t,e){var r="";for(var i=0;i<t.length;i++)r+=e(t[i],i,t,e)||"";return r}function Q(t,e,r,i){switch(t.type){case D:if(t.children.length)break;case A:case _:case k:return t.return=t.return||t.value;case C:return"";case F:return t.return=t.value+"{"+J(t.children,i)+"}";case v:if(!U(t.value=t.props.join(",")))return""}return U(r=J(t.children,i))?t.return=t.value+"{"+r+"}":""}var tt=1;var et=1;var rt=0;var it=0;var at=0;var nt="";function ot(t,e,r,i,a,n,o,s){return{value:t,root:e,parent:r,type:i,props:a,children:n,line:tt,column:et,length:o,return:"",siblings:s}}function st(t,e){return assign(ot("",null,null,"",null,null,0,t.siblings),t,{length:-t.length},e)}function lt(t){while(t.root)t=st(t.root,{children:[t]});append(t,t.siblings)}function ct(){return at}function ht(){at=it>0?H(nt,--it):0;if(et--,at===10)et=1,tt--;return at}function dt(){at=it<rt?H(nt,it++):0;if(et++,at===10)et=1,tt++;return at}function ut(){return H(nt,it)}function ft(){return it}function pt(t,e){return Y(nt,t,e)}function gt(t){switch(t){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function mt(t){return tt=et=1,rt=U(nt=t),it=0,[]}function yt(t){return nt="",t}function bt(t){return q(pt(it-1,wt(t===91?t+2:t===40?t+1:t)))}function xt(t){return yt(vt(mt(t)))}function Ct(t){while(at=ut())if(at<33)dt();else break;return gt(t)>2||gt(at)>3?"":" "}function vt(t){while(dt())switch(gt(at)){case 0:append(At(it-1),t);break;case 2:append(bt(at),t);break;default:append(from(at),t)}return t}function kt(t,e){while(--e&&dt())if(at<48||at>102||at>57&&at<65||at>70&&at<97)break;return pt(t,ft()+(e<6&&ut()==32&&dt()==32))}function wt(t){while(dt())switch(at){case t:return it;case 34:case 39:if(t!==34&&t!==39)wt(at);break;case 40:if(t===41)wt(t);break;case 92:dt();break}return it}function St(t,e){while(dt())if(t+at===47+10)break;else if(t+at===42+42&&ut()===47)break;return"/*"+pt(e,it-1)+"*"+R(t===47?t:dt())}function At(t){while(!gt(ut()))dt();return pt(t,it)}function Tt(t){return yt(Bt("",null,null,null,[""],t=mt(t),0,[0],t))}function Bt(t,e,r,i,a,n,o,s,l){var c=0;var h=0;var d=o;var u=0;var f=0;var p=0;var g=1;var m=1;var y=1;var b=0;var x="";var C=a;var v=n;var k=i;var w=x;while(m)switch(p=b,b=dt()){case 40:if(p!=108&&H(w,d-1)==58){if(j(w+=W(bt(b),"&","&\f"),"&\f",K(c?s[c-1]:0))!=-1)y=-1;break}case 34:case 39:case 91:w+=bt(b);break;case 9:case 10:case 13:case 32:w+=Ct(p);break;case 92:w+=kt(ft()-1,7);continue;case 47:switch(ut()){case 42:case 47:V(Mt(St(dt(),ft()),e,r,l),l);if((gt(p||1)==5||gt(ut()||1)==5)&&U(w)&&Y(w,-1,void 0)!==" ")w+=" ";break;default:w+="/"}break;case 123*g:s[c++]=U(w)*y;case 125*g:case 59:case 0:switch(b){case 0:case 125:m=0;case 59+h:if(y==-1)w=W(w,/\f/g,"");if(f>0&&(U(w)-d||g===0&&p===47))V(f>32?_t(w+";",i,r,d-1,l):_t(W(w," ","")+";",i,r,d-2,l),l);break;case 59:w+=";";default:V(k=Lt(w,e,r,c,h,a,s,x,C=[],v=[],d,n),n);if(b===123)if(h===0)Bt(w,e,k,k,C,n,d,s,v);else{switch(u){case 99:if(H(w,3)===110)break;case 108:if(H(w,2)===97)break;default:h=0;case 100:case 109:case 115:}if(h)Bt(t,k,k,i&&V(Lt(t,k,k,0,0,a,s,x,a,C=[],d,v),v),a,v,d,s,i?C:v);else Bt(w,k,k,k,[""],v,0,s,v)}}c=h=f=0,g=y=1,x=w="",d=o;break;case 58:d=1+U(w),f=p;default:if(g<1)if(b==123)--g;else if(b==125&&g++==0&&ht()==125)continue;switch(w+=R(b),b*g){case 38:y=h>0?1:(w+="\f",-1);break;case 44:s[c++]=(U(w)-1)*y,y=1;break;case 64:if(ut()===45)w+=bt(dt());u=ut(),h=d=U(x=w+=At(ft())),b++;break;case 45:if(p===45&&U(w)==2)g=0}}return n}function Lt(t,e,r,i,a,n,o,s,l,c,h,d){var u=a-1;var f=a===0?n:[""];var p=G(f);for(var g=0,m=0,y=0;g<i;++g)for(var b=0,x=Y(t,u+1,u=K(m=o[g])),C=t;b<p;++b)if(C=q(m>0?f[b]+" "+x:W(x,/&\f/g,f[b])))l[y++]=C;return ot(t,e,r,a===0?v:s,l,c,h,d)}function Mt(t,e,r,i){return ot(t,e,r,C,R(ct()),Y(t,2,-2),0,i)}function _t(t,e,r,i,a){return ot(t,e,r,k,Y(t,0,i),Y(t,i+1,-1),i,a)}var Ft=r(84997);var $t=r(74650);var Et="c4";var Ot=(0,p.K2)((t=>/^\s*C4Context|C4Container|C4Component|C4Dynamic|C4Deployment/.test(t)),"detector");var Dt=(0,p.K2)((async()=>{const{diagram:t}=await r.e(1912).then(r.bind(r,71912));return{id:Et,diagram:t}}),"loader");var It={id:Et,detector:Ot,loader:Dt};var Kt=It;var Rt="flowchart";var Pt=(0,p.K2)(((t,e)=>{if(e?.flowchart?.defaultRenderer==="dagre-wrapper"||e?.flowchart?.defaultRenderer==="elk"){return false}return/^\s*graph/.test(t)}),"detector");var zt=(0,p.K2)((async()=>{const{diagram:t}=await r.e(2023).then(r.bind(r,52023));return{id:Rt,diagram:t}}),"loader");var qt={id:Rt,detector:Pt,loader:zt};var Nt=qt;var Wt="flowchart-v2";var jt=(0,p.K2)(((t,e)=>{if(e?.flowchart?.defaultRenderer==="dagre-d3"){return false}if(e?.flowchart?.defaultRenderer==="elk"){e.layout="elk"}if(/^\s*graph/.test(t)&&e?.flowchart?.defaultRenderer==="dagre-wrapper"){return true}return/^\s*flowchart/.test(t)}),"detector");var Ht=(0,p.K2)((async()=>{const{diagram:t}=await r.e(2023).then(r.bind(r,52023));return{id:Wt,diagram:t}}),"loader");var Yt={id:Wt,detector:jt,loader:Ht};var Ut=Yt;var Gt="er";var Vt=(0,p.K2)((t=>/^\s*erDiagram/.test(t)),"detector");var Xt=(0,p.K2)((async()=>{const{diagram:t}=await r.e(805).then(r.bind(r,70805));return{id:Gt,diagram:t}}),"loader");var Zt={id:Gt,detector:Vt,loader:Xt};var Jt=Zt;var Qt="gitGraph";var te=(0,p.K2)((t=>/^\s*gitGraph/.test(t)),"detector");var ee=(0,p.K2)((async()=>{const{diagram:t}=await Promise.all([r.e(1838),r.e(4010),r.e(9890)]).then(r.bind(r,99890));return{id:Qt,diagram:t}}),"loader");var re={id:Qt,detector:te,loader:ee};var ie=re;var ae="gantt";var ne=(0,p.K2)((t=>/^\s*gantt/.test(t)),"detector");var oe=(0,p.K2)((async()=>{const{diagram:t}=await r.e(9572).then(r.bind(r,87191));return{id:ae,diagram:t}}),"loader");var se={id:ae,detector:ne,loader:oe};var le=se;var ce="info";var he=(0,p.K2)((t=>/^\s*info/.test(t)),"detector");var de=(0,p.K2)((async()=>{const{diagram:t}=await Promise.all([r.e(1838),r.e(4010),r.e(8537)]).then(r.bind(r,98537));return{id:ce,diagram:t}}),"loader");var ue={id:ce,detector:he,loader:de};var fe="pie";var pe=(0,p.K2)((t=>/^\s*pie/.test(t)),"detector");var ge=(0,p.K2)((async()=>{const{diagram:t}=await Promise.all([r.e(1838),r.e(4010),r.e(649)]).then(r.bind(r,70649));return{id:fe,diagram:t}}),"loader");var me={id:fe,detector:pe,loader:ge};var ye="quadrantChart";var be=(0,p.K2)((t=>/^\s*quadrantChart/.test(t)),"detector");var xe=(0,p.K2)((async()=>{const{diagram:t}=await r.e(4311).then(r.bind(r,4311));return{id:ye,diagram:t}}),"loader");var Ce={id:ye,detector:be,loader:xe};var ve=Ce;var ke="xychart";var we=(0,p.K2)((t=>/^\s*xychart-beta/.test(t)),"detector");var Se=(0,p.K2)((async()=>{const{diagram:t}=await r.e(9881).then(r.bind(r,79881));return{id:ke,diagram:t}}),"loader");var Ae={id:ke,detector:we,loader:Se};var Te=Ae;var Be="requirement";var Le=(0,p.K2)((t=>/^\s*requirement(Diagram)?/.test(t)),"detector");var Me=(0,p.K2)((async()=>{const{diagram:t}=await r.e(580).then(r.bind(r,90580));return{id:Be,diagram:t}}),"loader");var _e={id:Be,detector:Le,loader:Me};var Fe=_e;var $e="sequence";var Ee=(0,p.K2)((t=>/^\s*sequenceDiagram/.test(t)),"detector");var Oe=(0,p.K2)((async()=>{const{diagram:t}=await r.e(8038).then(r.bind(r,38038));return{id:$e,diagram:t}}),"loader");var De={id:$e,detector:Ee,loader:Oe};var Ie=De;var Ke="class";var Re=(0,p.K2)(((t,e)=>{if(e?.class?.defaultRenderer==="dagre-wrapper"){return false}return/^\s*classDiagram/.test(t)}),"detector");var Pe=(0,p.K2)((async()=>{const{diagram:t}=await Promise.all([r.e(1359),r.e(3048)]).then(r.bind(r,53048));return{id:Ke,diagram:t}}),"loader");var ze={id:Ke,detector:Re,loader:Pe};var qe=ze;var Ne="classDiagram";var We=(0,p.K2)(((t,e)=>{if(/^\s*classDiagram/.test(t)&&e?.class?.defaultRenderer==="dagre-wrapper"){return true}return/^\s*classDiagram-v2/.test(t)}),"detector");var je=(0,p.K2)((async()=>{const{diagram:t}=await Promise.all([r.e(1359),r.e(874)]).then(r.bind(r,874));return{id:Ne,diagram:t}}),"loader");var He={id:Ne,detector:We,loader:je};var Ye=He;var Ue="state";var Ge=(0,p.K2)(((t,e)=>{if(e?.state?.defaultRenderer==="dagre-wrapper"){return false}return/^\s*stateDiagram/.test(t)}),"detector");var Ve=(0,p.K2)((async()=>{const{diagram:t}=await Promise.all([r.e(1838),r.e(2211),r.e(8855),r.e(8391)]).then(r.bind(r,78391));return{id:Ue,diagram:t}}),"loader");var Xe={id:Ue,detector:Ge,loader:Ve};var Ze=Xe;var Je="stateDiagram";var Qe=(0,p.K2)(((t,e)=>{if(/^\s*stateDiagram-v2/.test(t)){return true}if(/^\s*stateDiagram/.test(t)&&e?.state?.defaultRenderer==="dagre-wrapper"){return true}return false}),"detector");var tr=(0,p.K2)((async()=>{const{diagram:t}=await Promise.all([r.e(8855),r.e(6779)]).then(r.bind(r,16779));return{id:Je,diagram:t}}),"loader");var er={id:Je,detector:Qe,loader:tr};var rr=er;var ir="journey";var ar=(0,p.K2)((t=>/^\s*journey/.test(t)),"detector");var nr=(0,p.K2)((async()=>{const{diagram:t}=await r.e(5135).then(r.bind(r,85135));return{id:ir,diagram:t}}),"loader");var or={id:ir,detector:ar,loader:nr};var sr=or;var lr=(0,p.K2)(((t,e,r)=>{p.Rm.debug("rendering svg for syntax error\n");const i=(0,f.D)(e);const a=i.append("g");i.attr("viewBox","0 0 2412 512");(0,p.a$)(i,100,512,true);a.append("path").attr("class","error-icon").attr("d","m411.313,123.313c6.25-6.25 6.25-16.375 0-22.625s-16.375-6.25-22.625,0l-32,32-9.375,9.375-20.688-20.688c-12.484-12.5-32.766-12.5-45.25,0l-16,16c-1.261,1.261-2.304,2.648-3.31,4.051-21.739-8.561-45.324-13.426-70.065-13.426-105.867,0-192,86.133-192,192s86.133,192 192,192 192-86.133 192-192c0-24.741-4.864-48.327-13.426-70.065 1.402-1.007 2.79-2.049 4.051-3.31l16-16c12.5-12.492 12.5-32.758 0-45.25l-20.688-20.688 9.375-9.375 32.001-31.999zm-219.313,100.687c-52.938,0-96,43.063-96,96 0,8.836-7.164,16-16,16s-16-7.164-16-16c0-70.578 57.***********-128 8.836,0 16,7.164 16,16s-7.164,16-16,16z");a.append("path").attr("class","error-icon").attr("d","m459.02,148.98c-6.25-6.25-16.375-6.25-22.625,0s-6.25,16.375 0,22.625l16,16c3.125,3.125 7.219,4.688 11.313,4.688 4.094,0 8.188-1.563 11.313-4.688 6.25-6.25 6.25-16.375 0-22.625l-16.001-16z");a.append("path").attr("class","error-icon").attr("d","m340.395,75.605c3.125,3.125 7.219,4.688 11.313,4.688 4.094,0 8.188-1.563 11.313-4.688 6.25-6.25 6.25-16.375 0-22.625l-16-16c-6.25-6.25-16.375-6.25-22.625,0s-6.25,16.375 0,22.625l15.999,16z");a.append("path").attr("class","error-icon").attr("d","m400,64c8.844,0 16-7.164 16-16v-32c0-8.836-7.156-16-16-16-8.844,0-16,7.164-16,16v32c0,8.836 7.156,16 16,16z");a.append("path").attr("class","error-icon").attr("d","m496,96.586h-32c-8.844,0-16,7.164-16,16 0,8.836 7.156,16 16,16h32c8.844,0 16-7.164 16-16 0-8.836-7.156-16-16-16z");a.append("path").attr("class","error-icon").attr("d","m436.98,75.605c3.125,3.125 7.219,4.688 11.313,4.688 4.094,0 8.188-1.563 11.313-4.688l32-32c6.25-6.25 6.25-16.375 0-22.625s-16.375-6.25-22.625,0l-32,32c-6.251,6.25-6.251,16.375-0.001,22.625z");a.append("text").attr("class","error-text").attr("x",1440).attr("y",250).attr("font-size","150px").style("text-anchor","middle").text("Syntax error in text");a.append("text").attr("class","error-text").attr("x",1250).attr("y",400).attr("font-size","100px").style("text-anchor","middle").text(`mermaid version ${r}`)}),"draw");var cr={draw:lr};var hr=cr;var dr={db:{},renderer:cr,parser:{parse:(0,p.K2)((()=>{}),"parse")}};var ur=dr;var fr="flowchart-elk";var pr=(0,p.K2)(((t,e={})=>{if(/^\s*flowchart-elk/.test(t)||/^\s*flowchart|graph/.test(t)&&e?.flowchart?.defaultRenderer==="elk"){e.layout="elk";return true}return false}),"detector");var gr=(0,p.K2)((async()=>{const{diagram:t}=await r.e(2023).then(r.bind(r,52023));return{id:fr,diagram:t}}),"loader");var mr={id:fr,detector:pr,loader:gr};var yr=mr;var br="timeline";var xr=(0,p.K2)((t=>/^\s*timeline/.test(t)),"detector");var Cr=(0,p.K2)((async()=>{const{diagram:t}=await r.e(6214).then(r.bind(r,26214));return{id:br,diagram:t}}),"loader");var vr={id:br,detector:xr,loader:Cr};var kr=vr;var wr="mindmap";var Sr=(0,p.K2)((t=>/^\s*mindmap/.test(t)),"detector");var Ar=(0,p.K2)((async()=>{const{diagram:t}=await Promise.all([r.e(8786),r.e(8915)]).then(r.bind(r,18915));return{id:wr,diagram:t}}),"loader");var Tr={id:wr,detector:Sr,loader:Ar};var Br=Tr;var Lr="kanban";var Mr=(0,p.K2)((t=>/^\s*kanban/.test(t)),"detector");var _r=(0,p.K2)((async()=>{const{diagram:t}=await r.e(4982).then(r.bind(r,4982));return{id:Lr,diagram:t}}),"loader");var Fr={id:Lr,detector:Mr,loader:_r};var $r=Fr;var Er="sankey";var Or=(0,p.K2)((t=>/^\s*sankey-beta/.test(t)),"detector");var Dr=(0,p.K2)((async()=>{const{diagram:t}=await r.e(3358).then(r.bind(r,33358));return{id:Er,diagram:t}}),"loader");var Ir={id:Er,detector:Or,loader:Dr};var Kr=Ir;var Rr="packet";var Pr=(0,p.K2)((t=>/^\s*packet-beta/.test(t)),"detector");var zr=(0,p.K2)((async()=>{const{diagram:t}=await Promise.all([r.e(1838),r.e(4010),r.e(2550)]).then(r.bind(r,92550));return{id:Rr,diagram:t}}),"loader");var qr={id:Rr,detector:Pr,loader:zr};var Nr="radar";var Wr=(0,p.K2)((t=>/^\s*radar-beta/.test(t)),"detector");var jr=(0,p.K2)((async()=>{const{diagram:t}=await Promise.all([r.e(1838),r.e(4010),r.e(898)]).then(r.bind(r,80898));return{id:Nr,diagram:t}}),"loader");var Hr={id:Nr,detector:Wr,loader:jr};var Yr="block";var Ur=(0,p.K2)((t=>/^\s*block-beta/.test(t)),"detector");var Gr=(0,p.K2)((async()=>{const{diagram:t}=await Promise.all([r.e(1838),r.e(6364)]).then(r.bind(r,46364));return{id:Yr,diagram:t}}),"loader");var Vr={id:Yr,detector:Ur,loader:Gr};var Xr=Vr;var Zr="architecture";var Jr=(0,p.K2)((t=>/^\s*architecture/.test(t)),"detector");var Qr=(0,p.K2)((async()=>{const{diagram:t}=await Promise.all([r.e(1838),r.e(4010),r.e(8786),r.e(7371)]).then(r.bind(r,17371));return{id:Zr,diagram:t}}),"loader");var ti={id:Zr,detector:Jr,loader:Qr};var ei=ti;var ri=false;var ii=(0,p.K2)((()=>{if(ri){return}ri=true;(0,p.Js)("error",ur,(t=>t.toLowerCase().trim()==="error"));(0,p.Js)("---",{db:{clear:(0,p.K2)((()=>{}),"clear")},styles:{},renderer:{draw:(0,p.K2)((()=>{}),"draw")},parser:{parse:(0,p.K2)((()=>{throw new Error("Diagrams beginning with --- are not valid. If you were trying to use a YAML front-matter, please ensure that you've correctly opened and closed the YAML front-matter with un-indented `---` blocks")}),"parse")},init:(0,p.K2)((()=>null),"init")},(t=>t.toLowerCase().trimStart().startsWith("---")));(0,p.Xd)(Kt,$r,Ye,qe,Jt,le,ue,me,Fe,Ie,yr,Ut,Nt,Br,kr,ie,rr,Ze,sr,ve,Kr,qr,Te,Xr,ei,Hr)}),"addDiagrams");var ai=(0,p.K2)((async()=>{p.Rm.debug(`Loading registered diagrams`);const t=await Promise.allSettled(Object.entries(p.mW).map((async([t,{detector:e,loader:r}])=>{if(r){try{(0,p.Gs)(t)}catch{try{const{diagram:t,id:i}=await r();(0,p.Js)(i,t,e)}catch(i){p.Rm.error(`Failed to load external diagram with key ${t}. Removing from detectors.`);delete p.mW[t];throw i}}}})));const e=t.filter((t=>t.status==="rejected"));if(e.length>0){p.Rm.error(`Failed to load ${e.length} external diagrams`);for(const t of e){p.Rm.error(t)}throw new Error(`Failed to load ${e.length} external diagrams`)}}),"loadRegisteredDiagrams");var ni="graphics-document document";function oi(t,e){t.attr("role",ni);if(e!==""){t.attr("aria-roledescription",e)}}(0,p.K2)(oi,"setA11yDiagramInfo");function si(t,e,r,i){if(t.insert===void 0){return}if(r){const e=`chart-desc-${i}`;t.attr("aria-describedby",e);t.insert("desc",":first-child").attr("id",e).text(r)}if(e){const r=`chart-title-${i}`;t.attr("aria-labelledby",r);t.insert("title",":first-child").attr("id",r).text(e)}}(0,p.K2)(si,"addSVGa11yTitleDescription");var li=class t{constructor(t,e,r,i,a){this.type=t;this.text=e;this.db=r;this.parser=i;this.renderer=a}static{(0,p.K2)(this,"Diagram")}static async fromText(e,r={}){const i=(0,p.zj)();const a=(0,p.Ch)(e,i);e=(0,d.C4)(e)+"\n";try{(0,p.Gs)(a)}catch{const t=(0,p.J$)(a);if(!t){throw new p.C0(`Diagram ${a} not found.`)}const{id:e,diagram:r}=await t();(0,p.Js)(e,r)}const{db:n,parser:o,renderer:s,init:l}=(0,p.Gs)(a);if(o.parser){o.parser.yy=n}n.clear?.();l?.(i);if(r.title){n.setDiagramTitle?.(r.title)}await o.parse(e);return new t(a,e,n,o,s)}async render(t,e){await this.renderer.draw(this.text,t,e,this)}getParser(){return this.parser}getType(){return this.type}};var ci=[];var hi=(0,p.K2)((()=>{ci.forEach((t=>{t()}));ci=[]}),"attachFunctions");var di=(0,p.K2)((t=>t.replace(/^\s*%%(?!{)[^\n]+\n?/gm,"").trimStart()),"cleanupComments");function ui(t){const e=t.match(p.EJ);if(!e){return{text:t,metadata:{}}}let r=(0,i.H)(e[1],{schema:i.r})??{};r=typeof r==="object"&&!Array.isArray(r)?r:{};const a={};if(r.displayMode){a.displayMode=r.displayMode.toString()}if(r.title){a.title=r.title.toString()}if(r.config){a.config=r.config}return{text:t.slice(e[0].length),metadata:a}}(0,p.K2)(ui,"extractFrontMatter");var fi=(0,p.K2)((t=>t.replace(/\r\n?/g,"\n").replace(/<(\w+)([^>]*)>/g,((t,e,r)=>"<"+e+r.replace(/="([^"]*)"/g,"='$1'")+">"))),"cleanupText");var pi=(0,p.K2)((t=>{const{text:e,metadata:r}=ui(t);const{displayMode:i,title:a,config:n={}}=r;if(i){if(!n.gantt){n.gantt={}}n.gantt.displayMode=i}return{title:a,config:n,text:e}}),"processFrontmatter");var gi=(0,p.K2)((t=>{const e=d._K.detectInit(t)??{};const r=d._K.detectDirective(t,"wrap");if(Array.isArray(r)){e.wrap=r.some((({type:t})=>t==="wrap"))}else if(r?.type==="wrap"){e.wrap=true}return{text:(0,d.vU)(t),directive:e}}),"processDirectives");function mi(t){const e=fi(t);const r=pi(e);const i=gi(r.text);const a=(0,d.$t)(r.config,i.directive);t=di(i.text);return{code:t,title:r.title,config:a}}(0,p.K2)(mi,"preprocessDiagram");function yi(t){const e=(new TextEncoder).encode(t);const r=Array.from(e,(t=>String.fromCodePoint(t))).join("");return btoa(r)}(0,p.K2)(yi,"toBase64");var bi=5e4;var xi="graph TB;a[Maximum text size in diagram exceeded];style a fill:#faa";var Ci="sandbox";var vi="loose";var ki="http://www.w3.org/2000/svg";var wi="http://www.w3.org/1999/xlink";var Si="http://www.w3.org/1999/xhtml";var Ai="100%";var Ti="100%";var Bi="border:0;margin:0;";var Li="margin:0";var Mi="allow-top-navigation-by-user-activation allow-popups";var _i='The "iframe" tag is not supported by your browser.';var Fi=["foreignobject"];var $i=["dominant-baseline"];function Ei(t){const e=mi(t);(0,p.cL)();(0,p.xA)(e.config??{});return e}(0,p.K2)(Ei,"processAndSetConfigs");async function Oi(t,e){ii();try{const{code:e,config:r}=Ei(t);const i=await Hi(e);return{diagramType:i.type,config:r}}catch(r){if(e?.suppressErrors){return false}throw r}}(0,p.K2)(Oi,"parse");var Di=(0,p.K2)(((t,e,r=[])=>`\n.${t} ${e} { ${r.join(" !important; ")} !important; }`),"cssImportantStyles");var Ii=(0,p.K2)(((t,e=new Map)=>{let r="";if(t.themeCSS!==void 0){r+=`\n${t.themeCSS}`}if(t.fontFamily!==void 0){r+=`\n:root { --mermaid-font-family: ${t.fontFamily}}`}if(t.altFontFamily!==void 0){r+=`\n:root { --mermaid-alt-font-family: ${t.altFontFamily}}`}if(e instanceof Map){const i=t.htmlLabels??t.flowchart?.htmlLabels;const a=["> *","span"];const n=["rect","polygon","ellipse","circle","path"];const o=i?a:n;e.forEach((t=>{if(!(0,$t.A)(t.styles)){o.forEach((e=>{r+=Di(t.id,e,t.styles)}))}if(!(0,$t.A)(t.textStyles)){r+=Di(t.id,"tspan",(t?.textStyles||[]).map((t=>t.replace("color","fill"))))}}))}return r}),"createCssStyles");var Ki=(0,p.K2)(((t,e,r,i)=>{const a=Ii(t,r);const n=(0,p.tM)(e,a,t.themeVariables);return J(Tt(`${i}{${n}}`),Q)}),"createUserStyles");var Ri=(0,p.K2)(((t="",e,r)=>{let i=t;if(!r&&!e){i=i.replace(/marker-end="url\([\d+./:=?A-Za-z-]*?#/g,'marker-end="url(#')}i=(0,d.Sm)(i);i=i.replace(/<br>/g,"<br/>");return i}),"cleanUpSvgCode");var Pi=(0,p.K2)(((t="",e)=>{const r=e?.viewBox?.baseVal?.height?e.viewBox.baseVal.height+"px":Ti;const i=yi(`<body style="${Li}">${t}</body>`);return`<iframe style="width:${Ai};height:${r};${Bi}" src="data:text/html;charset=UTF-8;base64,${i}" sandbox="${Mi}">\n  ${_i}\n</iframe>`}),"putIntoIFrame");var zi=(0,p.K2)(((t,e,r,i,a)=>{const n=t.append("div");n.attr("id",r);if(i){n.attr("style",i)}const o=n.append("svg").attr("id",e).attr("width","100%").attr("xmlns",ki);if(a){o.attr("xmlns:xlink",a)}o.append("g");return t}),"appendDivSvgG");function qi(t,e){return t.append("iframe").attr("id",e).attr("style","width: 100%; height: 100%;").attr("sandbox","")}(0,p.K2)(qi,"sandboxedIframe");var Ni=(0,p.K2)(((t,e,r,i)=>{t.getElementById(e)?.remove();t.getElementById(r)?.remove();t.getElementById(i)?.remove()}),"removeExistingElements");var Wi=(0,p.K2)((async function(t,e,r){ii();const i=Ei(e);e=i.code;const a=(0,p.zj)();p.Rm.debug(a);if(e.length>(a?.maxTextSize??bi)){e=xi}const n="#"+t;const o="i"+t;const s="#"+o;const l="d"+t;const c="#"+l;const h=(0,p.K2)((()=>{const t=f?s:c;const e=(0,m.Ltv)(t).node();if(e&&"remove"in e){e.remove()}}),"removeTempElements");let d=(0,m.Ltv)("body");const f=a.securityLevel===Ci;const g=a.securityLevel===vi;const y=a.fontFamily;if(r!==void 0){if(r){r.innerHTML=""}if(f){const t=qi((0,m.Ltv)(r),o);d=(0,m.Ltv)(t.nodes()[0].contentDocument.body);d.node().style.margin=0}else{d=(0,m.Ltv)(r)}zi(d,t,l,`font-family: ${y}`,wi)}else{Ni(document,t,l,o);if(f){const t=qi((0,m.Ltv)("body"),o);d=(0,m.Ltv)(t.nodes()[0].contentDocument.body);d.node().style.margin=0}else{d=(0,m.Ltv)("body")}zi(d,t,l)}let b;let x;try{b=await li.fromText(e,{title:i.title})}catch(F){if(a.suppressErrorRendering){h();throw F}b=await li.fromText("error");x=F}const C=d.select(c).node();const v=b.type;const k=C.firstChild;const w=k.firstChild;const S=b.renderer.getClasses?.(e,b);const A=Ki(a,v,S,n);const T=document.createElement("style");T.innerHTML=A;k.insertBefore(T,w);try{await b.renderer.draw(e,t,u.n.version,b)}catch($){if(a.suppressErrorRendering){h()}else{hr.draw(e,t,u.n.version)}throw $}const B=d.select(`${c} svg`);const L=b.db.getAccTitle?.();const M=b.db.getAccDescription?.();Yi(v,B,L,M);d.select(`[id="${t}"]`).selectAll("foreignobject > *").attr("xmlns",Si);let _=d.select(c).node().innerHTML;p.Rm.debug("config.arrowMarkerAbsolute",a.arrowMarkerAbsolute);_=Ri(_,f,(0,p._3)(a.arrowMarkerAbsolute));if(f){const t=d.select(c+" svg").node();_=Pi(_,t)}else if(!g){_=Ft.A.sanitize(_,{ADD_TAGS:Fi,ADD_ATTR:$i,HTML_INTEGRATION_POINTS:{foreignobject:true}})}hi();if(x){throw x}h();return{diagramType:v,svg:_,bindFunctions:b.db.bindFunctions}}),"render");function ji(t={}){const e=(0,p.hH)({},t);if(e?.fontFamily&&!e.themeVariables?.fontFamily){if(!e.themeVariables){e.themeVariables={}}e.themeVariables.fontFamily=e.fontFamily}(0,p.wZ)(e);if(e?.theme&&e.theme in p.H$){e.themeVariables=p.H$[e.theme].getThemeVariables(e.themeVariables)}else if(e){e.themeVariables=p.H$.default.getThemeVariables(e.themeVariables)}const r=typeof e==="object"?(0,p.UU)(e):(0,p.Q2)();(0,p.He)(r.logLevel);ii()}(0,p.K2)(ji,"initialize");var Hi=(0,p.K2)(((t,e={})=>{const{code:r}=mi(t);return li.fromText(r,e)}),"getDiagramFromText");function Yi(t,e,r,i){oi(e,t);si(e,r,i,e.attr("id"))}(0,p.K2)(Yi,"addA11yInfo");var Ui=Object.freeze({render:Wi,parse:Oi,getDiagramFromText:Hi,initialize:ji,getConfig:p.zj,setConfig:p.Nk,getSiteConfig:p.Q2,updateSiteConfig:p.B6,reset:(0,p.K2)((()=>{(0,p.cL)()}),"reset"),globalReset:(0,p.K2)((()=>{(0,p.cL)(p.sb)}),"globalReset"),defaultConfig:p.sb});(0,p.He)((0,p.zj)().logLevel);(0,p.cL)((0,p.zj)());var Gi=(0,p.K2)(((t,e,r)=>{p.Rm.warn(t);if((0,d.dq)(t)){if(r){r(t.str,t.hash)}e.push({...t,message:t.str,error:t})}else{if(r){r(t)}if(t instanceof Error){e.push({str:t.message,message:t.message,hash:t.name,error:t})}}}),"handleError");var Vi=(0,p.K2)((async function(t={querySelector:".mermaid"}){try{await Xi(t)}catch(e){if((0,d.dq)(e)){p.Rm.error(e.str)}if(sa.parseError){sa.parseError(e)}if(!t.suppressErrors){p.Rm.error("Use the suppressErrors option to suppress these errors");throw e}}}),"run");var Xi=(0,p.K2)((async function({postRenderCallback:t,querySelector:e,nodes:r}={querySelector:".mermaid"}){const i=Ui.getConfig();p.Rm.debug(`${!t?"No ":""}Callback function found`);let a;if(r){a=r}else if(e){a=document.querySelectorAll(e)}else{throw new Error("Nodes and querySelector are both undefined")}p.Rm.debug(`Found ${a.length} diagrams`);if(i?.startOnLoad!==void 0){p.Rm.debug("Start On Load: "+i?.startOnLoad);Ui.updateSiteConfig({startOnLoad:i?.startOnLoad})}const n=new d._K.InitIDGenerator(i.deterministicIds,i.deterministicIDSeed);let o;const s=[];for(const c of Array.from(a)){p.Rm.info("Rendering diagram: "+c.id);if(c.getAttribute("data-processed")){continue}c.setAttribute("data-processed","true");const e=`mermaid-${n.next()}`;o=c.innerHTML;o=(0,g.T)(d._K.entityDecode(o)).trim().replace(/<br\s*\/?>/gi,"<br/>");const r=d._K.detectInit(o);if(r){p.Rm.debug("Detected early reinit: ",r)}try{const{svg:r,bindFunctions:i}=await oa(e,o,c);c.innerHTML=r;if(t){await t(e)}if(i){i(c)}}catch(l){Gi(l,s,sa.parseError)}}if(s.length>0){throw s[0]}}),"runThrowsErrors");var Zi=(0,p.K2)((function(t){Ui.initialize(t)}),"initialize");var Ji=(0,p.K2)((async function(t,e,r){p.Rm.warn("mermaid.init is deprecated. Please use run instead.");if(t){Zi(t)}const i={postRenderCallback:r,querySelector:".mermaid"};if(typeof e==="string"){i.querySelector=e}else if(e){if(e instanceof HTMLElement){i.nodes=[e]}else{i.nodes=e}}await Vi(i)}),"init");var Qi=(0,p.K2)((async(t,{lazyLoad:e=true}={})=>{ii();(0,p.Xd)(...t);if(e===false){await ai()}}),"registerExternalDiagrams");var ta=(0,p.K2)((function(){if(sa.startOnLoad){const{startOnLoad:t}=Ui.getConfig();if(t){sa.run().catch((t=>p.Rm.error("Mermaid failed to initialize",t)))}}}),"contentLoaded");if(typeof document!=="undefined"){window.addEventListener("load",ta,false)}var ea=(0,p.K2)((function(t){sa.parseError=t}),"setParseErrorHandler");var ra=[];var ia=false;var aa=(0,p.K2)((async()=>{if(ia){return}ia=true;while(ra.length>0){const e=ra.shift();if(e){try{await e()}catch(t){p.Rm.error("Error executing queue",t)}}}ia=false}),"executeQueue");var na=(0,p.K2)((async(t,e)=>new Promise(((r,i)=>{const a=(0,p.K2)((()=>new Promise(((a,n)=>{Ui.parse(t,e).then((t=>{a(t);r(t)}),(t=>{p.Rm.error("Error parsing",t);sa.parseError?.(t);n(t);i(t)}))}))),"performCall");ra.push(a);aa().catch(i)}))),"parse");var oa=(0,p.K2)(((t,e,r)=>new Promise(((i,a)=>{const n=(0,p.K2)((()=>new Promise(((n,o)=>{Ui.render(t,e,r).then((t=>{n(t);i(t)}),(t=>{p.Rm.error("Error parsing",t);sa.parseError?.(t);o(t);a(t)}))}))),"performCall");ra.push(n);aa().catch(a)}))),"render");var sa={startOnLoad:true,mermaidAPI:Ui,parse:na,render:oa,init:Ji,run:Vi,registerExternalDiagrams:Qi,registerLayoutLoaders:a.sO,initialize:Zi,parseError:void 0,contentLoaded:ta,setParseErrorHandler:ea,detectType:p.Ch,registerIconPacks:c.pC};var la=sa},52274:(t,e,r)=>{"use strict";r.d(e,{A:()=>lt});function i(t,e,r){if(t&&t.length){const[i,a]=e,n=Math.PI/180*r,o=Math.cos(n),s=Math.sin(n);for(const e of t){const[t,r]=e;e[0]=(t-i)*o-(r-a)*s+i,e[1]=(t-i)*s+(r-a)*o+a}}}function a(t,e){return t[0]===e[0]&&t[1]===e[1]}function n(t,e,r,n=1){const o=r,s=Math.max(e,.1),l=t[0]&&t[0][0]&&"number"==typeof t[0][0]?[t]:t,c=[0,0];if(o)for(const a of l)i(a,c,o);const h=function(t,e,r){const i=[];for(const h of t){const t=[...h];a(t[0],t[t.length-1])||t.push([t[0][0],t[0][1]]),t.length>2&&i.push(t)}const n=[];e=Math.max(e,.1);const o=[];for(const a of i)for(let t=0;t<a.length-1;t++){const e=a[t],r=a[t+1];if(e[1]!==r[1]){const t=Math.min(e[1],r[1]);o.push({ymin:t,ymax:Math.max(e[1],r[1]),x:t===e[1]?e[0]:r[0],islope:(r[0]-e[0])/(r[1]-e[1])})}}if(o.sort(((t,e)=>t.ymin<e.ymin?-1:t.ymin>e.ymin?1:t.x<e.x?-1:t.x>e.x?1:t.ymax===e.ymax?0:(t.ymax-e.ymax)/Math.abs(t.ymax-e.ymax))),!o.length)return n;let s=[],l=o[0].ymin,c=0;for(;s.length||o.length;){if(o.length){let t=-1;for(let e=0;e<o.length&&!(o[e].ymin>l);e++)t=e;o.splice(0,t+1).forEach((t=>{s.push({s:l,edge:t})}))}if(s=s.filter((t=>!(t.edge.ymax<=l))),s.sort(((t,e)=>t.edge.x===e.edge.x?0:(t.edge.x-e.edge.x)/Math.abs(t.edge.x-e.edge.x))),(1!==r||c%e==0)&&s.length>1)for(let t=0;t<s.length;t+=2){const e=t+1;if(e>=s.length)break;const r=s[t].edge,i=s[e].edge;n.push([[Math.round(r.x),l],[Math.round(i.x),l]])}l+=r,s.forEach((t=>{t.edge.x=t.edge.x+r*t.edge.islope})),c++}return n}(l,s,n);if(o){for(const t of l)i(t,c,-o);!function(t,e,r){const a=[];t.forEach((t=>a.push(...t))),i(a,e,r)}(h,c,-o)}return h}function o(t,e){var r;const i=e.hachureAngle+90;let a=e.hachureGap;a<0&&(a=4*e.strokeWidth),a=Math.round(Math.max(a,.1));let o=1;return e.roughness>=1&&((null===(r=e.randomizer)||void 0===r?void 0:r.next())||Math.random())>.7&&(o=a),n(t,a,i,o||1)}class s{constructor(t){this.helper=t}fillPolygons(t,e){return this._fillPolygons(t,e)}_fillPolygons(t,e){const r=o(t,e);return{type:"fillSketch",ops:this.renderLines(r,e)}}renderLines(t,e){const r=[];for(const i of t)r.push(...this.helper.doubleLineOps(i[0][0],i[0][1],i[1][0],i[1][1],e));return r}}function l(t){const e=t[0],r=t[1];return Math.sqrt(Math.pow(e[0]-r[0],2)+Math.pow(e[1]-r[1],2))}class c extends s{fillPolygons(t,e){let r=e.hachureGap;r<0&&(r=4*e.strokeWidth),r=Math.max(r,.1);const i=o(t,Object.assign({},e,{hachureGap:r})),a=Math.PI/180*e.hachureAngle,n=[],s=.5*r*Math.cos(a),c=.5*r*Math.sin(a);for(const[o,h]of i)l([o,h])&&n.push([[o[0]-s,o[1]+c],[...h]],[[o[0]+s,o[1]-c],[...h]]);return{type:"fillSketch",ops:this.renderLines(n,e)}}}class h extends s{fillPolygons(t,e){const r=this._fillPolygons(t,e),i=Object.assign({},e,{hachureAngle:e.hachureAngle+90}),a=this._fillPolygons(t,i);return r.ops=r.ops.concat(a.ops),r}}class d{constructor(t){this.helper=t}fillPolygons(t,e){const r=o(t,e=Object.assign({},e,{hachureAngle:0}));return this.dotsOnLines(r,e)}dotsOnLines(t,e){const r=[];let i=e.hachureGap;i<0&&(i=4*e.strokeWidth),i=Math.max(i,.1);let a=e.fillWeight;a<0&&(a=e.strokeWidth/2);const n=i/4;for(const o of t){const t=l(o),s=t/i,c=Math.ceil(s)-1,h=t-c*i,d=(o[0][0]+o[1][0])/2-i/4,u=Math.min(o[0][1],o[1][1]);for(let o=0;o<c;o++){const t=u+h+o*i,s=d-n+2*Math.random()*n,l=t-n+2*Math.random()*n,c=this.helper.ellipse(s,l,a,a,e);r.push(...c.ops)}}return{type:"fillSketch",ops:r}}}class u{constructor(t){this.helper=t}fillPolygons(t,e){const r=o(t,e);return{type:"fillSketch",ops:this.dashedLine(r,e)}}dashedLine(t,e){const r=e.dashOffset<0?e.hachureGap<0?4*e.strokeWidth:e.hachureGap:e.dashOffset,i=e.dashGap<0?e.hachureGap<0?4*e.strokeWidth:e.hachureGap:e.dashGap,a=[];return t.forEach((t=>{const n=l(t),o=Math.floor(n/(r+i)),s=(n+i-o*(r+i))/2;let c=t[0],h=t[1];c[0]>h[0]&&(c=t[1],h=t[0]);const d=Math.atan((h[1]-c[1])/(h[0]-c[0]));for(let l=0;l<o;l++){const t=l*(r+i),n=t+r,o=[c[0]+t*Math.cos(d)+s*Math.cos(d),c[1]+t*Math.sin(d)+s*Math.sin(d)],h=[c[0]+n*Math.cos(d)+s*Math.cos(d),c[1]+n*Math.sin(d)+s*Math.sin(d)];a.push(...this.helper.doubleLineOps(o[0],o[1],h[0],h[1],e))}})),a}}class f{constructor(t){this.helper=t}fillPolygons(t,e){const r=e.hachureGap<0?4*e.strokeWidth:e.hachureGap,i=e.zigzagOffset<0?r:e.zigzagOffset,a=o(t,e=Object.assign({},e,{hachureGap:r+i}));return{type:"fillSketch",ops:this.zigzagLines(a,i,e)}}zigzagLines(t,e,r){const i=[];return t.forEach((t=>{const a=l(t),n=Math.round(a/(2*e));let o=t[0],s=t[1];o[0]>s[0]&&(o=t[1],s=t[0]);const c=Math.atan((s[1]-o[1])/(s[0]-o[0]));for(let l=0;l<n;l++){const t=2*l*e,a=2*(l+1)*e,n=Math.sqrt(2*Math.pow(e,2)),s=[o[0]+t*Math.cos(c),o[1]+t*Math.sin(c)],h=[o[0]+a*Math.cos(c),o[1]+a*Math.sin(c)],d=[s[0]+n*Math.cos(c+Math.PI/4),s[1]+n*Math.sin(c+Math.PI/4)];i.push(...this.helper.doubleLineOps(s[0],s[1],d[0],d[1],r),...this.helper.doubleLineOps(d[0],d[1],h[0],h[1],r))}})),i}}const p={};class g{constructor(t){this.seed=t}next(){return this.seed?(2**31-1&(this.seed=Math.imul(48271,this.seed)))/2**31:Math.random()}}const m=0,y=1,b=2,x={A:7,a:7,C:6,c:6,H:1,h:1,L:2,l:2,M:2,m:2,Q:4,q:4,S:4,s:4,T:2,t:2,V:1,v:1,Z:0,z:0};function C(t,e){return t.type===e}function v(t){const e=[],r=function(t){const e=new Array;for(;""!==t;)if(t.match(/^([ \t\r\n,]+)/))t=t.substr(RegExp.$1.length);else if(t.match(/^([aAcChHlLmMqQsStTvVzZ])/))e[e.length]={type:m,text:RegExp.$1},t=t.substr(RegExp.$1.length);else{if(!t.match(/^(([-+]?[0-9]+(\.[0-9]*)?|[-+]?\.[0-9]+)([eE][-+]?[0-9]+)?)/))return[];e[e.length]={type:y,text:`${parseFloat(RegExp.$1)}`},t=t.substr(RegExp.$1.length)}return e[e.length]={type:b,text:""},e}(t);let i="BOD",a=0,n=r[a];for(;!C(n,b);){let o=0;const s=[];if("BOD"===i){if("M"!==n.text&&"m"!==n.text)return v("M0,0"+t);a++,o=x[n.text],i=n.text}else C(n,y)?o=x[i]:(a++,o=x[n.text],i=n.text);if(!(a+o<r.length))throw new Error("Path data ended short");for(let t=a;t<a+o;t++){const e=r[t];if(!C(e,y))throw new Error("Param not a number: "+i+","+e.text);s[s.length]=+e.text}if("number"!=typeof x[i])throw new Error("Bad segment: "+i);{const t={key:i,data:s};e.push(t),a+=o,n=r[a],"M"===i&&(i="L"),"m"===i&&(i="l")}}return e}function k(t){let e=0,r=0,i=0,a=0;const n=[];for(const{key:o,data:s}of t)switch(o){case"M":n.push({key:"M",data:[...s]}),[e,r]=s,[i,a]=s;break;case"m":e+=s[0],r+=s[1],n.push({key:"M",data:[e,r]}),i=e,a=r;break;case"L":n.push({key:"L",data:[...s]}),[e,r]=s;break;case"l":e+=s[0],r+=s[1],n.push({key:"L",data:[e,r]});break;case"C":n.push({key:"C",data:[...s]}),e=s[4],r=s[5];break;case"c":{const t=s.map(((t,i)=>i%2?t+r:t+e));n.push({key:"C",data:t}),e=t[4],r=t[5];break}case"Q":n.push({key:"Q",data:[...s]}),e=s[2],r=s[3];break;case"q":{const t=s.map(((t,i)=>i%2?t+r:t+e));n.push({key:"Q",data:t}),e=t[2],r=t[3];break}case"A":n.push({key:"A",data:[...s]}),e=s[5],r=s[6];break;case"a":e+=s[5],r+=s[6],n.push({key:"A",data:[s[0],s[1],s[2],s[3],s[4],e,r]});break;case"H":n.push({key:"H",data:[...s]}),e=s[0];break;case"h":e+=s[0],n.push({key:"H",data:[e]});break;case"V":n.push({key:"V",data:[...s]}),r=s[0];break;case"v":r+=s[0],n.push({key:"V",data:[r]});break;case"S":n.push({key:"S",data:[...s]}),e=s[2],r=s[3];break;case"s":{const t=s.map(((t,i)=>i%2?t+r:t+e));n.push({key:"S",data:t}),e=t[2],r=t[3];break}case"T":n.push({key:"T",data:[...s]}),e=s[0],r=s[1];break;case"t":e+=s[0],r+=s[1],n.push({key:"T",data:[e,r]});break;case"Z":case"z":n.push({key:"Z",data:[]}),e=i,r=a}return n}function w(t){const e=[];let r="",i=0,a=0,n=0,o=0,s=0,l=0;for(const{key:c,data:h}of t){switch(c){case"M":e.push({key:"M",data:[...h]}),[i,a]=h,[n,o]=h;break;case"C":e.push({key:"C",data:[...h]}),i=h[4],a=h[5],s=h[2],l=h[3];break;case"L":e.push({key:"L",data:[...h]}),[i,a]=h;break;case"H":i=h[0],e.push({key:"L",data:[i,a]});break;case"V":a=h[0],e.push({key:"L",data:[i,a]});break;case"S":{let t=0,n=0;"C"===r||"S"===r?(t=i+(i-s),n=a+(a-l)):(t=i,n=a),e.push({key:"C",data:[t,n,...h]}),s=h[0],l=h[1],i=h[2],a=h[3];break}case"T":{const[t,n]=h;let o=0,c=0;"Q"===r||"T"===r?(o=i+(i-s),c=a+(a-l)):(o=i,c=a);const d=i+2*(o-i)/3,u=a+2*(c-a)/3,f=t+2*(o-t)/3,p=n+2*(c-n)/3;e.push({key:"C",data:[d,u,f,p,t,n]}),s=o,l=c,i=t,a=n;break}case"Q":{const[t,r,n,o]=h,c=i+2*(t-i)/3,d=a+2*(r-a)/3,u=n+2*(t-n)/3,f=o+2*(r-o)/3;e.push({key:"C",data:[c,d,u,f,n,o]}),s=t,l=r,i=n,a=o;break}case"A":{const t=Math.abs(h[0]),r=Math.abs(h[1]),n=h[2],o=h[3],s=h[4],l=h[5],c=h[6];if(0===t||0===r)e.push({key:"C",data:[i,a,l,c,l,c]}),i=l,a=c;else if(i!==l||a!==c){A(i,a,l,c,t,r,n,o,s).forEach((function(t){e.push({key:"C",data:t})})),i=l,a=c}break}case"Z":e.push({key:"Z",data:[]}),i=n,a=o}r=c}return e}function S(t,e,r){return[t*Math.cos(r)-e*Math.sin(r),t*Math.sin(r)+e*Math.cos(r)]}function A(t,e,r,i,a,n,o,s,l,c){const h=(d=o,Math.PI*d/180);var d;let u=[],f=0,p=0,g=0,m=0;if(c)[f,p,g,m]=c;else{[t,e]=S(t,e,-h),[r,i]=S(r,i,-h);const o=(t-r)/2,c=(e-i)/2;let d=o*o/(a*a)+c*c/(n*n);d>1&&(d=Math.sqrt(d),a*=d,n*=d);const u=a*a,y=n*n,b=u*y-u*c*c-y*o*o,x=u*c*c+y*o*o,C=(s===l?-1:1)*Math.sqrt(Math.abs(b/x));g=C*a*c/n+(t+r)/2,m=C*-n*o/a+(e+i)/2,f=Math.asin(parseFloat(((e-m)/n).toFixed(9))),p=Math.asin(parseFloat(((i-m)/n).toFixed(9))),t<g&&(f=Math.PI-f),r<g&&(p=Math.PI-p),f<0&&(f=2*Math.PI+f),p<0&&(p=2*Math.PI+p),l&&f>p&&(f-=2*Math.PI),!l&&p>f&&(p-=2*Math.PI)}let y=p-f;if(Math.abs(y)>120*Math.PI/180){const t=p,e=r,s=i;p=l&&p>f?f+120*Math.PI/180*1:f+120*Math.PI/180*-1,u=A(r=g+a*Math.cos(p),i=m+n*Math.sin(p),e,s,a,n,o,0,l,[p,t,g,m])}y=p-f;const b=Math.cos(f),x=Math.sin(f),C=Math.cos(p),v=Math.sin(p),k=Math.tan(y/4),w=4/3*a*k,T=4/3*n*k,B=[t,e],L=[t+w*x,e-T*b],M=[r+w*v,i-T*C],_=[r,i];if(L[0]=2*B[0]-L[0],L[1]=2*B[1]-L[1],c)return[L,M,_].concat(u);{u=[L,M,_].concat(u);const t=[];for(let e=0;e<u.length;e+=3){const r=S(u[e][0],u[e][1],h),i=S(u[e+1][0],u[e+1][1],h),a=S(u[e+2][0],u[e+2][1],h);t.push([r[0],r[1],i[0],i[1],a[0],a[1]])}return t}}const T={randOffset:function(t,e){return z(t,e)},randOffsetWithRange:function(t,e,r){return P(t,e,r)},ellipse:function(t,e,r,i,a){const n=F(r,i,a);return $(t,e,a,n).opset},doubleLineOps:function(t,e,r,i,a){return q(t,e,r,i,a,!0)}};function B(t,e,r,i,a){return{type:"path",ops:q(t,e,r,i,a)}}function L(t,e,r){const i=(t||[]).length;if(i>2){const a=[];for(let e=0;e<i-1;e++)a.push(...q(t[e][0],t[e][1],t[e+1][0],t[e+1][1],r));return e&&a.push(...q(t[i-1][0],t[i-1][1],t[0][0],t[0][1],r)),{type:"path",ops:a}}return 2===i?B(t[0][0],t[0][1],t[1][0],t[1][1],r):{type:"path",ops:[]}}function M(t,e,r,i,a){return function(t,e){return L(t,!0,e)}([[t,e],[t+r,e],[t+r,e+i],[t,e+i]],a)}function _(t,e){if(t.length){const r="number"==typeof t[0][0]?[t]:t,i=W(r[0],1*(1+.2*e.roughness),e),a=e.disableMultiStroke?[]:W(r[0],1.5*(1+.22*e.roughness),K(e));for(let t=1;t<r.length;t++){const n=r[t];if(n.length){const t=W(n,1*(1+.2*e.roughness),e),r=e.disableMultiStroke?[]:W(n,1.5*(1+.22*e.roughness),K(e));for(const e of t)"move"!==e.op&&i.push(e);for(const e of r)"move"!==e.op&&a.push(e)}}return{type:"path",ops:i.concat(a)}}return{type:"path",ops:[]}}function F(t,e,r){const i=Math.sqrt(2*Math.PI*Math.sqrt((Math.pow(t/2,2)+Math.pow(e/2,2))/2)),a=Math.ceil(Math.max(r.curveStepCount,r.curveStepCount/Math.sqrt(200)*i)),n=2*Math.PI/a;let o=Math.abs(t/2),s=Math.abs(e/2);const l=1-r.curveFitting;return o+=z(o*l,r),s+=z(s*l,r),{increment:n,rx:o,ry:s}}function $(t,e,r,i){const[a,n]=H(i.increment,t,e,i.rx,i.ry,1,i.increment*P(.1,P(.4,1,r),r),r);let o=j(a,null,r);if(!r.disableMultiStroke&&0!==r.roughness){const[a]=H(i.increment,t,e,i.rx,i.ry,1.5,0,r),n=j(a,null,r);o=o.concat(n)}return{estimatedPoints:n,opset:{type:"path",ops:o}}}function E(t,e,r,i,a,n,o,s,l){const c=t,h=e;let d=Math.abs(r/2),u=Math.abs(i/2);d+=z(.01*d,l),u+=z(.01*u,l);let f=a,p=n;for(;f<0;)f+=2*Math.PI,p+=2*Math.PI;p-f>2*Math.PI&&(f=0,p=2*Math.PI);const g=2*Math.PI/l.curveStepCount,m=Math.min(g/2,(p-f)/2),y=Y(m,c,h,d,u,f,p,1,l);if(!l.disableMultiStroke){const t=Y(m,c,h,d,u,f,p,1.5,l);y.push(...t)}return o&&(s?y.push(...q(c,h,c+d*Math.cos(f),h+u*Math.sin(f),l),...q(c,h,c+d*Math.cos(p),h+u*Math.sin(p),l)):y.push({op:"lineTo",data:[c,h]},{op:"lineTo",data:[c+d*Math.cos(f),h+u*Math.sin(f)]})),{type:"path",ops:y}}function O(t,e){const r=w(k(v(t))),i=[];let a=[0,0],n=[0,0];for(const{key:o,data:s}of r)switch(o){case"M":n=[s[0],s[1]],a=[s[0],s[1]];break;case"L":i.push(...q(n[0],n[1],s[0],s[1],e)),n=[s[0],s[1]];break;case"C":{const[t,r,a,o,l,c]=s;i.push(...U(t,r,a,o,l,c,n,e)),n=[l,c];break}case"Z":i.push(...q(n[0],n[1],a[0],a[1],e)),n=[a[0],a[1]]}return{type:"path",ops:i}}function D(t,e){const r=[];for(const i of t)if(i.length){const t=e.maxRandomnessOffset||0,a=i.length;if(a>2){r.push({op:"move",data:[i[0][0]+z(t,e),i[0][1]+z(t,e)]});for(let n=1;n<a;n++)r.push({op:"lineTo",data:[i[n][0]+z(t,e),i[n][1]+z(t,e)]})}}return{type:"fillPath",ops:r}}function I(t,e){return function(t,e){let r=t.fillStyle||"hachure";if(!p[r])switch(r){case"zigzag":p[r]||(p[r]=new c(e));break;case"cross-hatch":p[r]||(p[r]=new h(e));break;case"dots":p[r]||(p[r]=new d(e));break;case"dashed":p[r]||(p[r]=new u(e));break;case"zigzag-line":p[r]||(p[r]=new f(e));break;default:r="hachure",p[r]||(p[r]=new s(e))}return p[r]}(e,T).fillPolygons(t,e)}function K(t){const e=Object.assign({},t);return e.randomizer=void 0,t.seed&&(e.seed=t.seed+1),e}function R(t){return t.randomizer||(t.randomizer=new g(t.seed||0)),t.randomizer.next()}function P(t,e,r,i=1){return r.roughness*i*(R(r)*(e-t)+t)}function z(t,e,r=1){return P(-t,t,e,r)}function q(t,e,r,i,a,n=!1){const o=n?a.disableMultiStrokeFill:a.disableMultiStroke,s=N(t,e,r,i,a,!0,!1);if(o)return s;const l=N(t,e,r,i,a,!0,!0);return s.concat(l)}function N(t,e,r,i,a,n,o){const s=Math.pow(t-r,2)+Math.pow(e-i,2),l=Math.sqrt(s);let c=1;c=l<200?1:l>500?.4:-.0016668*l+1.233334;let h=a.maxRandomnessOffset||0;h*h*100>s&&(h=l/10);const d=h/2,u=.2+.2*R(a);let f=a.bowing*a.maxRandomnessOffset*(i-e)/200,p=a.bowing*a.maxRandomnessOffset*(t-r)/200;f=z(f,a,c),p=z(p,a,c);const g=[],m=()=>z(d,a,c),y=()=>z(h,a,c),b=a.preserveVertices;return n&&(o?g.push({op:"move",data:[t+(b?0:m()),e+(b?0:m())]}):g.push({op:"move",data:[t+(b?0:z(h,a,c)),e+(b?0:z(h,a,c))]})),o?g.push({op:"bcurveTo",data:[f+t+(r-t)*u+m(),p+e+(i-e)*u+m(),f+t+2*(r-t)*u+m(),p+e+2*(i-e)*u+m(),r+(b?0:m()),i+(b?0:m())]}):g.push({op:"bcurveTo",data:[f+t+(r-t)*u+y(),p+e+(i-e)*u+y(),f+t+2*(r-t)*u+y(),p+e+2*(i-e)*u+y(),r+(b?0:y()),i+(b?0:y())]}),g}function W(t,e,r){if(!t.length)return[];const i=[];i.push([t[0][0]+z(e,r),t[0][1]+z(e,r)]),i.push([t[0][0]+z(e,r),t[0][1]+z(e,r)]);for(let a=1;a<t.length;a++)i.push([t[a][0]+z(e,r),t[a][1]+z(e,r)]),a===t.length-1&&i.push([t[a][0]+z(e,r),t[a][1]+z(e,r)]);return j(i,null,r)}function j(t,e,r){const i=t.length,a=[];if(i>3){const n=[],o=1-r.curveTightness;a.push({op:"move",data:[t[1][0],t[1][1]]});for(let e=1;e+2<i;e++){const r=t[e];n[0]=[r[0],r[1]],n[1]=[r[0]+(o*t[e+1][0]-o*t[e-1][0])/6,r[1]+(o*t[e+1][1]-o*t[e-1][1])/6],n[2]=[t[e+1][0]+(o*t[e][0]-o*t[e+2][0])/6,t[e+1][1]+(o*t[e][1]-o*t[e+2][1])/6],n[3]=[t[e+1][0],t[e+1][1]],a.push({op:"bcurveTo",data:[n[1][0],n[1][1],n[2][0],n[2][1],n[3][0],n[3][1]]})}if(e&&2===e.length){const t=r.maxRandomnessOffset;a.push({op:"lineTo",data:[e[0]+z(t,r),e[1]+z(t,r)]})}}else 3===i?(a.push({op:"move",data:[t[1][0],t[1][1]]}),a.push({op:"bcurveTo",data:[t[1][0],t[1][1],t[2][0],t[2][1],t[2][0],t[2][1]]})):2===i&&a.push(...N(t[0][0],t[0][1],t[1][0],t[1][1],r,!0,!0));return a}function H(t,e,r,i,a,n,o,s){const l=[],c=[];if(0===s.roughness){t/=4,c.push([e+i*Math.cos(-t),r+a*Math.sin(-t)]);for(let n=0;n<=2*Math.PI;n+=t){const t=[e+i*Math.cos(n),r+a*Math.sin(n)];l.push(t),c.push(t)}c.push([e+i*Math.cos(0),r+a*Math.sin(0)]),c.push([e+i*Math.cos(t),r+a*Math.sin(t)])}else{const h=z(.5,s)-Math.PI/2;c.push([z(n,s)+e+.9*i*Math.cos(h-t),z(n,s)+r+.9*a*Math.sin(h-t)]);const d=2*Math.PI+h-.01;for(let o=h;o<d;o+=t){const t=[z(n,s)+e+i*Math.cos(o),z(n,s)+r+a*Math.sin(o)];l.push(t),c.push(t)}c.push([z(n,s)+e+i*Math.cos(h+2*Math.PI+.5*o),z(n,s)+r+a*Math.sin(h+2*Math.PI+.5*o)]),c.push([z(n,s)+e+.98*i*Math.cos(h+o),z(n,s)+r+.98*a*Math.sin(h+o)]),c.push([z(n,s)+e+.9*i*Math.cos(h+.5*o),z(n,s)+r+.9*a*Math.sin(h+.5*o)])}return[c,l]}function Y(t,e,r,i,a,n,o,s,l){const c=n+z(.1,l),h=[];h.push([z(s,l)+e+.9*i*Math.cos(c-t),z(s,l)+r+.9*a*Math.sin(c-t)]);for(let d=c;d<=o;d+=t)h.push([z(s,l)+e+i*Math.cos(d),z(s,l)+r+a*Math.sin(d)]);return h.push([e+i*Math.cos(o),r+a*Math.sin(o)]),h.push([e+i*Math.cos(o),r+a*Math.sin(o)]),j(h,null,l)}function U(t,e,r,i,a,n,o,s){const l=[],c=[s.maxRandomnessOffset||1,(s.maxRandomnessOffset||1)+.3];let h=[0,0];const d=s.disableMultiStroke?1:2,u=s.preserveVertices;for(let f=0;f<d;f++)0===f?l.push({op:"move",data:[o[0],o[1]]}):l.push({op:"move",data:[o[0]+(u?0:z(c[0],s)),o[1]+(u?0:z(c[0],s))]}),h=u?[a,n]:[a+z(c[f],s),n+z(c[f],s)],l.push({op:"bcurveTo",data:[t+z(c[f],s),e+z(c[f],s),r+z(c[f],s),i+z(c[f],s),h[0],h[1]]});return l}function G(t){return[...t]}function V(t,e=0){const r=t.length;if(r<3)throw new Error("A curve must have at least three points.");const i=[];if(3===r)i.push(G(t[0]),G(t[1]),G(t[2]),G(t[2]));else{const r=[];r.push(t[0],t[0]);for(let e=1;e<t.length;e++)r.push(t[e]),e===t.length-1&&r.push(t[e]);const a=[],n=1-e;i.push(G(r[0]));for(let t=1;t+2<r.length;t++){const e=r[t];a[0]=[e[0],e[1]],a[1]=[e[0]+(n*r[t+1][0]-n*r[t-1][0])/6,e[1]+(n*r[t+1][1]-n*r[t-1][1])/6],a[2]=[r[t+1][0]+(n*r[t][0]-n*r[t+2][0])/6,r[t+1][1]+(n*r[t][1]-n*r[t+2][1])/6],a[3]=[r[t+1][0],r[t+1][1]],i.push(a[1],a[2],a[3])}}return i}function X(t,e){return Math.pow(t[0]-e[0],2)+Math.pow(t[1]-e[1],2)}function Z(t,e,r){const i=X(e,r);if(0===i)return X(t,e);let a=((t[0]-e[0])*(r[0]-e[0])+(t[1]-e[1])*(r[1]-e[1]))/i;return a=Math.max(0,Math.min(1,a)),X(t,J(e,r,a))}function J(t,e,r){return[t[0]+(e[0]-t[0])*r,t[1]+(e[1]-t[1])*r]}function Q(t,e,r,i){const a=i||[];if(function(t,e){const r=t[e+0],i=t[e+1],a=t[e+2],n=t[e+3];let o=3*i[0]-2*r[0]-n[0];o*=o;let s=3*i[1]-2*r[1]-n[1];s*=s;let l=3*a[0]-2*n[0]-r[0];l*=l;let c=3*a[1]-2*n[1]-r[1];return c*=c,o<l&&(o=l),s<c&&(s=c),o+s}(t,e)<r){const r=t[e+0];if(a.length){(n=a[a.length-1],o=r,Math.sqrt(X(n,o)))>1&&a.push(r)}else a.push(r);a.push(t[e+3])}else{const i=.5,n=t[e+0],o=t[e+1],s=t[e+2],l=t[e+3],c=J(n,o,i),h=J(o,s,i),d=J(s,l,i),u=J(c,h,i),f=J(h,d,i),p=J(u,f,i);Q([n,c,u,p],0,r,a),Q([p,f,d,l],0,r,a)}var n,o;return a}function tt(t,e){return et(t,0,t.length,e)}function et(t,e,r,i,a){const n=a||[],o=t[e],s=t[r-1];let l=0,c=1;for(let h=e+1;h<r-1;++h){const e=Z(t[h],o,s);e>l&&(l=e,c=h)}return Math.sqrt(l)>i?(et(t,e,c+1,i,n),et(t,c,r,i,n)):(n.length||n.push(o),n.push(s)),n}function rt(t,e=.15,r){const i=[],a=(t.length-1)/3;for(let n=0;n<a;n++){Q(t,3*n,e,i)}return r&&r>0?et(i,0,i.length,r):i}const it="none";class at{constructor(t){this.defaultOptions={maxRandomnessOffset:2,roughness:1,bowing:1,stroke:"#000",strokeWidth:1,curveTightness:0,curveFitting:.95,curveStepCount:9,fillStyle:"hachure",fillWeight:-1,hachureAngle:-41,hachureGap:-1,dashOffset:-1,dashGap:-1,zigzagOffset:-1,seed:0,disableMultiStroke:!1,disableMultiStrokeFill:!1,preserveVertices:!1,fillShapeRoughnessGain:.8},this.config=t||{},this.config.options&&(this.defaultOptions=this._o(this.config.options))}static newSeed(){return Math.floor(Math.random()*2**31)}_o(t){return t?Object.assign({},this.defaultOptions,t):this.defaultOptions}_d(t,e,r){return{shape:t,sets:e||[],options:r||this.defaultOptions}}line(t,e,r,i,a){const n=this._o(a);return this._d("line",[B(t,e,r,i,n)],n)}rectangle(t,e,r,i,a){const n=this._o(a),o=[],s=M(t,e,r,i,n);if(n.fill){const a=[[t,e],[t+r,e],[t+r,e+i],[t,e+i]];"solid"===n.fillStyle?o.push(D([a],n)):o.push(I([a],n))}return n.stroke!==it&&o.push(s),this._d("rectangle",o,n)}ellipse(t,e,r,i,a){const n=this._o(a),o=[],s=F(r,i,n),l=$(t,e,n,s);if(n.fill)if("solid"===n.fillStyle){const r=$(t,e,n,s).opset;r.type="fillPath",o.push(r)}else o.push(I([l.estimatedPoints],n));return n.stroke!==it&&o.push(l.opset),this._d("ellipse",o,n)}circle(t,e,r,i){const a=this.ellipse(t,e,r,r,i);return a.shape="circle",a}linearPath(t,e){const r=this._o(e);return this._d("linearPath",[L(t,!1,r)],r)}arc(t,e,r,i,a,n,o=!1,s){const l=this._o(s),c=[],h=E(t,e,r,i,a,n,o,!0,l);if(o&&l.fill)if("solid"===l.fillStyle){const o=Object.assign({},l);o.disableMultiStroke=!0;const s=E(t,e,r,i,a,n,!0,!1,o);s.type="fillPath",c.push(s)}else c.push(function(t,e,r,i,a,n,o){const s=t,l=e;let c=Math.abs(r/2),h=Math.abs(i/2);c+=z(.01*c,o),h+=z(.01*h,o);let d=a,u=n;for(;d<0;)d+=2*Math.PI,u+=2*Math.PI;u-d>2*Math.PI&&(d=0,u=2*Math.PI);const f=(u-d)/o.curveStepCount,p=[];for(let g=d;g<=u;g+=f)p.push([s+c*Math.cos(g),l+h*Math.sin(g)]);return p.push([s+c*Math.cos(u),l+h*Math.sin(u)]),p.push([s,l]),I([p],o)}(t,e,r,i,a,n,l));return l.stroke!==it&&c.push(h),this._d("arc",c,l)}curve(t,e){const r=this._o(e),i=[],a=_(t,r);if(r.fill&&r.fill!==it)if("solid"===r.fillStyle){const e=_(t,Object.assign(Object.assign({},r),{disableMultiStroke:!0,roughness:r.roughness?r.roughness+r.fillShapeRoughnessGain:0}));i.push({type:"fillPath",ops:this._mergedShape(e.ops)})}else{const e=[],a=t;if(a.length){const t="number"==typeof a[0][0]?[a]:a;for(const i of t)i.length<3?e.push(...i):3===i.length?e.push(...rt(V([i[0],i[0],i[1],i[2]]),10,(1+r.roughness)/2)):e.push(...rt(V(i),10,(1+r.roughness)/2))}e.length&&i.push(I([e],r))}return r.stroke!==it&&i.push(a),this._d("curve",i,r)}polygon(t,e){const r=this._o(e),i=[],a=L(t,!0,r);return r.fill&&("solid"===r.fillStyle?i.push(D([t],r)):i.push(I([t],r))),r.stroke!==it&&i.push(a),this._d("polygon",i,r)}path(t,e){const r=this._o(e),i=[];if(!t)return this._d("path",i,r);t=(t||"").replace(/\n/g," ").replace(/(-\s)/g,"-").replace("/(ss)/g"," ");const a=r.fill&&"transparent"!==r.fill&&r.fill!==it,n=r.stroke!==it,o=!!(r.simplification&&r.simplification<1),s=function(t,e,r){const i=w(k(v(t))),a=[];let n=[],o=[0,0],s=[];const l=()=>{s.length>=4&&n.push(...rt(s,e)),s=[]},c=()=>{l(),n.length&&(a.push(n),n=[])};for(const{key:d,data:u}of i)switch(d){case"M":c(),o=[u[0],u[1]],n.push(o);break;case"L":l(),n.push([u[0],u[1]]);break;case"C":if(!s.length){const t=n.length?n[n.length-1]:o;s.push([t[0],t[1]])}s.push([u[0],u[1]]),s.push([u[2],u[3]]),s.push([u[4],u[5]]);break;case"Z":l(),n.push([o[0],o[1]])}if(c(),!r)return a;const h=[];for(const d of a){const t=tt(d,r);t.length&&h.push(t)}return h}(t,1,o?4-4*(r.simplification||1):(1+r.roughness)/2),l=O(t,r);if(a)if("solid"===r.fillStyle)if(1===s.length){const e=O(t,Object.assign(Object.assign({},r),{disableMultiStroke:!0,roughness:r.roughness?r.roughness+r.fillShapeRoughnessGain:0}));i.push({type:"fillPath",ops:this._mergedShape(e.ops)})}else i.push(D(s,r));else i.push(I(s,r));return n&&(o?s.forEach((t=>{i.push(L(t,!1,r))})):i.push(l)),this._d("path",i,r)}opsToPath(t,e){let r="";for(const i of t.ops){const t="number"==typeof e&&e>=0?i.data.map((t=>+t.toFixed(e))):i.data;switch(i.op){case"move":r+=`M${t[0]} ${t[1]} `;break;case"bcurveTo":r+=`C${t[0]} ${t[1]}, ${t[2]} ${t[3]}, ${t[4]} ${t[5]} `;break;case"lineTo":r+=`L${t[0]} ${t[1]} `}}return r.trim()}toPaths(t){const e=t.sets||[],r=t.options||this.defaultOptions,i=[];for(const a of e){let t=null;switch(a.type){case"path":t={d:this.opsToPath(a),stroke:r.stroke,strokeWidth:r.strokeWidth,fill:it};break;case"fillPath":t={d:this.opsToPath(a),stroke:it,strokeWidth:0,fill:r.fill||it};break;case"fillSketch":t=this.fillSketch(a,r)}t&&i.push(t)}return i}fillSketch(t,e){let r=e.fillWeight;return r<0&&(r=e.strokeWidth/2),{d:this.opsToPath(t),stroke:e.fill||it,strokeWidth:r,fill:it}}_mergedShape(t){return t.filter(((t,e)=>0===e||"move"!==t.op))}}class nt{constructor(t,e){this.canvas=t,this.ctx=this.canvas.getContext("2d"),this.gen=new at(e)}draw(t){const e=t.sets||[],r=t.options||this.getDefaultOptions(),i=this.ctx,a=t.options.fixedDecimalPlaceDigits;for(const n of e)switch(n.type){case"path":i.save(),i.strokeStyle="none"===r.stroke?"transparent":r.stroke,i.lineWidth=r.strokeWidth,r.strokeLineDash&&i.setLineDash(r.strokeLineDash),r.strokeLineDashOffset&&(i.lineDashOffset=r.strokeLineDashOffset),this._drawToContext(i,n,a),i.restore();break;case"fillPath":{i.save(),i.fillStyle=r.fill||"";const e="curve"===t.shape||"polygon"===t.shape||"path"===t.shape?"evenodd":"nonzero";this._drawToContext(i,n,a,e),i.restore();break}case"fillSketch":this.fillSketch(i,n,r)}}fillSketch(t,e,r){let i=r.fillWeight;i<0&&(i=r.strokeWidth/2),t.save(),r.fillLineDash&&t.setLineDash(r.fillLineDash),r.fillLineDashOffset&&(t.lineDashOffset=r.fillLineDashOffset),t.strokeStyle=r.fill||"",t.lineWidth=i,this._drawToContext(t,e,r.fixedDecimalPlaceDigits),t.restore()}_drawToContext(t,e,r,i="nonzero"){t.beginPath();for(const a of e.ops){const e="number"==typeof r&&r>=0?a.data.map((t=>+t.toFixed(r))):a.data;switch(a.op){case"move":t.moveTo(e[0],e[1]);break;case"bcurveTo":t.bezierCurveTo(e[0],e[1],e[2],e[3],e[4],e[5]);break;case"lineTo":t.lineTo(e[0],e[1])}}"fillPath"===e.type?t.fill(i):t.stroke()}get generator(){return this.gen}getDefaultOptions(){return this.gen.defaultOptions}line(t,e,r,i,a){const n=this.gen.line(t,e,r,i,a);return this.draw(n),n}rectangle(t,e,r,i,a){const n=this.gen.rectangle(t,e,r,i,a);return this.draw(n),n}ellipse(t,e,r,i,a){const n=this.gen.ellipse(t,e,r,i,a);return this.draw(n),n}circle(t,e,r,i){const a=this.gen.circle(t,e,r,i);return this.draw(a),a}linearPath(t,e){const r=this.gen.linearPath(t,e);return this.draw(r),r}polygon(t,e){const r=this.gen.polygon(t,e);return this.draw(r),r}arc(t,e,r,i,a,n,o=!1,s){const l=this.gen.arc(t,e,r,i,a,n,o,s);return this.draw(l),l}curve(t,e){const r=this.gen.curve(t,e);return this.draw(r),r}path(t,e){const r=this.gen.path(t,e);return this.draw(r),r}}const ot="http://www.w3.org/2000/svg";class st{constructor(t,e){this.svg=t,this.gen=new at(e)}draw(t){const e=t.sets||[],r=t.options||this.getDefaultOptions(),i=this.svg.ownerDocument||window.document,a=i.createElementNS(ot,"g"),n=t.options.fixedDecimalPlaceDigits;for(const o of e){let e=null;switch(o.type){case"path":e=i.createElementNS(ot,"path"),e.setAttribute("d",this.opsToPath(o,n)),e.setAttribute("stroke",r.stroke),e.setAttribute("stroke-width",r.strokeWidth+""),e.setAttribute("fill","none"),r.strokeLineDash&&e.setAttribute("stroke-dasharray",r.strokeLineDash.join(" ").trim()),r.strokeLineDashOffset&&e.setAttribute("stroke-dashoffset",`${r.strokeLineDashOffset}`);break;case"fillPath":e=i.createElementNS(ot,"path"),e.setAttribute("d",this.opsToPath(o,n)),e.setAttribute("stroke","none"),e.setAttribute("stroke-width","0"),e.setAttribute("fill",r.fill||""),"curve"!==t.shape&&"polygon"!==t.shape||e.setAttribute("fill-rule","evenodd");break;case"fillSketch":e=this.fillSketch(i,o,r)}e&&a.appendChild(e)}return a}fillSketch(t,e,r){let i=r.fillWeight;i<0&&(i=r.strokeWidth/2);const a=t.createElementNS(ot,"path");return a.setAttribute("d",this.opsToPath(e,r.fixedDecimalPlaceDigits)),a.setAttribute("stroke",r.fill||""),a.setAttribute("stroke-width",i+""),a.setAttribute("fill","none"),r.fillLineDash&&a.setAttribute("stroke-dasharray",r.fillLineDash.join(" ").trim()),r.fillLineDashOffset&&a.setAttribute("stroke-dashoffset",`${r.fillLineDashOffset}`),a}get generator(){return this.gen}getDefaultOptions(){return this.gen.defaultOptions}opsToPath(t,e){return this.gen.opsToPath(t,e)}line(t,e,r,i,a){const n=this.gen.line(t,e,r,i,a);return this.draw(n)}rectangle(t,e,r,i,a){const n=this.gen.rectangle(t,e,r,i,a);return this.draw(n)}ellipse(t,e,r,i,a){const n=this.gen.ellipse(t,e,r,i,a);return this.draw(n)}circle(t,e,r,i){const a=this.gen.circle(t,e,r,i);return this.draw(a)}linearPath(t,e){const r=this.gen.linearPath(t,e);return this.draw(r)}polygon(t,e){const r=this.gen.polygon(t,e);return this.draw(r)}arc(t,e,r,i,a,n,o=!1,s){const l=this.gen.arc(t,e,r,i,a,n,o,s);return this.draw(l)}curve(t,e){const r=this.gen.curve(t,e);return this.draw(r)}path(t,e){const r=this.gen.path(t,e);return this.draw(r)}}var lt={canvas:(t,e)=>new nt(t,e),svg:(t,e)=>new st(t,e),generator:t=>new at(t),newSeed:()=>at.newSeed()}},60513:(t,e,r)=>{"use strict";r.d(e,{T:()=>i});function i(t){var e=[];for(var r=1;r<arguments.length;r++){e[r-1]=arguments[r]}var i=Array.from(typeof t==="string"?[t]:t);i[i.length-1]=i[i.length-1].replace(/\r?\n([\t ]*)$/,"");var a=i.reduce((function(t,e){var r=e.match(/\n([\t ]+|(?!\s).)/g);if(r){return t.concat(r.map((function(t){var e,r;return(r=(e=t.match(/[\t ]/g))===null||e===void 0?void 0:e.length)!==null&&r!==void 0?r:0})))}return t}),[]);if(a.length){var n=new RegExp("\n[\t ]{"+Math.min.apply(Math,a)+"}","g");i=i.map((function(t){return t.replace(n,"\n")}))}i[0]=i[0].replace(/^\r?\n/,"");var o=i[0];e.forEach((function(t,e){var r=o.match(/(?:^|\n)( *)$/);var a=r?r[1]:"";var n=t;if(typeof t==="string"&&t.includes("\n")){n=String(t).split("\n").map((function(t,e){return e===0?t:""+a+t})).join("\n")}o+=n+i[e+1]}));return o}var a=null&&i}}]);