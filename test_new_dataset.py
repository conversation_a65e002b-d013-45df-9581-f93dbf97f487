#!/usr/bin/env python3
"""
测试新数据集格式
"""

import json
import torch
from PIL import Image
from transformers import AutoProcessor

def test_new_dataset():
    print("🧪 测试新数据集格式...")
    
    # 加载处理器
    try:
        processor = AutoProcessor.from_pretrained(
            "Qwen/Qwen2-VL-2B-Instruct",
            trust_remote_code=True
        )
        print("✅ 处理器加载成功")
    except Exception as e:
        print(f"❌ 处理器加载失败: {e}")
        return
    
    # 读取数据
    with open("data/my_dataset/metadata.json", 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print(f"📊 数据集大小: {len(data)}")
    
    # 测试第一条数据
    item = data[0]
    print(f"📋 第一条数据: {item}")
    
    # 加载图片
    image_filename = item['img_url']
    image_path = f"data/my_dataset/images/{image_filename}"
    
    try:
        image = Image.open(image_path).convert('RGB')
        print(f"✅ 图片加载成功: {image.size}")
    except Exception as e:
        print(f"⚠️ 图片加载失败: {e}")
        image = Image.new('RGB', (224, 224), color='red')
        print("🔄 使用测试图片")
    
    # 构建训练文本
    elements = item.get('element', [])
    if elements:
        element = elements[0]
        instruction = element.get('instruction', '点击')
        point = element.get('point', [0.5, 0.5])
        
        # 转换相对坐标到绝对坐标
        img_size = item.get('img_size', [1282, 846])
        abs_x = int(point[0] * img_size[0])
        abs_y = int(point[1] * img_size[1])
        
        text = f"用户: 请点击{instruction}\n助手: 我会帮您点击{instruction}。<click>{abs_x}, {abs_y}</click>"
    else:
        text = "用户: 描述这个图片\n助手: 这是一个界面截图。"
    
    print(f"📝 生成的文本: {text}")
    
    # 测试处理
    try:
        inputs = processor(
            text=text,
            images=image,
            return_tensors="pt",
            padding=True,
            truncation=True,
            max_length=1024
        )
        
        print(f"✅ 数据处理成功")
        print(f"  input_ids shape: {inputs['input_ids'].shape}")
        print(f"  attention_mask shape: {inputs['attention_mask'].shape}")
        if 'pixel_values' in inputs:
            print(f"  pixel_values shape: {inputs['pixel_values'].shape}")
        
        # 解码查看
        decoded = processor.decode(inputs['input_ids'][0], skip_special_tokens=False)
        print(f"  解码文本: {decoded}")
        
    except Exception as e:
        print(f"❌ 数据处理失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_new_dataset()
